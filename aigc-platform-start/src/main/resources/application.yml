logging:
  #日志目录
  path: ./logs
spring:
  main:
    allow-circular-references: true
  jackson:
    default-property-inclusion: NON_NULL
    serialization.FAIL_ON_EMPTY_BEANS: false
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
  security:
    user:
      name: dev
      password: 402945e4-6d95-4f61-bf67-0f6db06a4b12
  datasource:
    # MySQL 数据源 (主数据源)
    mysql:
      url: *******************************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: rds_admin@conrain2023
      # Druid datasource
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        # 初始化大小
        initial-size: 5
        # 最小连接数
        min-idle: 10
        # 最大连接数
        max-active: 20
        # 获取连接时的最大等待时间
        max-wait: 60000
        # 一个连接在池中最小生存的时间，单位是毫秒
        min-evictable-idle-time-millis: 300000
        # 多久才进行一次检测需要关闭的空闲连接，单位是毫秒
        time-between-eviction-runs-millis: 60000
        # 配置扩展插件：stat-监控统计，log4j-日志，wall-防火墙（防止SQL注入），去掉后，监控界面的sql无法统计，监控地址：http://localhost:8080/druid/index.html
        filters: stat,wall,slf4j
        # 检测连接是否有效的 SQL语句，为空时以下三个配置均无效
        validation-query: SELECT 1
        # 申请连接时执行validationQuery检测连接是否有效，默认true，开启后会降低性能
        test-on-borrow: true
        # 归还连接时执行validationQuery检测连接是否有效，默认false，开启后会降低性能
        test-on-return: true
        # 申请连接时如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效，默认false，建议开启，不影响性能
        test-while-idle: true
        timeBetweenLogStatsMillis: 30000 #30s输出一次log
        useGlobalDtaSourceStat: false
        # 是否开启 StatViewServlet
        stat-view-servlet:
          enabled: true
          # 访问监控页面 白名单，默认127.0.0.1
          allow: 127.0.0.1
          login-username: admin
          login-password: admin
        # FilterStat
        filter:
          stat:
            # 是否开启 FilterStat，默认true
            enabled: true
            # 是否开启 慢SQL 记录，默认false
            log-slow-sql: true
            # 慢 SQL 的标准，默认 3000，单位：毫秒
            slow-sql-millis: 5000
            # 合并多个连接池的监控数据，默认false
            merge-sql: false
    
    # PostgreSQL 数据源 (第二数据源)
    pgsql:
      url: ********************************************************************************************************************************
      driver-class-name: org.postgresql.Driver
      username: prod_pg_root
      password: pg_rds@conrain2025
      # Druid datasource
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        # 初始化大小
        initial-size: 5
        # 最小连接数
        min-idle: 10
        # 最大连接数
        max-active: 20
        # 获取连接时的最大等待时间
        max-wait: 60000
        # 一个连接在池中最小生存的时间，单位是毫秒
        min-evictable-idle-time-millis: 300000
        # 多久才进行一次检测需要关闭的空闲连接，单位是毫秒
        time-between-eviction-runs-millis: 60000
        # 配置扩展插件
        filters: stat,wall,slf4j
        validation-query: SELECT 1
        # 申请连接时执行validationQuery检测连接是否有效
        test-on-borrow: true
        # 归还连接时执行validationQuery检测连接是否有效
        test-on-return: false
        # 申请连接时如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
        test-while-idle: true
        # 强制关闭长时间不归还的连接
        remove-abandoned: true
        # 超时时间，单位为秒
        remove-abandoned-timeout: 1800
        # 关闭abanded连接时输出错误日志
        log-abandoned: true
  redis:
    host: r-8vbrffssyq7s2u78jc.redis.zhangbei.rds.aliyuncs.com
    jedis:
      pool:
        max-active: 2000
        max-idle: 1500
        max-wait: -1
        min-idle: 2
    password: r-8vbrffssyq7s2u78jc:Tair-prod@conrain
    port: 6379
    timeout: 2000
  schedulerx2:
    appKey: sEicHdl7D7toUr1H5Ria0y7g
    endpoint: addr-cn-zhangjiakou-internal.edas.aliyun.com
    groupId: aigc-platform
    namespace: 0f5d26ad-1023-42d7-934a-c6e9e7736678

app:
  custom:
    tenant: MuseGate
    env: PROD
    domain: https://musegate.tech

aliyun:
  sts:
    endpoint: sts-vpc.cn-zhangjiakou.aliyuncs.com
  oss:
    endpoint: https://oss-cn-zhangjiakou-internal.aliyuncs.com
    internetEndpoint: https://oss-cn-zhangjiakou.aliyuncs.com
    bucket: aigc-platform-online
    region: oss-cn-zhangjiakou
    accessKeyId: LTAI5tGtrXAPAtQoPoBw49Rh
    accessKeySecret: ******************************
  sms:
    accessKeyId: LTAI5tDoz5LzRafiN33aKkwj
    accessKeySecret: ******************************
    regionId: cn-zhangjiakou
    endpoint: dysmsapi.aliyuncs.com
    #    短信模板绑定的签名要和这里一致
    signName: 霖润智能
    mock: false
  security:
    accessKeyId: LTAI5tKiqrJPS3ih4ynvheHq
    accessKeySecret: ******************************
    endpoint: green-cip.cn-hangzhou.aliyuncs.com
  api:
    accessKeyId: LTAI5tKZe29qbGtf77swiAfJ
    accessKeySecret: ******************************
  ice:
    endpoint: ice.cn-hangzhou.aliyuncs.com
    ossUrl: https://musegate-hz-media.oss-cn-hangzhou.aliyuncs.com/
    callback: https://musegate.tech/media/notify
  imm:
    endpoint: imm-vpc.cn-zhangjiakou.aliyuncs.com

#三方支付
zlpay:
  url: https://pay.zlinepay.com/payGateway/api
  merchantNo: M230925215
  appId: 0646eb20348a4c8ebbc0f16d6f3b9902
  merchantPriKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALbwuc5TcGVgdpc3UDKFgffp+saSQ3lzujUAK4UP05EOrvecQPIP2OBIg4wRmGvqpqr5OGkZ1E63dDp1sAwwPYE4uYZsEL3bG2pwdcI1En0so8adYH5myN3FDTbYwfsWxmbNM1E+I6U9kQSdZBgqE+5NM+LSDDjC9m39fjrD6k0pAgMBAAECgYBprgh4M2VTevUnB7IRbWSWNZlDysylxX/FDFMXq/vY6TWUHrS54QO1FM7BRIzbkpMzY4L93Aj0bC/9cW0ArYFpQVC73I1SDObFDoFHKHnqYw7dR+Zhxn1qud2orFf36Su9OhHm/w7xaPMFkiyKujzqxbD/d8+XX6atPi7ih4HazQJBAOT4Fxf9Ij7FZps4EL7FKT3cqxwHTQ0pJKmo5XQ3hUeMkHqIB71afRyjKG+KS9lf1Uufheaw6jjx3gcSZ4fiKPMCQQDMiY2WDTGHKmTfIaWhYb/0bBpiZr02+zJw8q/Q/AE9VTTxpeQBuQNKVFxWMRM1dksmByVMCBfcSje9l9H5YHhzAkEAhUhZARDLz2hPKwl+zd2s/SKnxAAH/SI3Aj+bmYP8JEMRkxH+F15GN9ZN4mfJcTaUzZmFc10GarF4QfuXooSmuwJANKWvWRE6T2VdI4YAoSKNCzgMLontaEqOOD5vfOZw1z2obA7cOolJzIccYV8uZ5N0UmyCcCihI6XC5T0/4eWv+wJBALIg1oim4tQpfOs4YwDU7++LbBJYE+mYakCQYcZ4hjGMr8AwdTwBkCcL5mbSqAGY8nM8BtVT2tGRGDFZYmzGmd4=
  platPubKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAudc1lfbldbQTSpy1slQFL/Di5+wADyBmKsMxVhNemmg4M/xNEbrJXdaNI1WC4oy/OFYSGlg0MOOjwgFii++uKQ8wkjMDIAH4TAy/REazemq9zxt8JzZoJg2Bh0edUkN9bHF1DkJyTvLKLWQwaimW2GvakWcrsZrJuMmv2zxJW1C6eMNyXmbF+KKwmB0eWB/itZR6skQ2svip3hySHpS/RTXrqBWwc8Fvh9Vunm9DnAXV9dYEOrvCKMX8p4OoYk9e4wbrEKvjMFFHfwgZdOL07tuQK8YMP0zBfyFLWV6t9Ii3OpXJlLGQxErNIEWEXR1EhaLP/akCnGnDnGwoureFZQIDAQAB
  notifyUrl: https://kdd.conrain.cn/notify/zlpay/result

nacos:
  config:
    access-key: LTAI5tCGkdjPzZsQfC2Yj3bX
    endpoint: addr-cn-zhangjiakou-internal.edas.aliyun.com
    namespace: 177a2206-d554-4c98-810c-7ecac7705e1c
    secret-key: ******************************

llm:
  service.url: http://alb-aq8vhhwqe8jmgp575a.cn-zhangjiakou.alb.aliyuncs.com:8089

comfyui:
  service:
    url: http://*************:11175
  ws:
    url: ws://*************:11175
  output:
    path: /home/<USER>/aigc/ComfyUI/output/
  input:
    path: /home/<USER>/aigc/ComfyUI/input/
  lora:
    url: http://lq.lianqiai.cn:20318
    path: /home/<USER>/aigc/ComfyUI/models/loras/
  root:
    path: /home/<USER>/aigc/ComfyUI/
  file:
    4090url: https://aigc-http-abcd.conrain.cn:11171
    a800url: http://lq.lianqiai.cn:20310
    embedWorkflow: false
    fileType: jpg
    needSyncLoraFile: true

ai:
  qwen:
    service: http://lq.lianqiai.cn:20310/
    sec: none
    timeout: 10
    model: QWen/QWen-14B-Chat
  gpt:
    service: https://api.openai.com/
    sec: ***************************************************
    timeout: 20
    model: gpt-4-1106-preview
  imageAnalysis:
    baseUrl: http://123.181.192.135:18815/
    apiKey: sk-0amBWPVUuFQiw3HM90M0FWJ5q8juA_zLMIKy_V46xj8
  styleImage:
    url: http://123.181.192.135:18820/process_image_data
    apiKey: p9J!k@8L$mQ#2xW7

gpt:
  providers:
    - id: azure-openai-4o
      enabled: false
      type: azure-openai
      api-key: ********************************
      api-version: 2024-08-01-preview
      endpoint: https://conrain-sweden.openai.azure.com
      deployment-id: gpt-4o
      model: gpt-4o
    - id: azure-openai-4.1
      enabled: true
      type: azure-openai
      api-key: 45kfGqoR8P3jcqSUrFOYdVsq7mizFSLx74kcWXihUxTZPBXyarHeJQQJ99BGACHYHv6XJ3w3AAABACOG74BV
      api-version: 2025-01-01-preview
      endpoint: https://conrain0703.openai.azure.com
      deployment-id: gpt-4.1
      model: gpt-4.1
    - id: azure-openai-4.1-mini
      enabled: true
      type: azure-openai
      api-key: 45kfGqoR8P3jcqSUrFOYdVsq7mizFSLx74kcWXihUxTZPBXyarHeJQQJ99BGACHYHv6XJ3w3AAABACOG74BV
      api-version: 2025-01-01-preview
      endpoint: https://conrain0703.openai.azure.com
      deployment-id: gpt-4.1-mini
      model: gpt-4.1-mini

tencent:
  cloud:
    appId: **********
    secretId: AKID53wiIm491hK5mP5f4VwGQVcYorgGdkom
    secretKey: yjR2VwQsGhB8ziSDzdO1SfjjbKF1d4jh
    region: ap-chongqing


# 微信支付相关参数
wx:
  pay:
    # 商户号
    mchId: 1661695453
    certSerialNo: 3E87DB81E6CC0DCB4894CD8222A11761A405238C
    # 商户私钥文件
    privateKeyPath: classpath:apiclient_key.pem
    # 商户公钥文件
    privateCertPath: classpath:apiclient_cert.pem
    # APIv3密钥
    apiV3Key: kyj9lNb2QD1qA1Wn7hcPM1BXhxOdJPCv
    # 接收结果通知地址
    notifyUrl: https://musegate.tech/wx/pay/notify

# 企查查相关配置
qichacha:
  # 企查查模糊查询企业信息接口
  fuzzySearchUrl: https://api.qichacha.com/FuzzySearch/GetList
  appkey: 6d81fc8fee244bb2a007a8d61e909e2c
  secretkey: 9A14597794A52355CCEB2A7C70431AA4


rocketmq:
  enabled: true
  name-server: *************:9877;*************:9878
  producer:
    group: aigc-platform-prod-producer-group
  consumer:
    group: aigc-platform-prod-consumer-group


alipay:
  privateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCGiF8yjig9/8O5PAOegG0XLHpkgDeEHUcEMA4xg0WXQ/JSHQRn98Zfba8HmmdNhFX3oYPveoTkpjKsJPpSjKKGjF47Ffg3g2aJ0AprGFJh2v6TFJcbvrPhRoTH3coRLHTK7PKtLKpc7T5iZ/znhgg0ndir2jbrpSngbfdporyhLqx9PRloTaQjHUpN/oFyQmQ4RRekhXSwdV4A9220LfVb121+VclkY61mc6B/51c6tO0ZATKo/Z8lTdipl7F/rOk6Oq82Rssxdhi1hrsj/HjS9VFvGlKe9Nng82NxDgt30MOPOQNyIiCpmQq8F951f7CoQ168DkKh7aLBEpgxnCjnAgMBAAECggEAKlctLuoSvECMxvWeZfohW1PCSXMZBdl5ClZdgTPBaTQXRcSgKGCQ7GPG3Gu562mk7MTGk05KGPQ9qnQQ3H4qgJIY7huh/Ua7tM/nF4GWG/oieQPX8wF394SMMaMZ/4GwWFVuoxYVoB2CrtHvhKZoWrb7BEsvUcavgVTas0Jmf4uKJeNMg1QcVGRlMtd9JlsJk24RCJIZ0luqiFcCFxXwfUWi2usLgVtr5Ntf+saoztK73wLM1PKJ1meuBEE/+GW3xH1QLDis6yXb/X/hEQv0OjzJ9V6VXMIMmTXPaHUah+mhNmt3JzrWtF2HeDpnVdRkqyXlnaD0q+zBqxnF4wNoYQKBgQDxBQ1ZybzFLtSHrAL5XID8kD5Q+8ixO3wGz/CRpO9cOKE040GylMMKbjP6qouPJ5mJOupAarL7xFLF2WDjRXXq02ECTxDLfyIszbQJVQfZrmJ++RNRtbRYaWkEYLGVlnIG001GsHgbFVEWu/bTAF0Wa8+yhow7AG8SELrHW56m6QKBgQCO5PfK20AgNS/98cezgJ3I4eS2ioN97AG+mq3pz3LuMOmdmpVQBe3FZVSDefobqQTd804Uhu58PvqrzGzeC0dANH7qBfTGSHkEkBNAlDyEp4Cf2EuH6DEfmVHRGP5FBBhzAk+BpOtOpK+qc+4dASpQ9tdNijJxg9aErR9644cPTwKBgE5r4mgwp+dQ0X5DjcQtQRrspBodkrN4M2bUCeaZz/glxO48c4fzt3KRYQBStY4iyGZKcSX8kbgxrO0LSecpFrENjib+KuzLtQb293mB3UgK4amGya5wfPm3cqAmBiIyDXADGMXDbZECYpwPFMSa/a7p7W6dMMuR3JH4N43eVcThAoGAC5c/ElFejj5COlMWY9UW92h7aOMrb1dpfNCHMuQE6pVFHeegHPGeK9jgVhna08uN/YLy3m9jgOiK5MycOBaqHOclNE5vYHYYeupE5YQgtJHxuQWOeuO2uF1zaYaheW+vnASf9mAuRh7NsQHeWrPxfsfHBx8ioFqVWAXC+/Dgz7kCgYA2gf1e8HsL6A9W+kBoLCSJQ23TWFyO4FvUYotPrJOZ2Ym3SDLHhhFITOLzpzQBzt3XIDitSisQIk68HMxIuoBlxoL5TLwgMPw7dQ1+pxXPCFt904VH8vMoCGdEaDR24VSV3eLKV7SJn59XQQ0yADFZPbHBZ6VLFnbX7ewk2ZUuSw==
  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4d9pOLxN4zFkFMJvvbM9xzBy6U2Ms8rRGkky25pWyR1KYeqUPxiHgSAJ8J9Z8pimww9FXj+DesSRONBR5IvsQ769cLksevEUoeDm9Eb69wJcWrIZgyKWffh0ijbu1cthIKinEywkrrKcNKD+Ln+71VRsFwgENg2n7gG0A0vDT1CVsTk50Up8ZyxmK7poq6Pw/nMOPmbFIX7mkiUgQ2QTHQBT+KU0GP8tXLAKHTucZ3LyxfIS1D4ei/Hj0LPM5HgiS1ZQ/KnOsJNpq1Xz7TkL088tBKuN0fyHzId+5vF2AGtRdrIcPH+unFVEdk8u+AG0o+0R5Z1Nub51OhQVKHOinQIDAQAB
  appId: 2021004183638571
  notifyUrl: https://musegate.tech/alipay/notify

image-gallery:
  api-token: qxDz3QqVSMZQ8Fg7HYWrcr9BprHAE8x9

taobao:
  # open url
  openapi:
    url: https://eco.taobao.com/router/rest
  # huiwa appKey And AppSecret
  huiwa:
    app-key: 35121002
    app-secret: 529d4ce07083e8761ea060fd267ab1c5