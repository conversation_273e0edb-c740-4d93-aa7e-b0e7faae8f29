-- 权限初始化
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT';
update permission set allowed_sub = false where action like '/user/sub/%';
update permission set `config`  = 'NONE' where action like '/login/%';
update permission set `config`  = 'NONE' where action like '/sms/%';
update permission set memo = '{"actionType":"OPERATE"}';
update permission set memo = '{"actionType":"QUERY"}' where action like '%/get%' or action like '%/query%' or config = 'NONE' or action = '/user/myInfo' or action like '/file%';
update permission set `config` = 'ADMIN' where action like '/materialModel/queryListWithBlogs';
update permission set `config` = 'ADMIN' where action like '/element/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/getConfig';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/file/upload';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/file/uploadPro';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/getSceneTypes';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/getMerchantRecentSceneElements';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/materialModel/updateById';
update permission set `config` = 'ADMIN' where action like '/file/uploadLora';
update permission set `config` = 'ADMIN' where action like '/user/create';
update permission set `config` = 'ADMIN' where action like '/user/update/back';
update permission set `config` = 'ADMIN' where action like '/point/topupByImage';
update permission set `config` = 'ADMIN' where action like '/tags/%';
update permission set `config` = 'ADMIN' where action like '/materialModel/add/system';
update permission set `config` = 'ADMIN' where action like '/materialModel/deleteById';
update permission set `config` = 'ADMIN,OPERATOR' where action like '/creative/fetchPrompt';
update permission set `config` = 'ADMIN,OPERATOR' where action like '/materialModel/getTrainDetail';
update permission set `config` = 'ADMIN,OPERATOR' where action like '/materialModel/confirmTrainLora';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/queryModels4HistoryTasks';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/imageLike';
update permission set `config` = 'ADMIN,OPERATOR' where action like '/creative/fetchWorkflow';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/materialInfo/checkMaterialNameExists';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/wx/pay/qrcode';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/wx/pay/query';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/fetchZipUrl';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/orderInfo/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/point/predict';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/getFaceTypes';
update permission set `config` = 'ADMIN' where action like '/element/editElementType';

update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/userPointLog/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/orderInfo/queryMerchantTopupByPage';
update permission set `config` = 'ADMIN' where action like '/user/allMaster';

update permission set `config` = 'ADMIN,OPERATOR' where action like '/materialModel/updateLabelText';


update creative_batch_elements set element_key = 'FACE' where element_id in (select id from creative_element where config_key = 'FACE');
update creative_batch_elements set element_key = 'SCENE' where element_id in (select id from creative_element where config_key = 'SCENE');
UPDATE creative_batch_elements
JOIN creative_batch ON creative_batch_elements.batch_id = creative_batch.id
SET creative_batch_elements.operator_id = creative_batch.operator_id
WHERE creative_batch_elements.operator_id is null;

-- 初始化系统配置
insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('MAX_REFRESH_USER_INTERVAL','300000','300000',now(),0,'session用户刷新最大间隔'),
('CAPTCHA_SMS_TEMPLATE','SMS_464111496','SMS_464111496',now(),0,'短信验证码模板'),
('CREATIVE_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"1":{"inputs":{"seed":"${seed}","steps":20,"cfg":"${lora.extInfo.cfg}","sampler_name":"dpmpp_2m_sde_gpu","scheduler":"karras","denoise":1,"model":["135",0],"positive":["205",0],"negative":["122",0],"latent_image":["93",0]},"class_type":"KSampler","_meta":{"title":"KSampler"}},"2":{"inputs":{"ckpt_name":"sdxl/zavychromaxl_v60.safetensors"},"class_type":"CheckpointLoaderSimple","_meta":{"title":"Load Checkpoint"}},"6":{"inputs":{"samples":["1",0],"vae":["2",2]},"class_type":"VAEDecode","_meta":{"title":"VAE Decode"}},"93":{"inputs":{"width":"${width}","height":"${height}","batch_size":"${imageNum}"},"class_type":"EmptyLatentImage","_meta":{"title":"Empty Latent Image"}},"122":{"inputs":{"text":["171",0],"token_normalization":"none","weight_interpretation":"A1111","clip":["135",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"135":{"inputs":{"lora_name":"${lora.loraName}","strength_model":1,"strength_clip":1,"model":["2",0],"clip":["2",1]},"class_type":"LoraLoader","_meta":{"title":"Load LoRA"}},"141":{"inputs":{"model_name":"bbox/face_yolov8m.pt"},"class_type":"UltralyticsDetectorProvider","_meta":{"title":"UltralyticsDetectorProvider"}},"142":{"inputs":{"model_name":"sam_vit_b_01ec64.pth","device_mode":"Prefer GPU"},"class_type":"SAMLoader","_meta":{"title":"SAMLoader (Impact)"}},"144":{"inputs":{"text":"realistic, ${FACE.extTags}","token_normalization":"none","weight_interpretation":"A1111","clip":["215",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"145":{"inputs":{"text":"EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,","token_normalization":"none","weight_interpretation":"A1111","clip":["215",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"152":{"inputs":{"image":"${FACE.extInfo.faceImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"Load Image"}},"171":{"inputs":{"text":"(Breasts exposed:1.2), (Butt naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, worst quality, mannequin, blurry, low quality, bad quality, NSFW, text, watermark, bad hands, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}","text_b":"","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"172":{"inputs":{"prompts":"${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\\n\\n\\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}realistic hands, well-defined hands, hands in natural positions, well-proportioned hands, natural looking hands, smooth and realistic hands, hands with clear lines and structure.\\n\\n\\n${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.lens}${SCENE.extInfo.style}","seed":"${promptSeed}"},"class_type":"ConrainRandomPrompts","_meta":{"title":"conrain random prompts"}},"177":{"inputs":{"text":"${outputPath}","text_b":"${fileNamePrefix}","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"179":{"inputs":{"text":["185",0],"path":["232",0],"filename_prefix":["177",1],"filename_delimiter":"_","filename_number_padding":4},"class_type":"Save Text File","_meta":{"title":"Save Text File"}},"185":{"inputs":{"delimiter":" ","clean_whitespace":"true","text_a":["201",0],"text_b":["172",0],"text_c":["200",0],"text_d":["171",0]},"class_type":"Text Concatenate","_meta":{"title":"Text Concatenate"}},"200":{"inputs":{"string":"【negative】:"},"class_type":"String to Text","_meta":{"title":"String to Text"}},"201":{"inputs":{"string":"【positive】:"},"class_type":"String to Text","_meta":{"title":"String to Text"}},"205":{"inputs":{"text":["172",0],"token_normalization":"none","weight_interpretation":"A1111","clip":["135",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"209":{"inputs":{"swap_model":"inswapper_128.onnx","facedetection_model":"retinaface_resnet50","face_restore_model":"GFPGANv1.4.pth","parse_model":"parsenet"},"class_type":"LoadConrainReactorModels","_meta":{"title":"Load ConrainReactor Models"}},"210":{"inputs":{"enabled":true,"face_restore_visibility":"${FACE.extInfo.faceRestoreVisibility}","codeformer_weight":0.5,"detect_gender_input":"no","detect_gender_source":"no","input_faces_index":"0","source_faces_index":"0","console_log_level":1,"keep_largest":"yes","input_image":["211",0],"swap_model":["209",0],"facedetection":["209",1],"face_restore_model":["209",2],"faceparse_model":["209",3],"source_image":["152",0]},"class_type":"ConrainReActorFaceSwap","_meta":{"title":"ConrainReactor Fast Face Swap"}},"211":{"inputs":{"guide_size":384,"guide_size_for":true,"max_size":512,"seed":"${faceSeed}","steps":8,"cfg":"${faceCfg}","sampler_name":"euler","scheduler":"normal","denoise":0.4,"feather":5,"noise_mask":true,"force_inpaint":true,"bbox_threshold":0.5,"bbox_dilation":500,"bbox_crop_factor":3,"sam_detection_hint":"center-1","sam_dilation":0,"sam_threshold":0.93,"sam_bbox_expansion":0,"sam_mask_hint_threshold":0.7,"sam_mask_hint_use_negative":"False","drop_size":10,"wildcard":"","cycle":1,"top_k":1,"inpaint_model":0,"noise_mask_feather":0,"image":["6",0],"model":["215",0],"clip":["215",1],"vae":["215",2],"positive":["144",0],"negative":["145",0],"bbox_detector":["141",0],"sam_model_opt":["142",0]},"class_type":"FaceDetailer","_meta":{"title":"FaceDetailer"}},"215":{"inputs":{"ckpt_name":"真实系：majicmixRealistic_v7.safetensors"},"class_type":"CheckpointLoaderSimple","_meta":{"title":"Load Checkpoint"}},"216":{"inputs":{"output_path":["177",0],"filename_prefix":["177",1],"extension":"${fileType}","dpi":100,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"${embedWorkflow}","use_time_str":"true","output_as_root":"true","images":["210",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"232":{"inputs":{"delimiter":"/","clean_whitespace":"false","text_a":["233",0],"text_b":["177",0]},"class_type":"Text Concatenate","_meta":{"title":"Text Concatenate"}},"233":{"inputs":{"string":"output"},"class_type":"String to Text","_meta":{"title":"String to Text"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":234,"last_link_id":395,"nodes":[{"id":205,"type":"BNK_CLIPTextEncodeAdvanced","pos":[2018,-967],"size":{"0":226.8000030517578,"1":160},"flags":{},"order":19,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":329,"label":"clip"},{"name":"text","type":"STRING","link":330,"widget":{"name":"text"},"label":"text","slot_index":1}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[331],"shape":3,"label":"CONDITIONING","slot_index":0}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["","none","A1111"],"color":"#322","bgcolor":"#533"},{"id":122,"type":"BNK_CLIPTextEncodeAdvanced","pos":[2024,-699],"size":{"0":226.8000030517578,"1":160},"flags":{},"order":18,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":290,"label":"clip"},{"name":"text","type":"STRING","link":328,"widget":{"name":"text"},"label":"text"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[211],"shape":3,"label":"CONDITIONING","slot_index":0}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["(Breasts exposed:1.2), (Butt naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, worst quality, mannequin, blurry, low quality, bad quality, NSFW, text, watermark, bad hands, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}","none","A1111"],"color":"#322","bgcolor":"#533"},{"id":171,"type":"Text String","pos":[1539,-519],"size":{"0":315,"1":190},"flags":{},"order":0,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[315,328],"shape":3,"label":"STRING","slot_index":0},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["(Breasts exposed:1.2), (Butt naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, worst quality, mannequin, blurry, low quality, bad quality, NSFW, text, watermark, bad hands, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}","","",""]},{"id":200,"type":"String to Text","pos":[4528,-376],"size":{"0":315,"1":58},"flags":{"collapsed":true},"order":1,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[318],"shape":3,"slot_index":0,"label":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["【negative】:"]},{"id":201,"type":"String to Text","pos":[4532,-440],"size":{"0":315,"1":58},"flags":{"collapsed":true},"order":2,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[319],"shape":3,"slot_index":0,"label":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["【positive】:"]},{"id":152,"type":"LoadImage","pos":[4485,-1083],"size":{"0":320,"1":314},"flags":{},"order":3,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[346],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK"}],"properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${FACE.extInfo.faceImage}","image"]},{"id":142,"type":"SAMLoader","pos":[3330,-410],"size":{"0":315,"1":82},"flags":{},"order":4,"mode":0,"outputs":[{"name":"SAM_MODEL","type":"SAM_MODEL","links":[364],"shape":3,"label":"SAM_MODEL","slot_index":0}],"properties":{"Node name for S&R":"SAMLoader"},"widgets_values":["sam_vit_b_01ec64.pth","Prefer GPU"]},{"id":141,"type":"UltralyticsDetectorProvider","pos":[3320,-570],"size":{"0":315,"1":78},"flags":{},"order":5,"mode":0,"outputs":[{"name":"BBOX_DETECTOR","type":"BBOX_DETECTOR","links":[355],"shape":3,"label":"BBOX_DETECTOR","slot_index":0},{"name":"SEGM_DETECTOR","type":"SEGM_DETECTOR","links":[],"shape":3,"label":"SEGM_DETECTOR","slot_index":1}],"properties":{"Node name for S&R":"UltralyticsDetectorProvider"},"widgets_values":["bbox/face_yolov8m.pt"]},{"id":2,"type":"CheckpointLoaderSimple","pos":[1064,-1110],"size":{"0":315,"1":98},"flags":{},"order":6,"mode":0,"outputs":[{"name":"MODEL","type":"MODEL","links":[236],"slot_index":0,"label":"MODEL"},{"name":"CLIP","type":"CLIP","links":[237],"slot_index":1,"label":"CLIP"},{"name":"VAE","type":"VAE","links":[8],"slot_index":2,"label":"VAE"}],"properties":{"Node name for S&R":"CheckpointLoaderSimple"},"widgets_values":["sdxl/zavychromaxl_v60.safetensors"]},{"id":6,"type":"VAEDecode","pos":[2899,-1032],"size":{"0":210,"1":46},"flags":{"collapsed":false},"order":22,"mode":0,"inputs":[{"name":"samples","type":"LATENT","link":213,"label":"samples"},{"name":"vae","type":"VAE","link":8,"label":"vae"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[361],"slot_index":0,"label":"IMAGE"}],"properties":{"Node name for S&R":"VAEDecode"}},{"id":211,"type":"FaceDetailer","pos":[3875,-1377],"size":{"0":506.4000244140625,"1":904},"flags":{},"order":23,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":361,"label":"image"},{"name":"model","type":"MODEL","link":368,"label":"model"},{"name":"clip","type":"CLIP","link":369,"label":"clip"},{"name":"vae","type":"VAE","link":370,"label":"vae"},{"name":"positive","type":"CONDITIONING","link":352,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":353,"label":"negative","slot_index":5},{"name":"bbox_detector","type":"BBOX_DETECTOR","link":355,"label":"bbox_detector","slot_index":6},{"name":"sam_model_opt","type":"SAM_MODEL","link":364,"label":"sam_model_opt","slot_index":7},{"name":"segm_detector_opt","type":"SEGM_DETECTOR","link":null,"label":"segm_detector_opt","slot_index":8},{"name":"detailer_hook","type":"DETAILER_HOOK","link":null,"label":"detailer_hook"}],"outputs":[{"name":"image","type":"IMAGE","links":[362],"shape":3,"label":"image","slot_index":0},{"name":"cropped_refined","type":"IMAGE","links":[],"shape":6,"label":"cropped_refined","slot_index":1},{"name":"cropped_enhanced_alpha","type":"IMAGE","links":[],"shape":6,"label":"cropped_enhanced_alpha","slot_index":2},{"name":"mask","type":"MASK","links":null,"shape":3,"label":"mask"},{"name":"detailer_pipe","type":"DETAILER_PIPE","links":null,"shape":3,"label":"detailer_pipe"},{"name":"cnet_images","type":"IMAGE","links":null,"shape":6,"label":"cnet_images"}],"properties":{"Node name for S&R":"FaceDetailer"},"widgets_values":[384,true,512,"${faceSeed}","randomize",8,"${faceCfg}","euler","normal",0.4,5,true,true,0.5,500,3,"center-1",0,0.93,0,0.7,"False",10,"",1,1,0,0]},{"id":144,"type":"BNK_CLIPTextEncodeAdvanced","pos":[3310,-1274],"size":{"0":389.95330810546875,"1":157.71157836914062},"flags":{},"order":14,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":371,"label":"clip"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[352],"shape":3,"label":"CONDITIONING","slot_index":0}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["realistic, ${FACE.extTags}","none","A1111"]},{"id":215,"type":"CheckpointLoaderSimple","pos":[2840,-1351],"size":{"0":315,"1":98},"flags":{},"order":7,"mode":0,"outputs":[{"name":"MODEL","type":"MODEL","links":[368],"shape":3,"slot_index":0,"label":"MODEL"},{"name":"CLIP","type":"CLIP","links":[369,371,372],"shape":3,"slot_index":1,"label":"CLIP"},{"name":"VAE","type":"VAE","links":[370],"shape":3,"slot_index":2,"label":"VAE"}],"properties":{"Node name for S&R":"CheckpointLoaderSimple"},"widgets_values":["真实系：majicmixRealistic_v7.safetensors"]},{"id":145,"type":"BNK_CLIPTextEncodeAdvanced","pos":[3298,-1057],"size":{"0":400,"1":200},"flags":{},"order":15,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":372,"label":"clip"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[353],"shape":3,"label":"CONDITIONING","slot_index":0}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,","none","A1111"]},{"id":1,"type":"KSampler","pos":[2484,-832],"size":{"0":320,"1":262},"flags":{},"order":21,"mode":0,"inputs":[{"name":"model","type":"MODEL","link":288,"label":"model"},{"name":"positive","type":"CONDITIONING","link":331,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":211,"label":"negative"},{"name":"latent_image","type":"LATENT","link":154,"label":"latent_image","slot_index":3}],"outputs":[{"name":"LATENT","type":"LATENT","links":[213],"slot_index":0,"label":"LATENT"}],"properties":{"Node name for S&R":"KSampler"},"widgets_values":["${seed}","increment",20,"${lora.extInfo.cfg}","dpmpp_2m_sde_gpu","karras",1]},{"id":209,"type":"LoadConrainReactorModels","pos":[4500,-1550],"size":{"0":315,"1":190},"flags":{},"order":8,"mode":0,"outputs":[{"name":"faceswapper_model","type":"FACE_MODEL","links":[342],"shape":3,"slot_index":0,"label":"faceswapper_model"},{"name":"facedetection_model","type":"FACE_MODEL","links":[343],"shape":3,"slot_index":1,"label":"facedetection_model"},{"name":"facerestore_model","type":"FACE_MODEL","links":[344],"shape":3,"slot_index":2,"label":"facerestore_model"},{"name":"faceparse_model","type":"FACE_MODEL","links":[345],"shape":3,"slot_index":3,"label":"faceparse_model"}],"properties":{"Node name for S&R":"LoadConrainReactorModels"},"widgets_values":["inswapper_128.onnx","retinaface_resnet50","GFPGANv1.4.pth","parsenet"]},{"id":135,"type":"LoraLoader","pos":[1420,-1040],"size":{"0":372.77825927734375,"1":126},"flags":{},"order":13,"mode":0,"inputs":[{"name":"model","type":"MODEL","link":236,"label":"model"},{"name":"clip","type":"CLIP","link":237,"label":"clip"}],"outputs":[{"name":"MODEL","type":"MODEL","links":[288],"shape":3,"label":"MODEL","slot_index":0},{"name":"CLIP","type":"CLIP","links":[290,329],"shape":3,"label":"CLIP","slot_index":1}],"properties":{"Node name for S&R":"LoraLoader"},"widgets_values":["${lora.loraName}",1,1],"color":"#232","bgcolor":"#353"},{"id":172,"type":"ConrainRandomPrompts","pos":[1074,-700],"size":{"0":443.5765380859375,"1":408.1977233886719},"flags":{},"order":9,"mode":0,"outputs":[{"name":"prompt","type":"STRING","links":[314,330],"shape":3,"slot_index":0,"label":"prompt"}],"properties":{"Node name for S&R":"ConrainRandomPrompts"},"widgets_values":["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\\n\\n\\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}realistic hands, well-defined hands, hands in natural positions, well-proportioned hands, natural looking hands, smooth and realistic hands, hands with clear lines and structure.\\n\\n\\n${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.lens}${SCENE.extInfo.style}","${promptSeed}","randomize"]},{"id":210,"type":"ConrainReActorFaceSwap","pos":[4992,-1565],"size":{"0":367.79998779296875,"1":370},"flags":{},"order":24,"mode":0,"inputs":[{"name":"input_image","type":"IMAGE","link":362,"label":"input_image"},{"name":"swap_model","type":"FACE_MODEL","link":342,"label":"swap_model"},{"name":"facedetection","type":"FACE_MODEL","link":343,"label":"facedetection"},{"name":"face_restore_model","type":"FACE_MODEL","link":344,"label":"face_restore_model"},{"name":"faceparse_model","type":"FACE_MODEL","link":345,"label":"faceparse_model"},{"name":"source_image","type":"IMAGE","link":346,"label":"source_image"},{"name":"face_model","type":"FACE_MODEL","link":null,"label":"face_model"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[373],"shape":3,"slot_index":0,"label":"IMAGE"},{"name":"FACE_MODEL","type":"FACE_MODEL","links":null,"shape":3,"label":"FACE_MODEL"}],"properties":{"Node name for S&R":"ConrainReActorFaceSwap"},"widgets_values":[true,0.5,"${FACE.extInfo.faceRestoreVisibility}","no","no","0","0",1,"yes"]},{"id":93,"type":"EmptyLatentImage","pos":[2015,-423],"size":{"0":282.0785217285156,"1":106},"flags":{},"order":10,"mode":0,"outputs":[{"name":"LATENT","type":"LATENT","links":[154],"shape":3,"label":"LATENT"}],"properties":{"Node name for S&R":"EmptyLatentImage"},"widgets_values":["${width}","${height}","${imageNum}"]},{"id":216,"type":"ConrainImageSave","pos":[6000,-1580],"size":{"0":320,"1":266},"flags":{},"order":25,"mode":0,"inputs":[{"name":"images","type":"IMAGE","link":373,"label":"images"},{"name":"output_path","type":"STRING","link":392,"widget":{"name":"output_path"},"label":"output_path"},{"name":"filename_prefix","type":"STRING","link":386,"widget":{"name":"filename_prefix"},"label":"filename_prefix"}],"outputs":[{"name":"image_cnt","type":"INT","links":[],"shape":3,"label":"image_cnt","slot_index":0}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["[time(%Y-%m-%d)]","ComfyUI","${fileType}",100,100,"true","false","${embedWorkflow}","true","true"]},{"id":185,"type":"Text Concatenate","pos":[4990,-450],"size":{"0":315,"1":178},"flags":{},"order":16,"mode":0,"inputs":[{"name":"text_a","type":"STRING","link":319,"widget":{"name":"text_a"},"label":"text_a"},{"name":"text_b","type":"STRING","link":314,"widget":{"name":"text_b"},"label":"text_b"},{"name":"text_c","type":"STRING","link":318,"widget":{"name":"text_c"},"label":"text_c"},{"name":"text_d","type":"STRING","link":315,"widget":{"name":"text_d"},"label":"text_d"}],"outputs":[{"name":"STRING","type":"STRING","links":[309],"shape":3,"slot_index":0,"label":"STRING"}],"properties":{"Node name for S&R":"Text Concatenate"},"widgets_values":[" ","true","","","",""]},{"id":179,"type":"Save Text File","pos":[6000,-420],"size":{"0":315,"1":154},"flags":{},"order":20,"mode":0,"inputs":[{"name":"text","type":"STRING","link":309,"widget":{"name":"text"},"label":"text"},{"name":"path","type":"STRING","link":390,"widget":{"name":"path"},"label":"path"},{"name":"filename_prefix","type":"STRING","link":385,"widget":{"name":"filename_prefix"},"label":"filename_prefix"}],"properties":{"Node name for S&R":"Save Text File"},"widgets_values":["","./ComfyUI/output/[time(%Y-%m-%d)]","ComfyUI","_",4]},{"id":232,"type":"Text Concatenate","pos":[5590,-990],"size":{"0":250,"1":142},"flags":{},"order":17,"mode":0,"inputs":[{"name":"text_a","type":"STRING","link":395,"widget":{"name":"text_a"},"label":"text_a"},{"name":"text_b","type":"STRING","link":394,"widget":{"name":"text_b"},"label":"text_b"},{"name":"text_c","type":"STRING","link":null,"widget":{"name":"text_c"},"label":"text_c"},{"name":"text_d","type":"STRING","link":null,"widget":{"name":"text_d"},"label":"text_d"}],"outputs":[{"name":"STRING","type":"STRING","links":[390],"shape":3,"slot_index":0,"label":"STRING"}],"properties":{"Node name for S&R":"Text Concatenate"},"widgets_values":["/","false","","","",""]},{"id":233,"type":"String to Text","pos":[5000,-1130],"size":{"0":315,"1":58},"flags":{"collapsed":false},"order":11,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[395],"shape":3,"slot_index":0,"label":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["output"]},{"id":177,"type":"Text String","pos":[4990,-1000],"size":{"0":315,"1":190},"flags":{},"order":12,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[392,394],"shape":3,"slot_index":0,"label":"STRING"},{"name":"STRING","type":"STRING","links":[385,386],"shape":3,"slot_index":1,"label":"STRING"},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${outputPath}","${fileNamePrefix}","",""]}],"links":[[8,2,2,6,1,"VAE"],[154,93,0,1,3,"LATENT"],[211,122,0,1,2,"CONDITIONING"],[213,1,0,6,0,"LATENT"],[236,2,0,135,0,"MODEL"],[237,2,1,135,1,"CLIP"],[288,135,0,1,0,"MODEL"],[290,135,1,122,0,"CLIP"],[309,185,0,179,0,"STRING"],[314,172,0,185,1,"STRING"],[315,171,0,185,3,"STRING"],[318,200,0,185,2,"STRING"],[319,201,0,185,0,"STRING"],[328,171,0,122,1,"STRING"],[329,135,1,205,0,"CLIP"],[330,172,0,205,1,"STRING"],[331,205,0,1,1,"CONDITIONING"],[342,209,0,210,1,"FACE_MODEL"],[343,209,1,210,2,"FACE_MODEL"],[344,209,2,210,3,"FACE_MODEL"],[345,209,3,210,4,"FACE_MODEL"],[346,152,0,210,5,"IMAGE"],[352,144,0,211,4,"CONDITIONING"],[353,145,0,211,5,"CONDITIONING"],[355,141,0,211,6,"BBOX_DETECTOR"],[361,6,0,211,0,"IMAGE"],[362,211,0,210,0,"IMAGE"],[364,142,0,211,7,"SAM_MODEL"],[368,215,0,211,1,"MODEL"],[369,215,1,211,2,"CLIP"],[370,215,2,211,3,"VAE"],[371,215,1,144,0,"CLIP"],[372,215,1,145,0,"CLIP"],[373,210,0,216,0,"IMAGE"],[385,177,1,179,2,"STRING"],[386,177,1,216,2,"STRING"],[390,232,0,179,1,"STRING"],[392,177,0,216,1,"STRING"],[394,177,0,232,1,"STRING"],[395,233,0,232,0,"STRING"]],"groups":[],"config":{},"extra":{"ds":{"scale":0.2853116706110057,"offset":{"0":-1385.550351636203,"1":2223.5398959851327}}},"version":0.4,"widget_idx_map":{"1":{"seed":0,"sampler_name":4,"scheduler":5},"172":{"seed":1},"211":{"seed":3,"sampler_name":7,"scheduler":8}},"seed_widgets":{"1":0,"172":1,"211":3}}}}}',null,now(),0,'创作流程参数配置');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('PRICE_PLAN_CFG',
'[{"code":"PLAN_FLAGSHIP","name":"旗舰包","amount":"9900","musePoint":99000,"creativeImgCountGave":8000,"clothCount":330,"creativeImgCountPerCloth":100,"maxCreativeImgCount":41000},{"code":"PLAN_PREMIUM","name":"高级包","amount":"3300","musePoint":33000,"creativeImgCountGave":110,"clothCount":110,"creativeImgCountPerCloth":100,"maxCreativeImgCount":13000},{"code":"PLAN_BASIC","name":"基础包","amount":"990","musePoint":9900,"creativeImgCountGave":0,"clothCount":33,"creativeImgCountPerCloth":100,"maxCreativeImgCount":3300},{"code":"PLAN_NEWBIE","name":"新手体验包","amount":"99","musePoint":900,"creativeImgCountGave":0,"clothCount":3,"creativeImgCountPerCloth":100,"maxCreativeImgCount":300}]',
 '', now(), 0, '前端充值方案配置');

 insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
 ('CLOTH_EXAM_CFG',
 '{"fullExamCfg":[{"title":"上传左前侧45°照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE1.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE2.jpeg"},"上装":{"correct":"","wrong":""},"下装":{"correct":"","wrong":""},"泳装":{"correct":"","wrong":""},"连体衣":{"correct":"","wrong":""},"上下装":{"correct":"","wrong":""}},"viewTags":"full_side_left_1"},{"title":"上传左前侧 微侧身 照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE3.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE4.jpeg"}},"viewTags":"full_side_left_2"},{"title":"上传正面照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE5.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE6.jpeg"}},"viewTags":"full_front"},{"title":"上传右前侧 微侧身 照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE7.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE8.jpeg"}},"viewTags":"full_side_right_1"},{"title":"上传右前侧45°照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE9.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E5%85%A8%E8%BA%AB%E5%9B%BE/%E5%85%A8%E8%BA%AB%E5%9B%BE10.jpeg"}},"viewTags":"full_side_right_2"},{"title":"上传背面左侧照","exampleImgUrl":{},"viewTags":"full_back_side_1"},{"title":"上传背面照","exampleImgUrl":{},"viewTags":"full_back"},{"title":"上传背面右侧照","exampleImgUrl":{},"viewTags":"full_back_side_2"}],"detailUpperExamCfg":[{"title":"上传左前侧45°上身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB1.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB2.jpeg"}},"viewTags":"upper_side_left_1"},{"title":"上传左前侧 微侧 上身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB3.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB4.jpeg"}},"viewTags":"upper_side_left_2"},{"title":"上传正面上身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB5.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB6.jpeg"}},"viewTags":"upper_front"},{"title":"上传右前侧 微侧 上身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB7.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB8.jpeg"}},"viewTags":"upper_side_right_1"},{"title":"上传右前侧45°上身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB9.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8A%E5%8D%8A%E8%BA%AB/%E4%B8%8A%E5%8D%8A%E8%BA%AB10.jpeg"}},"viewTags":"upper_side_right_2"}],"detailLowerExamCfg":[{"title":"上传左前侧下身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8B%E5%8D%8A%E8%BA%AB/%E4%B8%8B%E5%8D%8A%E8%BA%AB1.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8B%E5%8D%8A%E8%BA%AB/%E4%B8%8B%E5%8D%8A%E8%BA%AB2.jpeg"}},"viewTags":"lower_side_left_1"},{"title":"上传正面下身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8B%E5%8D%8A%E8%BA%AB/%E4%B8%8B%E5%8D%8A%E8%BA%AB3.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8B%E5%8D%8A%E8%BA%AB/%E4%B8%8B%E5%8D%8A%E8%BA%AB4.jpeg"}},"viewTags":"lower_front"},{"title":"上传右前侧下身细节照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8B%E5%8D%8A%E8%BA%AB/%E4%B8%8B%E5%8D%8A%E8%BA%AB5.jpeg","wrong":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E4%B8%8B%E5%8D%8A%E8%BA%AB/%E4%B8%8B%E5%8D%8A%E8%BA%AB6.jpeg"}},"viewTags":"lower_side_right_1"}],"moreExamCfg":[{"title":"上传正面举手照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E8%A1%A5%E5%85%85/%E8%A1%A5%E5%85%85%E5%9B%BE1.jpeg","wrong":""}},"viewTags":"full_front_hand_up"},{"title":"上传侧面举手照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E8%A1%A5%E5%85%85/%E8%A1%A5%E5%85%85%E5%9B%BE2.jpeg","wrong":""}},"viewTags":"full_side_hand_up"},{"title":"上传正面坐姿照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E8%A1%A5%E5%85%85/%E8%A1%A5%E5%85%85%E5%9B%BE4.jpeg","wrong":""}},"viewTags":"full_front_sit"},{"title":"上传侧面坐姿照","exampleImgUrl":{"default":{"correct":"https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%870708/%E8%A1%A5%E5%85%85/%E8%A1%A5%E5%85%85%E5%9B%BE3.jpeg","wrong":""}},"viewTags":"full_side_sit"}]}',
  '', now(), 0, '前端上传素材页面示例配置');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
 ('SCENE_TYPE_CFG',
 '[{"code":"home life","name":"居家生活"},{"code":"outdoor travel","name":"户外旅游"},{"code":"street photo","name":"城市街拍"},{"code":"indoor leisure","name":"室内休闲"},{"code":"pure background","name":"纯色背景"},{"code":"chinese style","name":"中式"},{"code":"western style","name":"西式"}]',
  '', now(), 0, '前端后台选择场景分类');

 insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
 ('FACE_TYPE_CFG', '[{"code":"female-model","name":"女模"},{"code":"male-model","name":"男模"},{"code":"child-model","name":"童模"}]','', now(), 0, '模特分类');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('COMFYUI_PORT_CFG','',null,now(),0,'COMFYUI端口映射配置');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('COMFYUI_PORT_CFG_LORA','',null,now(),0,'COMFYUI-LORA（A800）训练端口映射配置');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('CREATIVE_STATUS_SYNC_BATCH','1',null,now(),0,'创作调度任务批次大小');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('MODERATION_FORBID_LABEL','pornographic_adultContent,suggestiveContent_bump,sexual_child,horrific_horrific,horrific_blood,contraband_gamble,contraband_drug,suggestiveContent_sexhint',null,now(),0,'图片安全检测禁止标签列表');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('NEED_REPLACE_MODEL_KEYS','the model,a model',null,now(),0,'需要替换的模特关键字，多个以逗号分隔');

update creative_batch set model_type = 'SYSTEM' where model_id in (select id from material_model where type = 'SYSTEM');
--insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
--('COMFYUI_PORT_CFG','{"100002":"20318","100003":"20311","100004":"20312","100005":"20317","100006":"20314","100008":"20315","100010":"20316","100012":"20313"}',null,now(),0,'COMFYUI端口映射配置');

insert into creative_element(id, name, `level`, parent_id, config_key, tags, ext_tags, ext_info, show_image, `order`, memo, `type`, deleted) values
(1, '模特', 1, null, 'FACE', null, null, null, null, 1, null, 'FACE', 0),
(2, '场景', 1, null, 'SCENE', null, null, null, null, 2, null, 'PROMPT', 0),
(3, '模特名字1', 2, 1, 'FACE', '1girl', 'clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression', '{"faceImage":"WechatIMG760.jpg"}', 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/model_demo.png', 1, null, 'FACE', 0),
(4, '模特名字2', 2, 1, 'FACE', '1girl', 'clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression', '{"faceImage":"WechatIMG760.jpg"}', 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/model_demo.png', 2, null, 'FACE', 0),
(5, '模特名字3', 2, 1, 'FACE', '1girl', 'clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression', '{"faceImage":"WechatIMG760.jpg"}', 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/model_demo.png', 3, null, 'FACE', 0),
(6, '模特名字4', 2, 1, 'FACE', '1girl', 'clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression', '{"faceImage":"WechatIMG760.jpg"}', 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/model_demo.png', 4, null, 'FACE', 0),
(7, '模特名字5', 2, 1, 'FACE', '1girl', 'clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression', '{"faceImage":"WechatIMG760.jpg"}', 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/model_demo.png', 5, null, 'FACE', 0),
(8, '模特名字6', 2, 1, 'FACE', '1girl', 'clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression', '{"faceImage":"WechatIMG760.jpg"}', 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/model_demo.png', 6, null, 'FACE', 0),
(9, '场景名字1', 2, 2, 'SCENE', 'seaside', null, null, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/scene_demo.png', 1, null, 'PROMPT', 0),
(10, '场景名字2', 2, 2, 'SCENE', 'seaside', null, null, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/scene_demo.png', 2, null, 'PROMPT', 0),
(11, '场景名字3', 2, 2, 'SCENE', 'seaside', null, null, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/scene_demo1.png', 3, null, 'PROMPT', 0),
(12, '场景名字4', 2, 2, 'SCENE', 'seaside', null, null, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/scene_demo1.png', 4, null, 'PROMPT', 0);

update `creative_element` set `type` = null;

insert into material_model(name, lora_name, tags, user_id, show_image, status, operator_id, deleted) values
('铁翅红bra', 'tc/swimsuit/tc2ss_0423_v4.safetensors', 'tc2ss, a bra and a pants, the girl wearing the tc2ss, full body', 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'ENABLED', 100002, 0),
('模型名称2', 'tc/swimsuit/tc2ss_0423_v4.safetensors', 'tc2ss, a bra and a pants, the girl wearing the tc2ss, full body', 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'ENABLED', 100000, 0),
('模型名称3', 'tc/swimsuit/tc2ss_0423_v4.safetensors', 'tc2ss, a bra and a pants, the girl wearing the tc2ss, full body', 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'ENABLED', 100000, 0),
('模型名称4', 'tc/swimsuit/tc2ss_0423_v4.safetensors', 'tc2ss, a bra and a pants, the girl wearing the tc2ss, full body', 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'ENABLED', 100000, 0),
('模型名称5', 'tc/swimsuit/tc2ss_0423_v4.safetensors', 'tc2ss, a bra and a pants, the girl wearing the tc2ss, full body', 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'ENABLED', 100000, 0),
('模型名称6', 'tc/swimsuit/tc2ss_0423_v4.safetensors', 'tc2ss, a bra and a pants, the girl wearing the tc2ss, full body', 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'ENABLED', 100000, 0);

INSERT INTO `user` (`nick_name`, `real_name`, `login_id`, `pswd`, `mobile`, `role_type`, `user_type`, `master_id`, `status`, `operator_id`, `register_from`, `memo`, `login_fail_count`, `last_login_time`, `deleted`, `create_time`, `modify_time`)
VALUES
('松然', null, '15906660486', null, '15906660486', 'ADMIN', 'MASTER', null, 'ENABLED', 100000, 'website', 'No remarks', 0, NULL, FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('半泉admin', null, '17364559376', null, '17364559376', 'ADMIN', 'MASTER', null, 'ENABLED', 100000, 'website', 'No remarks', 0, NULL, FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('伊登admin', null, '18717831174', null, '18717831174', 'ADMIN', 'MASTER', null, 'ENABLED', 100000, 'website', 'No remarks', 0, NULL, FALSE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


INSERT INTO `creative_batch` (`model_id`, `user_id`, `show_image`, `image_proportion`, `batch_cnt`, `aigc_request`, `prompt_id`, `result_images`, `result_path`, `status`, `operator_id`, `deleted`, `create_time`, `modify_time`, `ext_info`)
VALUES (100000, 100000, 'https://aigc-conrain.oss-cn-zhangjiakou.aliyuncs.com/image/lora_demo.png', 'THREE_FOUR', 5, '{\"prompt\": {\"1\": {\"inputs\": {\"cfg\": 5, \"seed\": \"038870744177351\", \"model\": [\"135\", 0], \"steps\": 20, \"denoise\": 1, \"negative\": [\"122\", 0], \"positive\": [\"119\", 0], \"scheduler\": \"karras\", \"latent_image\": [\"93\", 0], \"sampler_name\": \"dpmpp_2m_sde_gpu\"}, \"class_type\": \"KSampler\"}, \"2\": {\"inputs\": {\"ckpt_name\": \"sdxl/zavychromaxl_v60.safetensors\"}, \"class_type\": \"CheckpointLoaderSimple\"}, \"6\": {\"inputs\": {\"vae\": [\"2\", 2], \"samples\": [\"1\", 0]}, \"class_type\": \"VAEDecode\"}, \"86\": {\"inputs\": {\"style\": \"photographic\", \"log_prompt\": \"No\", \"style_name\": \"\", \"text_negative\": \"(mannequin:1.05), (blurry:1.05), bad quality, (NSFW:1.1), text, watermark, embedding:badhandv4, embedding:BadDream, \", \"text_positive\": \"tc2ss, a bra and a pants, the girl wearing the tc2ss, full body, 1girl, seaside,\"}, \"class_type\": \"SDXLPromptStyler\"}, \"93\": {\"inputs\": {\"width\": 768, \"height\": 1024, \"batch_size\": 5}, \"class_type\": \"EmptyLatentImage\"}, \"119\": {\"inputs\": {\"clip\": [\"135\", 1], \"text\": [\"86\", 0], \"token_normalization\": \"none\", \"weight_interpretation\": \"A1111\"}, \"class_type\": \"BNK_CLIPTextEncodeAdvanced\"}, \"122\": {\"inputs\": {\"clip\": [\"135\", 1], \"text\": [\"86\", 1], \"token_normalization\": \"none\", \"weight_interpretation\": \"A1111\"}, \"class_type\": \"BNK_CLIPTextEncodeAdvanced\"}, \"135\": {\"inputs\": {\"clip\": [\"2\", 1], \"model\": [\"2\", 0], \"lora_name\": \"tc/swimsuit/tc2ss_0423_v4.safetensors\", \"strength_clip\": 1, \"strength_model\": 1}, \"class_type\": \"LoraLoader\"}, \"140\": {\"inputs\": {\"cfg\": 7, \"vae\": [\"2\", 2], \"clip\": [\"135\", 1], \"seed\": 980856327754367, \"cycle\": 1, \"image\": [\"6\", 0], \"model\": [\"2\", 0], \"steps\": 22, \"denoise\": 0.4, \"feather\": 5, \"max_size\": 1024, \"negative\": [\"145\", 0], \"positive\": [\"144\", 0], \"wildcard\": \"\", \"drop_size\": 10, \"scheduler\": \"normal\", \"guide_size\": 384, \"noise_mask\": true, \"sam_dilation\": 0, \"sampler_name\": \"euler\", \"bbox_detector\": [\"141\", 0], \"bbox_dilation\": 10, \"force_inpaint\": true, \"inpaint_model\": true, \"sam_model_opt\": [\"142\", 0], \"sam_threshold\": 0.93, \"bbox_threshold\": 0.5, \"guide_size_for\": true, \"bbox_crop_factor\": 3, \"segm_detector_opt\": [\"143\", 1], \"noise_mask_feather\": 0, \"sam_bbox_expansion\": 0, \"sam_detection_hint\": \"center-1\", \"sam_mask_hint_threshold\": 0.7, \"sam_mask_hint_use_negative\": \"False\"}, \"class_type\": \"FaceDetailer\"}, \"141\": {\"inputs\": {\"model_name\": \"bbox/face_yolov8s.pt\"}, \"class_type\": \"UltralyticsDetectorProvider\"}, \"142\": {\"inputs\": {\"model_name\": \"sam_vit_b_01ec64.pth\", \"device_mode\": \"Prefer GPU\"}, \"class_type\": \"SAMLoader\"}, \"143\": {\"inputs\": {\"model_name\": \"segm/person_yolov8s-seg.pt\"}, \"class_type\": \"UltralyticsDetectorProvider\"}, \"144\": {\"inputs\": {\"clip\": [\"135\", 1], \"text\": \"clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression\", \"token_normalization\": \"none\", \"weight_interpretation\": \"A1111\"}, \"class_type\": \"BNK_CLIPTextEncodeAdvanced\"}, \"145\": {\"inputs\": {\"clip\": [\"135\", 1], \"text\": \"EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,\", \"token_normalization\": \"none\", \"weight_interpretation\": \"A1111\"}, \"class_type\": \"BNK_CLIPTextEncodeAdvanced\"}, \"151\": {\"inputs\": {\"enabled\": true, \"swap_model\": \"inswapper_128.onnx\", \"input_image\": [\"140\", 0], \"source_image\": [\"152\", 0], \"facedetection\": \"retinaface_resnet50\", \"codeformer_weight\": 0.5, \"console_log_level\": 1, \"input_faces_index\": \"0\", \"face_restore_model\": \"GFPGANv1.4.pth\", \"source_faces_index\": \"0\", \"detect_gender_input\": \"no\", \"detect_gender_source\": \"no\", \"face_restore_visibility\": 1}, \"class_type\": \"ReActorFaceSwap\"}, \"152\": {\"inputs\": {\"image\": \"WechatIMG760.jpg\", \"upload\": \"image\"}, \"class_type\": \"LoadImage\"}, \"154\": {\"inputs\": {\"images\": [\"151\", 0], \"quality\": 100, \"extension\": \"png\", \"output_path\": \"product/20240513/100000/44\", \"show_history\": \"false\", \"lossless_webp\": \"false\", \"show_previews\": \"true\", \"embed_workflow\": \"true\", \"overwrite_mode\": \"false\", \"filename_prefix\": \"product\", \"filename_delimiter\": \"_\", \"filename_number_start\": \"false\", \"show_history_by_prefix\": \"true\", \"filename_number_padding\": 4}, \"class_type\": \"Image Save\"}}, \"client_id\": \"_aigc_platform_operator_100000\", \"extra_data\": {\"extra_pnginfo\": {\"workflow\": {\"extra\": {}, \"links\": [[8, 2, 2, 6, 1, \"VAE\"], [154, 93, 0, 1, 3, \"LATENT\"], [209, 119, 0, 1, 1, \"CONDITIONING\"], [211, 122, 0, 1, 2, \"CONDITIONING\"], [213, 1, 0, 6, 0, \"LATENT\"], [236, 2, 0, 135, 0, \"MODEL\"], [237, 2, 1, 135, 1, \"CLIP\"], [245, 86, 1, 122, 1, \"STRING\"], [247, 86, 0, 119, 1, \"STRING\"], [249, 144, 0, 140, 4, \"CONDITIONING\"], [250, 145, 0, 140, 5, \"CONDITIONING\"], [251, 141, 0, 140, 6, \"BBOX_DETECTOR\"], [252, 142, 0, 140, 7, \"SAM_MODEL\"], [253, 143, 1, 140, 8, \"SEGM_DETECTOR\"], [255, 2, 0, 140, 1, \"MODEL\"], [256, 2, 2, 140, 3, \"VAE\"], [257, 135, 1, 144, 0, \"CLIP\"], [258, 135, 1, 145, 0, \"CLIP\"], [260, 135, 1, 140, 2, \"CLIP\"], [271, 152, 0, 151, 1, \"IMAGE\"], [273, 6, 0, 140, 0, \"IMAGE\"], [274, 140, 0, 151, 0, \"IMAGE\"], [275, 135, 0, 1, 0, \"MODEL\"], [276, 135, 1, 119, 0, \"CLIP\"], [277, 135, 1, 122, 0, \"CLIP\"], [278, 151, 0, 154, 0, \"IMAGE\"]], \"nodes\": [{\"id\": 145, \"pos\": [3310, -860], \"mode\": 0, \"size\": {\"0\": 400, \"1\": 200}, \"type\": \"BNK_CLIPTextEncodeAdvanced\", \"flags\": {}, \"order\": 9, \"inputs\": [{\"link\": 258, \"name\": \"clip\", \"type\": \"CLIP\", \"label\": \"clip\"}], \"outputs\": [{\"name\": \"CONDITIONING\", \"type\": \"CONDITIONING\", \"label\": \"CONDITIONING\", \"links\": [250], \"shape\": 3, \"slot_index\": 0}], \"properties\": {\"Node name for S&R\": \"BNK_CLIPTextEncodeAdvanced\"}, \"widgets_values\": [\"EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,\", \"none\", \"A1111\"]}, {\"id\": 141, \"pos\": [3350, -560], \"mode\": 0, \"size\": {\"0\": 315, \"1\": 78}, \"type\": \"UltralyticsDetectorProvider\", \"flags\": {}, \"order\": 0, \"outputs\": [{\"name\": \"BBOX_DETECTOR\", \"type\": \"BBOX_DETECTOR\", \"label\": \"BBOX_DETECTOR\", \"links\": [251], \"shape\": 3, \"slot_index\": 0}, {\"name\": \"SEGM_DETECTOR\", \"type\": \"SEGM_DETECTOR\", \"label\": \"SEGM_DETECTOR\", \"links\": [], \"shape\": 3, \"slot_index\": 1}], \"properties\": {\"Node name for S&R\": \"UltralyticsDetectorProvider\"}, \"widgets_values\": [\"bbox/face_yolov8s.pt\"]}, {\"id\": 142, \"pos\": [3350, -410], \"mode\": 0, \"size\": {\"0\": 315, \"1\": 82}, \"type\": \"SAMLoader\", \"flags\": {}, \"order\": 1, \"outputs\": [{\"name\": \"SAM_MODEL\", \"type\": \"SAM_MODEL\", \"label\": \"SAM_MODEL\", \"links\": [252], \"shape\": 3}], \"properties\": {\"Node name for S&R\": \"SAMLoader\"}, \"widgets_values\": [\"sam_vit_b_01ec64.pth\", \"Prefer GPU\"]}, {\"id\": 143, \"pos\": [3350, -240], \"mode\": 0, \"size\": {\"0\": 315, \"1\": 78}, \"type\": \"UltralyticsDetectorProvider\", \"flags\": {}, \"order\": 2, \"outputs\": [{\"name\": \"BBOX_DETECTOR\", \"type\": \"BBOX_DETECTOR\", \"label\": \"BBOX_DETECTOR\", \"links\": null, \"shape\": 3, \"slot_index\": 0}, {\"name\": \"SEGM_DETECTOR\", \"type\": \"SEGM_DETECTOR\", \"label\": \"SEGM_DETECTOR\", \"links\": [253], \"shape\": 3, \"slot_index\": 1}], \"properties\": {\"Node name for S&R\": \"UltralyticsDetectorProvider\"}, \"widgets_values\": [\"segm/person_yolov8s-seg.pt\"]}, {\"id\": 1, \"pos\": [2360.883235275207, -1179.3725775193466], \"mode\": 0, \"size\": {\"0\": 315, \"1\": 262}, \"type\": \"KSampler\", \"flags\": {}, \"order\": 12, \"inputs\": [{\"link\": 275, \"name\": \"model\", \"type\": \"MODEL\", \"label\": \"model\"}, {\"link\": 209, \"name\": \"positive\", \"type\": \"CONDITIONING\", \"label\": \"positive\"}, {\"link\": 211, \"name\": \"negative\", \"type\": \"CONDITIONING\", \"label\": \"negative\"}, {\"link\": 154, \"name\": \"latent_image\", \"type\": \"LATENT\", \"label\": \"latent_image\", \"slot_index\": 3}], \"outputs\": [{\"name\": \"LATENT\", \"type\": \"LATENT\", \"label\": \"LATENT\", \"links\": [213], \"slot_index\": 0}], \"properties\": {\"Node name for S&R\": \"KSampler\"}, \"widgets_values\": [\"038870744177351\", \"randomize\", 20, 5, \"dpmpp_2m_sde_gpu\", \"karras\", 1]}, {\"id\": 144, \"pos\": [3310, -1100], \"mode\": 0, \"size\": {\"0\": 389.9533081054687, \"1\": 157.71157836914062}, \"type\": \"BNK_CLIPTextEncodeAdvanced\", \"flags\": {}, \"order\": 8, \"inputs\": [{\"link\": 257, \"name\": \"clip\", \"type\": \"CLIP\", \"label\": \"clip\"}], \"outputs\": [{\"name\": \"CONDITIONING\", \"type\": \"CONDITIONING\", \"label\": \"CONDITIONING\", \"links\": [249], \"shape\": 3, \"slot_index\": 0}], \"properties\": {\"Node name for S&R\": \"BNK_CLIPTextEncodeAdvanced\"}, \"widgets_values\": [\"clear face, happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression\", \"none\", \"A1111\"]}, {\"id\": 2, \"pos\": [1070, -1160], \"mode\": 0, \"size\": {\"0\": 315, \"1\": 98}, \"type\": \"CheckpointLoaderSimple\", \"flags\": {}, \"order\": 3, \"outputs\": [{\"name\": \"MODEL\", \"type\": \"MODEL\", \"label\": \"MODEL\", \"links\": [236, 255], \"slot_index\": 0}, {\"name\": \"CLIP\", \"type\": \"CLIP\", \"label\": \"CLIP\", \"links\": [237], \"slot_index\": 1}, {\"name\": \"VAE\", \"type\": \"VAE\", \"label\": \"VAE\", \"links\": [8, 256], \"slot_index\": 2}], \"properties\": {\"Node name for S&R\": \"CheckpointLoaderSimple\"}, \"widgets_values\": [\"sdxl/zavychromaxl_v60.safetensors\"]}, {\"id\": 119, \"pos\": [1620, -710], \"mode\": 0, \"size\": {\"0\": 230, \"1\": 140}, \"type\": \"BNK_CLIPTextEncodeAdvanced\", \"flags\": {}, \"order\": 10, \"inputs\": [{\"link\": 276, \"name\": \"clip\", \"type\": \"CLIP\", \"label\": \"clip\"}, {\"link\": 247, \"name\": \"text\", \"type\": \"STRING\", \"label\": \"text\", \"widget\": {\"name\": \"text\"}}], \"outputs\": [{\"name\": \"CONDITIONING\", \"type\": \"CONDITIONING\", \"label\": \"CONDITIONING\", \"links\": [209], \"shape\": 3, \"slot_index\": 0}], \"properties\": {\"Node name for S&R\": \"BNK_CLIPTextEncodeAdvanced\"}, \"widgets_values\": [\" (pinseneiyi:1.2), (huashufenwei:1.2)\", \"none\", \"A1111\"]}, {\"id\": 122, \"pos\": [1620, -470], \"mode\": 0, \"size\": {\"0\": 226.8000030517578, \"1\": 160}, \"type\": \"BNK_CLIPTextEncodeAdvanced\", \"color\": \"#322\", \"flags\": {}, \"order\": 11, \"inputs\": [{\"link\": 277, \"name\": \"clip\", \"type\": \"CLIP\", \"label\": \"clip\"}, {\"link\": 245, \"name\": \"text\", \"type\": \"STRING\", \"label\": \"text\", \"widget\": {\"name\": \"text\"}}], \"bgcolor\": \"#533\", \"outputs\": [{\"name\": \"CONDITIONING\", \"type\": \"CONDITIONING\", \"label\": \"CONDITIONING\", \"links\": [211], \"shape\": 3, \"slot_index\": 0}], \"properties\": {\"Node name for S&R\": \"BNK_CLIPTextEncodeAdvanced\"}, \"widgets_values\": [\"\", \"none\", \"A1111\"]}, {\"id\": 86, \"pos\": [1070, -670], \"mode\": 0, \"size\": {\"0\": 490, \"1\": 440}, \"type\": \"SDXLPromptStyler\", \"color\": \"#232\", \"flags\": {\"collapsed\": false}, \"order\": 4, \"bgcolor\": \"#353\", \"outputs\": [{\"name\": \"positive_prompt_text_g\", \"type\": \"STRING\", \"label\": \"positive_prompt_text_g\", \"links\": [247], \"shape\": 3, \"slot_index\": 0}, {\"name\": \"negative_prompt_text_g\", \"type\": \"STRING\", \"label\": \"negative_prompt_text_g\", \"links\": [245], \"shape\": 3, \"slot_index\": 1}], \"properties\": {\"Node name for S&R\": \"SDXLPromptStyler\"}, \"widgets_values\": [\"tc2ss, a bra and a pants, the girl wearing the tc2ss, full body, 1girl, seaside,\", \"(mannequin:1.05), (blurry:1.05), bad quality, (NSFW:1.1), text, watermark, embedding:badhandv4, embedding:BadDream, \", \"photographic\", \"No\", \"\"]}, {\"id\": 6, \"pos\": [2800, -1160], \"mode\": 0, \"size\": {\"0\": 210, \"1\": 46}, \"type\": \"VAEDecode\", \"flags\": {\"collapsed\": false}, \"order\": 13, \"inputs\": [{\"link\": 213, \"name\": \"samples\", \"type\": \"LATENT\", \"label\": \"samples\"}, {\"link\": 8, \"name\": \"vae\", \"type\": \"VAE\", \"label\": \"vae\"}], \"outputs\": [{\"name\": \"IMAGE\", \"type\": \"IMAGE\", \"label\": \"IMAGE\", \"links\": [273], \"slot_index\": 0}], \"properties\": {\"Node name for S&R\": \"VAEDecode\"}}, {\"id\": 140, \"pos\": [3810, -1130], \"mode\": 0, \"size\": {\"0\": 506.4000244140625, \"1\": 880}, \"type\": \"FaceDetailer\", \"flags\": {}, \"order\": 14, \"inputs\": [{\"link\": 273, \"name\": \"image\", \"type\": \"IMAGE\", \"label\": \"image\"}, {\"link\": 255, \"name\": \"model\", \"type\": \"MODEL\", \"label\": \"model\"}, {\"link\": 260, \"name\": \"clip\", \"type\": \"CLIP\", \"label\": \"clip\"}, {\"link\": 256, \"name\": \"vae\", \"type\": \"VAE\", \"label\": \"vae\"}, {\"link\": 249, \"name\": \"positive\", \"type\": \"CONDITIONING\", \"label\": \"positive\"}, {\"link\": 250, \"name\": \"negative\", \"type\": \"CONDITIONING\", \"label\": \"negative\", \"slot_index\": 5}, {\"link\": 251, \"name\": \"bbox_detector\", \"type\": \"BBOX_DETECTOR\", \"label\": \"bbox_detector\", \"slot_index\": 6}, {\"link\": 252, \"name\": \"sam_model_opt\", \"type\": \"SAM_MODEL\", \"label\": \"sam_model_opt\", \"slot_index\": 7}, {\"link\": 253, \"name\": \"segm_detector_opt\", \"type\": \"SEGM_DETECTOR\", \"label\": \"segm_detector_opt\", \"slot_index\": 8}, {\"link\": null, \"name\": \"detailer_hook\", \"type\": \"DETAILER_HOOK\", \"label\": \"detailer_hook\"}], \"outputs\": [{\"name\": \"image\", \"type\": \"IMAGE\", \"label\": \"image\", \"links\": [274], \"shape\": 3, \"slot_index\": 0}, {\"name\": \"cropped_refined\", \"type\": \"IMAGE\", \"label\": \"cropped_refined\", \"links\": [], \"shape\": 6, \"slot_index\": 1}, {\"name\": \"cropped_enhanced_alpha\", \"type\": \"IMAGE\", \"label\": \"cropped_enhanced_alpha\", \"links\": [], \"shape\": 6, \"slot_index\": 2}, {\"name\": \"mask\", \"type\": \"MASK\", \"label\": \"mask\", \"links\": null, \"shape\": 3}, {\"name\": \"detailer_pipe\", \"type\": \"DETAILER_PIPE\", \"label\": \"detailer_pipe\", \"links\": null, \"shape\": 3}, {\"name\": \"cnet_images\", \"type\": \"IMAGE\", \"label\": \"cnet_images\", \"links\": null, \"shape\": 6}], \"properties\": {\"Node name for S&R\": \"FaceDetailer\"}, \"widgets_values\": [384, true, 1024, 980856327754367, \"randomize\", 22, 7, \"euler\", \"normal\", 0.4, 5, true, true, 0.5, 10, 3, \"center-1\", 0, 0.93, 0, 0.7, \"False\", 10, \"\", 1, true, 0]}, {\"id\": 135, \"pos\": [1050, -960], \"mode\": 0, \"size\": {\"0\": 372.77825927734375, \"1\": 126}, \"type\": \"LoraLoader\", \"color\": \"#232\", \"flags\": {}, \"order\": 7, \"inputs\": [{\"link\": 236, \"name\": \"model\", \"type\": \"MODEL\", \"label\": \"model\"}, {\"link\": 237, \"name\": \"clip\", \"type\": \"CLIP\", \"label\": \"clip\"}], \"bgcolor\": \"#353\", \"outputs\": [{\"name\": \"MODEL\", \"type\": \"MODEL\", \"label\": \"MODEL\", \"links\": [275], \"shape\": 3, \"slot_index\": 0}, {\"name\": \"CLIP\", \"type\": \"CLIP\", \"label\": \"CLIP\", \"links\": [257, 258, 260, 276, 277], \"shape\": 3, \"slot_index\": 1}], \"properties\": {\"Node name for S&R\": \"LoraLoader\"}, \"widgets_values\": [\"tc/swimsuit/tc2ss_0423_v4.safetensors\", 1, 1]}, {\"id\": 152, \"pos\": [3900, -150], \"mode\": 0, \"size\": [320, 310], \"type\": \"LoadImage\", \"flags\": {}, \"order\": 5, \"outputs\": [{\"name\": \"IMAGE\", \"type\": \"IMAGE\", \"label\": \"IMAGE\", \"links\": [271], \"shape\": 3, \"slot_index\": 0}, {\"name\": \"MASK\", \"type\": \"MASK\", \"label\": \"MASK\", \"links\": null, \"shape\": 3}], \"properties\": {\"Node name for S&R\": \"LoadImage\"}, \"widgets_values\": [\"WechatIMG760.jpg\", \"image\"]}, {\"id\": 154, \"pos\": [5120, -1110], \"mode\": 0, \"size\": {\"0\": 320, \"1\": 560}, \"type\": \"Image Save\", \"flags\": {}, \"order\": 16, \"inputs\": [{\"link\": 278, \"name\": \"images\", \"type\": \"IMAGE\", \"label\": \"images\"}], \"properties\": {\"Node name for S&R\": \"Image Save\"}, \"widgets_values\": [\"product/\", \"product\", \"_\", 4, \"false\", \"png\", 100, \"false\", \"false\", \"false\", \"true\", \"true\", \"true\"]}, {\"id\": 151, \"pos\": [4590, -1120], \"mode\": 0, \"size\": {\"0\": 315, \"1\": 338}, \"type\": \"ReActorFaceSwap\", \"flags\": {}, \"order\": 15, \"inputs\": [{\"link\": 274, \"name\": \"input_image\", \"type\": \"IMAGE\", \"label\": \"input_image\"}, {\"link\": 271, \"name\": \"source_image\", \"type\": \"IMAGE\", \"label\": \"source_image\"}, {\"link\": null, \"name\": \"face_model\", \"type\": \"FACE_MODEL\", \"label\": \"face_model\"}], \"outputs\": [{\"name\": \"IMAGE\", \"type\": \"IMAGE\", \"label\": \"IMAGE\", \"links\": [278], \"shape\": 3, \"slot_index\": 0}, {\"name\": \"FACE_MODEL\", \"type\": \"FACE_MODEL\", \"label\": \"FACE_MODEL\", \"links\": null, \"shape\": 3}], \"properties\": {\"Node name for S&R\": \"ReActorFaceSwap\"}, \"widgets_values\": [true, \"inswapper_128.onnx\", \"retinaface_resnet50\", \"GFPGANv1.4.pth\", 1, 0.5, \"no\", \"no\", \"0\", \"0\", 1]}, {\"id\": 93, \"pos\": [1960, -1160], \"mode\": 0, \"size\": {\"0\": 282.0785217285156, \"1\": 106}, \"type\": \"EmptyLatentImage\", \"flags\": {}, \"order\": 6, \"outputs\": [{\"name\": \"LATENT\", \"type\": \"LATENT\", \"label\": \"LATENT\", \"links\": [154], \"shape\": 3}], \"properties\": {\"Node name for S&R\": \"EmptyLatentImage\"}, \"widgets_values\": [768, 1024, 1]}], \"config\": {}, \"groups\": [{\"color\": \"#3f789e\", \"title\": \"Group\", \"locked\": false, \"bounding\": [1897, -1255, 1250, 1089], \"font_size\": 24}, {\"color\": \"#3f789e\", \"title\": \"Group\", \"locked\": false, \"bounding\": [1024, -782, 843, 661], \"font_size\": 24}, {\"color\": \"#3f789e\", \"title\": \"Group\", \"locked\": false, \"bounding\": [1027, -1255, 844, 450], \"font_size\": 24}], \"version\": 0.4, \"last_link_id\": 278, \"last_node_id\": 154, \"seed_widgets\": {\"1\": 0, \"140\": 3}, \"widget_idx_map\": {\"1\": {\"seed\": 0, \"scheduler\": 5, \"sampler_name\": 4}, \"140\": {\"seed\": 3, \"scheduler\": 8, \"sampler_name\": 7}}}}}}', 'ffe4860a-4030-4583-a93e-b1c65115deb2', '[\"https://agri-f2f-dev.oss-cn-zhangjiakou.aliyuncs.com/202405/product_0002.png?Expires=3292371180&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=NN5xxirvAYJ0ZWUGgRppedZD6bA%3D\",\"https://agri-f2f-dev.oss-cn-zhangjiakou.aliyuncs.com/202405/product_0004.png?Expires=3292371180&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=59bBuAEtqZR1bwbNdKEMs%2BnBcnA%3D\",\"https://agri-f2f-dev.oss-cn-zhangjiakou.aliyuncs.com/202405/product_0003.png?Expires=3292371180&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=p6RAf1Xlc5YM%2Bn6XpZR%2F%2BGng%2Fp0%3D\",\"https://agri-f2f-dev.oss-cn-zhangjiakou.aliyuncs.com/202405/product_0005.png?Expires=3292371180&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=lRj3bdCItzfNVq8dPZwnm%2FVE0q0%3D\",\"https://agri-f2f-dev.oss-cn-zhangjiakou.aliyuncs.com/202405/product_0001.png?Expires=3292371180&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=Ksk1LZrUIYaC3Py10m1QUcgV3ok%3D\"]', 'product/20240513/100000/44', 'FINISHED', 100000, 0, '2024-05-13 11:27:43', '2024-05-13 11:33:01', '{\"zipUrl\": \"https://agri-f2f-dev.oss-cn-zhangjiakou.aliyuncs.com/product_100000_44.zip?Expires=3292371180&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=WLPpbqfdvCpgV62a6YciyTHlgtM%3D\"}');


insert into user_point (user_id,`point`) values (100000,100000);

-- 0705 手部修复
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/fetchTaskInfo';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/fetchTaskImage/*';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/repairHands';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/deleteById';
insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('REPAIR_HANDS_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"11":{"inputs":{"mask_bbox_padding":50,"resolution":512,"mask_type":"based_on_depth","mask_expand":50,"rand_seed":88,"image":["382",0]},"class_type":"MeshGraphormer-DepthMapPreprocessor","_meta":{"title":"MeshGraphormer Hand Refiner"}},"12":{"inputs":{"strength":0.65,"start_percent":0,"end_percent":1,"positive":["133",1],"negative":["133",2],"control_net":["13",0],"image":["11",0]},"class_type":"ControlNetApplyAdvanced","_meta":{"title":"Apply ControlNet (Advanced)"}},"13":{"inputs":{"control_net_name":"xl/sai_xl_depth_256lora.safetensors"},"class_type":"ControlNetLoader","_meta":{"title":"Load ControlNet Model"}},"14":{"inputs":{"samples":["31",0],"vae":["128",2]},"class_type":"VAEDecode","_meta":{"title":"VAE Decode"}},"17":{"inputs":{"samples":["28",0],"mask":["383",0]},"class_type":"SetLatentNoiseMask","_meta":{"title":"Set Latent Noise Mask"}},"20":{"inputs":{"amount":2,"samples":["17",0]},"class_type":"RepeatLatentBatch","_meta":{"title":"Repeat Latent Batch"}},"27":{"inputs":{"upscale_method":"lanczos","scale_by":["306",3],"image":["168",0]},"class_type":"ImageScaleBy","_meta":{"title":"Upscale Image By"}},"28":{"inputs":{"pixels":["382",0],"vae":["128",2]},"class_type":"VAEEncode","_meta":{"title":"VAE Encode"}},"31":{"inputs":{"seed":"${seed}","steps":20,"cfg":3.5,"sampler_name":"dpmpp_3m_sde_gpu","scheduler":"exponential","denoise":0.65,"model":["128",0],"positive":["12",0],"negative":["12",1],"latent_image":["20",0]},"class_type":"KSampler","_meta":{"title":"KSampler"}},"54":{"inputs":{"scale_by":["306",3],"mask":["387",0]},"class_type":"MaskUpscale_LR","_meta":{"title":"Mask Upscale LR"}},"127":{"inputs":{"brushnet":"brushnet_xl/diffusion_pytorch_model.safetensors","dtype":"float16"},"class_type":"BrushNetLoader","_meta":{"title":"BrushNet Loader"}},"128":{"inputs":{"ckpt_name":"sdxl/zavychromaxl_v60.safetensors"},"class_type":"CheckpointLoaderSimple","_meta":{"title":"Load Checkpoint"}},"129":{"inputs":{"text":"hand of young fashion model, high quality, high detail, realistic,","clip":["128",1]},"class_type":"CLIPTextEncode","_meta":{"title":"CLIP Text Encode (Prompt)"}},"130":{"inputs":{"text":"nail polish, jewelry, accessories, gloves, wristbands, coverings, text, watermark,","clip":["128",1]},"class_type":"CLIPTextEncode","_meta":{"title":"CLIP Text Encode (Prompt)"}},"131":{"inputs":{"seed":"${seed2}","steps":20,"cfg":5,"sampler_name":"dpmpp_3m_sde_gpu","scheduler":"exponential","denoise":1,"model":["133",0],"positive":["133",1],"negative":["133",2],"latent_image":["180",0]},"class_type":"KSampler","_meta":{"title":"KSampler"}},"132":{"inputs":{"samples":["131",0],"vae":["128",2]},"class_type":"VAEDecode","_meta":{"title":"VAE Decode"}},"133":{"inputs":{"scale":1,"start_at":0,"end_at":10000,"model":["128",0],"vae":["128",2],"image":["372",0],"mask":["386",0],"brushnet":["127",0],"positive":["129",0],"negative":["130",0]},"class_type":"BrushNet","_meta":{"title":"BrushNet"}},"168":{"inputs":{"x":0,"y":0,"resize_source":false,"destination":["305",0],"source":["306",0],"mask":["305",2]},"class_type":"ImageCompositeMasked","_meta":{"title":"ImageCompositeMasked"}},"180":{"inputs":{"amount":2,"samples":["133",3]},"class_type":"RepeatLatentBatch","_meta":{"title":"Repeat Latent Batch"}},"182":{"inputs":{"bbox_threshold":0.1,"bbox_dilation":0,"crop_factor":3,"drop_size":10,"sub_threshold":0.5,"sub_dilation":0,"sub_bbox_expansion":0,"sam_mask_hint_threshold":0.7,"post_dilation":0,"bbox_detector":["184",0],"image":["372",0]},"class_type":"ImpactSimpleDetectorSEGS","_meta":{"title":"Simple Detector (SEGS)"}},"184":{"inputs":{"model_name":"bbox/hand_yolov8n.pt"},"class_type":"UltralyticsDetectorProvider","_meta":{"title":"UltralyticsDetectorProvider"}},"214":{"inputs":{"x":0,"y":0,"resize_source":false,"destination":["341",0],"source":["132",0],"mask":["369",1]},"class_type":"ImageCompositeMasked","_meta":{"title":"ImageCompositeMasked"}},"305":{"inputs":{"image":["341",0],"mask":["369",1]},"class_type":"BoundedImageCropWithMask_v2_LR","_meta":{"title":"Bounded Image Crop With Mask LR v2"}},"306":{"inputs":{"image":["132",0],"mask":["369",1]},"class_type":"BoundedImageCropWithMask_v2_LR","_meta":{"title":"Bounded Image Crop With Mask LR v2"}},"326":{"inputs":{"any":["306",1]},"class_type":"TensorListStack_LR","_meta":{"title":"Tensor List Stack LR"}},"330":{"inputs":{"blend_factor":0,"feathering":0,"target":["372",0],"target_bounds":["326",0],"source":["381",0]},"class_type":"Bounded_Image_Blend_LR","_meta":{"title":"Bounded Image Blend LR"}},"341":{"inputs":{"amount":2,"image":["372",0]},"class_type":"RepeatImageBatch","_meta":{"title":"RepeatImageBatch"}},"369":{"inputs":{"log_dir":"${outputPath}","threshold":10000,"segs":["182",0],"mask":["372",1]},"class_type":"SegsProcess_LR_v2","_meta":{"title":"Segs Process LR v2"}},"370":{"inputs":{"output_path":"${outputPath}","filename_prefix":"${fileNamePrefix}","extension":"${fileType}","dpi":300,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"${embedWorkflow}","use_time_str":"true","output_as_root":"true","images":["384",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"372":{"inputs":{"image":"${repairImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"Load Image"}},"381":{"inputs":{"image":["14",0]},"class_type":"ConrainImageListToBatch","_meta":{"title":"Conrain Image List To Batch"}},"382":{"inputs":{"ANY":["369",2],"IF_TRUE":["27",0],"IF_FALSE":["214",0]},"class_type":"ConrainIfExecute","_meta":{"title":"If Crop"}},"383":{"inputs":{"ANY":["369",2],"IF_TRUE":["54",0],"IF_FALSE":["388",0]},"class_type":"ConrainIfExecute","_meta":{"title":"If Crop"}},"384":{"inputs":{"ANY":["369",2],"IF_TRUE":["330",0],"IF_FALSE":["381",0]},"class_type":"ConrainIfExecute","_meta":{"title":"If Crop"}},"386":{"inputs":{"expand":2,"incremental_expandrate":0,"tapered_corners":false,"flip_input":false,"blur_radius":2,"lerp_alpha":1,"decay_factor":1,"fill_holes":true,"mask":["369",0]},"class_type":"ConrainGrowMaskWithBlur","_meta":{"title":"Conrain Grow Mask With Blur"}},"387":{"inputs":{"expand":20,"incremental_expandrate":0,"tapered_corners":true,"flip_input":false,"blur_radius":2,"lerp_alpha":1,"decay_factor":1,"fill_holes":true,"mask":["306",2]},"class_type":"ConrainGrowMaskWithBlur","_meta":{"title":"Conrain Grow Mask With Blur"}},"388":{"inputs":{"expand":20,"incremental_expandrate":0,"tapered_corners":true,"flip_input":false,"blur_radius":2,"lerp_alpha":1,"decay_factor":1,"fill_holes":true,"mask":["369",1]},"class_type":"ConrainGrowMaskWithBlur","_meta":{"title":"Conrain Grow Mask With Blur"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":388,"last_link_id":993,"nodes":[{"id":20,"type":"RepeatLatentBatch","pos":[4483.9484930525705,1362.2943074247619],"size":{"0":240,"1":60},"flags":{"collapsed":false},"order":41,"mode":0,"inputs":[{"name":"samples","type":"LATENT","link":27,"label":"samples"}],"outputs":[{"name":"LATENT","type":"LATENT","links":[44],"shape":3,"label":"LATENT","slot_index":0}],"properties":{"Node name for S&R":"RepeatLatentBatch"},"widgets_values":[2]},{"id":65,"type":"Reroute","pos":[3324.8513367491673,943.4054794569452],"size":[75,26],"flags":{},"order":35,"mode":0,"inputs":[{"name":"","type":"*","link":978,"label":""}],"outputs":[{"name":"","type":"*","links":[751,753],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false},"color":"#323","bgcolor":"#535"},{"id":239,"type":"Reroute","pos":[3344.8513367491673,1213.4054794569452],"size":[75,26],"flags":{},"order":36,"mode":0,"inputs":[{"name":"","type":"*","link":982,"label":""}],"outputs":[{"name":"","type":"*","links":[752],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false},"color":"#323","bgcolor":"#535"},{"id":14,"type":"VAEDecode","pos":[4886.951732064077,1273.7131335249285],"size":{"0":210,"1":46},"flags":{},"order":43,"mode":0,"inputs":[{"name":"samples","type":"LATENT","link":19,"label":"samples"},{"name":"vae","type":"VAE","link":62,"label":"vae","slot_index":1}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[971],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"VAEDecode"}},{"id":117,"type":"Reroute","pos":[3515.201687330836,126.61873806313187],"size":[75,26],"flags":{},"order":18,"mode":0,"inputs":[{"name":"","type":"*","link":989,"label":""}],"outputs":[{"name":"","type":"MASK","links":[287],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":116,"type":"Reroute","pos":[3515.201687330836,46.61873806313185],"size":[75,26],"flags":{},"order":13,"mode":0,"inputs":[{"name":"","type":"*","link":532,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[286,830],"label":"","slot_index":0}],"properties":{"showOutputText":false,"horizontal":false}},{"id":42,"type":"Reroute","pos":[4800,750],"size":[75,26],"flags":{},"order":9,"mode":0,"inputs":[{"name":"","type":"*","link":884,"label":""}],"outputs":[{"name":"","type":"VAE","links":[62,76],"label":"","slot_index":0}],"properties":{"showOutputText":false,"horizontal":false}},{"id":43,"type":"Reroute","pos":[4800,710],"size":[75,26],"flags":{},"order":6,"mode":0,"inputs":[{"name":"","type":"*","link":883,"label":""}],"outputs":[{"name":"","type":"MODEL","links":[66],"label":"","slot_index":0}],"properties":{"showOutputText":false,"horizontal":false}},{"id":180,"type":"RepeatLatentBatch","pos":[4665.841351497415,309.2106936256042],"size":{"0":220,"1":60},"flags":{"collapsed":true},"order":22,"mode":0,"inputs":[{"name":"samples","type":"LATENT","link":464,"label":"samples"}],"outputs":[{"name":"LATENT","type":"LATENT","links":[465],"shape":3,"label":"LATENT","slot_index":0}],"properties":{"Node name for S&R":"RepeatLatentBatch"},"widgets_values":[2]},{"id":133,"type":"BrushNet","pos":[4340,10],"size":{"0":315,"1":226},"flags":{},"order":21,"mode":0,"inputs":[{"name":"model","type":"MODEL","link":280,"label":"model"},{"name":"vae","type":"VAE","link":281,"label":"vae"},{"name":"image","type":"IMAGE","link":286,"label":"image"},{"name":"mask","type":"MASK","link":287,"label":"mask"},{"name":"brushnet","type":"BRMODEL","link":282,"label":"brushnet"},{"name":"positive","type":"CONDITIONING","link":283,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":284,"label":"negative"}],"outputs":[{"name":"model","type":"MODEL","links":[274],"shape":3,"slot_index":0,"label":"model"},{"name":"positive","type":"CONDITIONING","links":[275,294],"shape":3,"slot_index":1,"label":"positive"},{"name":"negative","type":"CONDITIONING","links":[276,295],"shape":3,"slot_index":2,"label":"negative"},{"name":"latent","type":"LATENT","links":[464],"shape":3,"slot_index":3,"label":"latent"}],"properties":{"Node name for S&R":"BrushNet"},"widgets_values":[1,0,10000]},{"id":132,"type":"VAEDecode","pos":[4840,420],"size":{"0":210,"1":46},"flags":{},"order":24,"mode":0,"inputs":[{"name":"samples","type":"LATENT","link":278,"label":"samples"},{"name":"vae","type":"VAE","link":279,"label":"vae"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[869],"shape":3,"slot_index":0,"label":"IMAGE"}],"properties":{"Node name for S&R":"VAEDecode"}},{"id":128,"type":"CheckpointLoaderSimple","pos":[3670,10],"size":{"0":360,"1":100},"flags":{},"order":0,"mode":0,"outputs":[{"name":"MODEL","type":"MODEL","links":[280,883],"shape":3,"slot_index":0,"label":"MODEL"},{"name":"CLIP","type":"CLIP","links":[272,273],"shape":3,"slot_index":1,"label":"CLIP"},{"name":"VAE","type":"VAE","links":[279,281,884],"shape":3,"slot_index":2,"label":"VAE"}],"properties":{"Node name for S&R":"CheckpointLoaderSimple"},"widgets_values":["sdxl/zavychromaxl_v60.safetensors"]},{"id":28,"type":"VAEEncode","pos":[4494.828848127193,1061.3915526843884],"size":{"0":210,"1":46},"flags":{},"order":38,"mode":0,"inputs":[{"name":"pixels","type":"IMAGE","link":753,"label":"pixels"},{"name":"vae","type":"VAE","link":76,"label":"vae"}],"outputs":[{"name":"LATENT","type":"LATENT","links":[23],"shape":3,"label":"LATENT","slot_index":0}],"properties":{"Node name for S&R":"VAEEncode"}},{"id":305,"type":"BoundedImageCropWithMask_v2_LR","pos":[1190,910],"size":{"0":285.6000061035156,"1":86},"flags":{},"order":19,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":892,"label":"image"},{"name":"mask","type":"MASK","link":762,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[764],"shape":3,"label":"IMAGE","slot_index":0},{"name":"IMAGE_BOUNDS","type":"IMAGE_BOUNDS","links":null,"shape":3,"label":"IMAGE_BOUNDS","slot_index":1},{"name":"MASK","type":"MASK","links":[765],"shape":3,"label":"MASK","slot_index":2},{"name":"SCALE_BY","type":"FLOAT","links":null,"shape":3,"label":"SCALE_BY"}],"properties":{"Node name for S&R":"BoundedImageCropWithMask_v2_LR"}},{"id":168,"type":"ImageCompositeMasked","pos":[1600,910],"size":{"0":315,"1":146},"flags":{},"order":28,"mode":0,"inputs":[{"name":"destination","type":"IMAGE","link":764,"label":"destination"},{"name":"source","type":"IMAGE","link":768,"label":"source"},{"name":"mask","type":"MASK","link":765,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[446],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageCompositeMasked"},"widgets_values":[0,0,false]},{"id":17,"type":"SetLatentNoiseMask","pos":[4479.484068902471,1180.488129491812],"size":{"0":210,"1":46},"flags":{"collapsed":false},"order":40,"mode":0,"inputs":[{"name":"samples","type":"LATENT","link":23,"label":"samples"},{"name":"mask","type":"MASK","link":752,"label":"mask"}],"outputs":[{"name":"LATENT","type":"LATENT","links":[27],"shape":3,"label":"LATENT","slot_index":0}],"properties":{"Node name for S&R":"SetLatentNoiseMask"}},{"id":127,"type":"BrushNetLoader","pos":[3789.2000122070312,530],"size":{"0":440,"1":82},"flags":{},"order":1,"mode":0,"outputs":[{"name":"brushnet","type":"BRMODEL","links":[282],"shape":3,"slot_index":0,"label":"brushnet"}],"properties":{"Node name for S&R":"BrushNetLoader"},"widgets_values":["brushnet_xl/diffusion_pytorch_model.safetensors","float16"]},{"id":130,"type":"CLIPTextEncode","pos":[3890,350],"size":{"0":339.20001220703125,"1":96.39999389648438},"flags":{},"order":8,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":273,"label":"clip"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[284],"shape":3,"slot_index":0,"label":"CONDITIONING"}],"properties":{"Node name for S&R":"CLIPTextEncode"},"widgets_values":["nail polish, jewelry, accessories, gloves, wristbands, coverings, text, watermark,"],"color":"#322","bgcolor":"#533"},{"id":129,"type":"CLIPTextEncode","pos":[3890,200],"size":{"0":339.20001220703125,"1":96.39999389648438},"flags":{},"order":7,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":272,"label":"clip"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[283],"shape":3,"slot_index":0,"label":"CONDITIONING"}],"properties":{"Node name for S&R":"CLIPTextEncode"},"widgets_values":["hand of young fashion model, high quality, high detail, realistic,"],"color":"#232","bgcolor":"#353"},{"id":54,"type":"MaskUpscale_LR","pos":[2020,1180],"size":{"0":320,"1":60},"flags":{},"order":32,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":991,"label":"mask"},{"name":"scale_by","type":"FLOAT","link":880,"widget":{"name":"scale_by"},"label":"scale_by"}],"outputs":[{"name":"MASK","type":"MASK","links":[980],"shape":3,"label":"MASK","slot_index":0}],"properties":{"Node name for S&R":"MaskUpscale_LR"},"widgets_values":[4]},{"id":27,"type":"ImageScaleBy","pos":[2030,910],"size":{"0":320,"1":80},"flags":{},"order":31,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":446,"label":"image"},{"name":"scale_by","type":"FLOAT","link":879,"widget":{"name":"scale_by"},"label":"scale_by"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[976],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageScaleBy"},"widgets_values":["lanczos",4]},{"id":214,"type":"ImageCompositeMasked","pos":[1180,1650],"size":{"0":315,"1":146},"flags":{},"order":26,"mode":0,"inputs":[{"name":"destination","type":"IMAGE","link":871,"label":"destination"},{"name":"source","type":"IMAGE","link":518,"label":"source"},{"name":"mask","type":"MASK","link":519,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[977],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageCompositeMasked"},"widgets_values":[0,0,false]},{"id":48,"type":"Reroute","pos":[980,920],"size":[75,26],"flags":{},"order":25,"mode":0,"inputs":[{"name":"","type":"*","link":869,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[518,766],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":49,"type":"Reroute","pos":[970,1160],"size":[75,26],"flags":{},"order":17,"mode":0,"inputs":[{"name":"","type":"*","link":941,"label":""}],"outputs":[{"name":"","type":"MASK","links":[519,762,767,992],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":306,"type":"BoundedImageCropWithMask_v2_LR","pos":[1190,1220],"size":{"0":285.6000061035156,"1":86},"flags":{},"order":27,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":766,"label":"image"},{"name":"mask","type":"MASK","link":767,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[768],"shape":3,"label":"IMAGE","slot_index":0},{"name":"IMAGE_BOUNDS","type":"IMAGE_BOUNDS","links":[823],"shape":3,"label":"IMAGE_BOUNDS","slot_index":1},{"name":"MASK","type":"MASK","links":[990],"shape":3,"label":"MASK","slot_index":2},{"name":"SCALE_BY","type":"FLOAT","links":[879,880],"shape":3,"label":"SCALE_BY","slot_index":3}],"properties":{"Node name for S&R":"BoundedImageCropWithMask_v2_LR"}},{"id":131,"type":"KSampler","pos":[4810,10],"size":{"0":320,"1":262},"flags":{},"order":23,"mode":0,"inputs":[{"name":"model","type":"MODEL","link":274,"label":"model"},{"name":"positive","type":"CONDITIONING","link":275,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":276,"label":"negative"},{"name":"latent_image","type":"LATENT","link":465,"slot_index":3,"label":"latent_image"}],"outputs":[{"name":"LATENT","type":"LATENT","links":[278],"shape":3,"slot_index":0,"label":"LATENT"}],"properties":{"Node name for S&R":"KSampler"},"widgets_values":["${seed2}","randomize",20,5,"dpmpp_3m_sde_gpu","exponential",1]},{"id":31,"type":"KSampler","pos":[4794.828848127193,921.3915526843886],"size":{"0":315,"1":262},"flags":{},"order":42,"mode":0,"inputs":[{"name":"model","type":"MODEL","link":66,"label":"model"},{"name":"positive","type":"CONDITIONING","link":462,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":463,"label":"negative","slot_index":2},{"name":"latent_image","type":"LATENT","link":44,"label":"latent_image"}],"outputs":[{"name":"LATENT","type":"LATENT","links":[19],"shape":3,"slot_index":0,"label":"LATENT"}],"properties":{"Node name for S&R":"KSampler"},"widgets_values":["${seed}","randomize",20,3.5,"dpmpp_3m_sde_gpu","exponential",0.65]},{"id":326,"type":"TensorListStack_LR","pos":[2600,1980],"size":{"0":210,"1":26},"flags":{},"order":29,"mode":0,"inputs":[{"name":"any","type":"*","link":823,"label":"any"}],"outputs":[{"name":"*","type":"*","links":[834],"shape":3,"label":"*","slot_index":0}],"properties":{"Node name for S&R":"TensorListStack_LR"}},{"id":341,"type":"RepeatImageBatch","pos":[990,700],"size":{"0":230,"1":60},"flags":{},"order":14,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":881,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[871,892],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"RepeatImageBatch"},"widgets_values":[2]},{"id":13,"type":"ControlNetLoader","pos":[4010,1280],"size":{"0":380,"1":60},"flags":{"collapsed":false},"order":2,"mode":0,"outputs":[{"name":"CONTROL_NET","type":"CONTROL_NET","links":[17],"shape":3,"label":"CONTROL_NET"}],"properties":{"Node name for S&R":"ControlNetLoader"},"widgets_values":["xl/sai_xl_depth_256lora.safetensors"]},{"id":12,"type":"ControlNetApplyAdvanced","pos":[4040,940],"size":{"0":340,"1":180},"flags":{},"order":39,"mode":0,"inputs":[{"name":"positive","type":"CONDITIONING","link":294,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":295,"label":"negative"},{"name":"control_net","type":"CONTROL_NET","link":17,"label":"control_net","slot_index":2},{"name":"image","type":"IMAGE","link":18,"label":"image"}],"outputs":[{"name":"positive","type":"CONDITIONING","links":[462],"shape":3,"label":"positive","slot_index":0},{"name":"negative","type":"CONDITIONING","links":[463],"shape":3,"label":"negative","slot_index":1}],"properties":{"Node name for S&R":"ControlNetApplyAdvanced"},"widgets_values":[0.65,0,1]},{"id":196,"type":"Reroute","pos":[1480,50],"size":[75,26],"flags":{},"order":10,"mode":0,"inputs":[{"name":"","type":"*","link":969,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[486,532,881],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":197,"type":"Reroute","pos":[1480,100],"size":[75,26],"flags":{},"order":11,"mode":0,"inputs":[{"name":"","type":"*","link":970,"label":""}],"outputs":[{"name":"","type":"MASK","links":[938],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":370,"type":"ConrainImageSave","pos":[4440,1620],"size":{"0":320,"1":270},"flags":{},"order":47,"mode":0,"inputs":[{"name":"images","type":"IMAGE","link":986,"label":"images"},{"name":"output_path","type":"STRING","link":968,"widget":{"name":"output_path"},"label":"output_path"}],"outputs":[{"name":"image_cnt","type":"INT","links":null,"shape":3,"label":"image_cnt"}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["${outputPath}","${fileNamePrefix}","${fileType}",300,100,"true","false","${embedWorkflow}","true","true"]},{"id":379,"type":"PrimitiveNode","pos":[2420,420],"size":{"0":210,"1":60},"flags":{},"order":3,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[967,968],"slot_index":0,"widget":{"name":"log_dir"},"label":"STRING"}],"title":"Output Path","properties":{"Run widget replace on values":false},"widgets_values":["${outputPath}"]},{"id":11,"type":"MeshGraphormer-DepthMapPreprocessor","pos":[3540,940],"size":{"0":330,"1":180},"flags":{},"order":37,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":751,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[18],"shape":3,"label":"IMAGE","slot_index":0},{"name":"INPAINTING_MASK","type":"MASK","links":[],"shape":3,"label":"INPAINTING_MASK","slot_index":1}],"properties":{"Node name for S&R":"MeshGraphormer-DepthMapPreprocessor"},"widgets_values":[50,512,"based_on_depth",50,88]},{"id":330,"type":"Bounded_Image_Blend_LR","pos":[3570,1930],"size":{"0":315,"1":122},"flags":{},"order":45,"mode":0,"inputs":[{"name":"target","type":"IMAGE","link":830,"label":"target"},{"name":"target_bounds","type":"IMAGE_BOUNDS","link":834,"label":"target_bounds"},{"name":"source","type":"IMAGE","link":973,"label":"source"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[984],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"Bounded_Image_Blend_LR"},"widgets_values":[0,0]},{"id":383,"type":"ConrainIfExecute","pos":[3030,1220],"size":{"0":210,"1":66},"flags":{},"order":34,"mode":0,"inputs":[{"name":"ANY","type":"*","link":979,"label":"ANY"},{"name":"IF_TRUE","type":"*","link":980,"label":"IF_TRUE"},{"name":"IF_FALSE","type":"*","link":993,"label":"IF_FALSE"}],"outputs":[{"name":"?","type":"*","links":[982],"shape":3,"label":"?","slot_index":0}],"title":"If Crop","properties":{"Node name for S&R":"ConrainIfExecute"}},{"id":382,"type":"ConrainIfExecute","pos":[3020,940],"size":{"0":210,"1":66},"flags":{},"order":33,"mode":0,"inputs":[{"name":"ANY","type":"*","link":975,"label":"ANY"},{"name":"IF_TRUE","type":"*","link":976,"label":"IF_TRUE"},{"name":"IF_FALSE","type":"*","link":977,"label":"IF_FALSE"}],"outputs":[{"name":"?","type":"*","links":[978],"shape":3,"label":"?","slot_index":0}],"title":"If Crop","properties":{"Node name for S&R":"ConrainIfExecute"}},{"id":381,"type":"ConrainImageListToBatch","pos":[3560,1660],"size":{"0":226.8000030517578,"1":26},"flags":{},"order":44,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":971,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[973,985],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ConrainImageListToBatch"}},{"id":369,"type":"SegsProcess_LR_v2","pos":[2780,10],"size":{"0":320,"1":140},"flags":{},"order":15,"mode":0,"inputs":[{"name":"segs","type":"SEGS","link":939,"label":"segs"},{"name":"mask","type":"MASK","link":938,"label":"mask"},{"name":"log_dir","type":"STRING","link":967,"widget":{"name":"log_dir"},"label":"log_dir"}],"outputs":[{"name":"Mask","type":"MASK","links":[988],"shape":3,"label":"Mask","slot_index":0},{"name":"Masks","type":"MASK","links":[941],"shape":6,"label":"Masks","slot_index":1},{"name":"If_Crop","type":"BOOLEAN","links":[975,979,983],"shape":3,"label":"If_Crop","slot_index":2},{"name":"If_Split","type":"BOOLEAN","links":null,"shape":3,"label":"If_Split"}],"properties":{"Node name for S&R":"SegsProcess_LR_v2"},"widgets_values":["${outputPath}",10000]},{"id":386,"type":"ConrainGrowMaskWithBlur","pos":[3080,270],"size":{"0":315,"1":246},"flags":{},"order":16,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":988,"label":"mask"}],"outputs":[{"name":"mask","type":"MASK","links":[989],"shape":3,"label":"mask","slot_index":0},{"name":"mask_inverted","type":"MASK","links":null,"shape":3,"label":"mask_inverted"}],"properties":{"Node name for S&R":"ConrainGrowMaskWithBlur"},"widgets_values":[2,0,false,false,2,1,1,true]},{"id":387,"type":"ConrainGrowMaskWithBlur","pos":[1600,1190],"size":{"0":315,"1":246},"flags":{},"order":30,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":990,"label":"mask"}],"outputs":[{"name":"mask","type":"MASK","links":[991],"shape":3,"label":"mask","slot_index":0},{"name":"mask_inverted","type":"MASK","links":null,"shape":3,"label":"mask_inverted"}],"properties":{"Node name for S&R":"ConrainGrowMaskWithBlur"},"widgets_values":[20,0,true,false,2,1,1,true]},{"id":388,"type":"ConrainGrowMaskWithBlur","pos":[1170,1960],"size":{"0":315,"1":246},"flags":{},"order":20,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":992,"label":"mask"}],"outputs":[{"name":"mask","type":"MASK","links":[993],"shape":3,"label":"mask","slot_index":0},{"name":"mask_inverted","type":"MASK","links":null,"shape":3,"label":"mask_inverted"}],"properties":{"Node name for S&R":"ConrainGrowMaskWithBlur"},"widgets_values":[20,0,true,false,2,1,1,true]},{"id":184,"type":"UltralyticsDetectorProvider","pos":[1690.7229396812966,14.346958681616934],"size":{"0":399.5620422363281,"1":87.12212371826172},"flags":{},"order":4,"mode":0,"outputs":[{"name":"BBOX_DETECTOR","type":"BBOX_DETECTOR","links":[470],"shape":3,"label":"BBOX_DETECTOR","slot_index":0},{"name":"SEGM_DETECTOR","type":"SEGM_DETECTOR","links":null,"shape":3,"label":"SEGM_DETECTOR"}],"properties":{"Node name for S&R":"UltralyticsDetectorProvider"},"widgets_values":["bbox/hand_yolov8n.pt"]},{"id":372,"type":"LoadImage","pos":[930,30],"size":{"0":490,"1":450},"flags":{},"order":5,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[969],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":[970],"shape":3,"label":"MASK","slot_index":1}],"properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${repairImage}","image"]},{"id":182,"type":"ImpactSimpleDetectorSEGS","pos":[2230,10],"size":{"0":405.86236572265625,"1":311.0417175292969},"flags":{},"order":12,"mode":0,"inputs":[{"name":"bbox_detector","type":"BBOX_DETECTOR","link":470,"label":"bbox_detector"},{"name":"image","type":"IMAGE","link":486,"label":"image"},{"name":"sam_model_opt","type":"SAM_MODEL","link":null,"label":"sam_model_opt","slot_index":2},{"name":"segm_detector_opt","type":"SEGM_DETECTOR","link":null,"label":"segm_detector_opt","slot_index":3}],"outputs":[{"name":"SEGS","type":"SEGS","links":[939],"shape":3,"label":"SEGS","slot_index":0}],"properties":{"Node name for S&R":"ImpactSimpleDetectorSEGS"},"widgets_values":[0.1,0,3,10,0.5,0,0,0.7,0]},{"id":384,"type":"ConrainIfExecute","pos":[4060,1620],"size":{"0":210,"1":66},"flags":{},"order":46,"mode":0,"inputs":[{"name":"ANY","type":"*","link":983,"label":"ANY"},{"name":"IF_TRUE","type":"*","link":984,"label":"IF_TRUE"},{"name":"IF_FALSE","type":"*","link":985,"label":"IF_FALSE"}],"outputs":[{"name":"?","type":"*","links":[986],"shape":3,"label":"?","slot_index":0}],"title":"If Crop","properties":{"Node name for S&R":"ConrainIfExecute"}}],"links":[[17,13,0,12,2,"CONTROL_NET"],[18,11,0,12,3,"IMAGE"],[19,31,0,14,0,"LATENT"],[23,28,0,17,0,"LATENT"],[27,17,0,20,0,"LATENT"],[44,20,0,31,3,"LATENT"],[62,42,0,14,1,"VAE"],[66,43,0,31,0,"MODEL"],[76,42,0,28,1,"VAE"],[272,128,1,129,0,"CLIP"],[273,128,1,130,0,"CLIP"],[274,133,0,131,0,"MODEL"],[275,133,1,131,1,"CONDITIONING"],[276,133,2,131,2,"CONDITIONING"],[278,131,0,132,0,"LATENT"],[279,128,2,132,1,"VAE"],[280,128,0,133,0,"MODEL"],[281,128,2,133,1,"VAE"],[282,127,0,133,4,"BRMODEL"],[283,129,0,133,5,"CONDITIONING"],[284,130,0,133,6,"CONDITIONING"],[286,116,0,133,2,"IMAGE"],[287,117,0,133,3,"MASK"],[294,133,1,12,0,"CONDITIONING"],[295,133,2,12,1,"CONDITIONING"],[446,168,0,27,0,"IMAGE"],[462,12,0,31,1,"CONDITIONING"],[463,12,1,31,2,"CONDITIONING"],[464,133,3,180,0,"LATENT"],[465,180,0,131,3,"LATENT"],[470,184,0,182,0,"BBOX_DETECTOR"],[486,196,0,182,1,"IMAGE"],[518,48,0,214,1,"IMAGE"],[519,49,0,214,2,"MASK"],[532,196,0,116,0,"*"],[751,65,0,11,0,"IMAGE"],[752,239,0,17,1,"MASK"],[753,65,0,28,0,"IMAGE"],[762,49,0,305,1,"MASK"],[764,305,0,168,0,"IMAGE"],[765,305,2,168,2,"MASK"],[766,48,0,306,0,"IMAGE"],[767,49,0,306,1,"MASK"],[768,306,0,168,1,"IMAGE"],[823,306,1,326,0,"*"],[830,116,0,330,0,"IMAGE"],[834,326,0,330,1,"IMAGE_BOUNDS"],[869,132,0,48,0,"*"],[871,341,0,214,0,"IMAGE"],[879,306,3,27,1,"FLOAT"],[880,306,3,54,1,"FLOAT"],[881,196,0,341,0,"IMAGE"],[883,128,0,43,0,"*"],[884,128,2,42,0,"*"],[892,341,0,305,0,"IMAGE"],[938,197,0,369,1,"MASK"],[939,182,0,369,0,"SEGS"],[941,369,1,49,0,"*"],[967,379,0,369,2,"STRING"],[968,379,0,370,1,"STRING"],[969,372,0,196,0,"*"],[970,372,1,197,0,"*"],[971,14,0,381,0,"IMAGE"],[973,381,0,330,2,"IMAGE"],[975,369,2,382,0,"*"],[976,27,0,382,1,"*"],[977,214,0,382,2,"*"],[978,382,0,65,0,"*"],[979,369,2,383,0,"*"],[980,54,0,383,1,"*"],[982,383,0,239,0,"*"],[983,369,2,384,0,"*"],[984,330,0,384,1,"*"],[985,381,0,384,2,"*"],[986,384,0,370,0,"IMAGE"],[988,369,0,386,0,"MASK"],[989,386,0,117,0,"*"],[990,306,2,387,0,"MASK"],[991,387,0,54,0,"MASK"],[992,49,0,388,0,"MASK"],[993,388,0,383,2,"*"]],"groups":[],"config":{},"extra":{"ds":{"scale":0.555991731349224,"offset":[-615.6081089366171,-347.81619291821994]}},"version":0.4,"widget_idx_map":{"31":{"seed":0,"sampler_name":4,"scheduler":5},"131":{"seed":0,"sampler_name":4,"scheduler":5}},"seed_widgets":{"31":0,"131":0}}}}}',null,now(),0,'手部修复流程参数配置');

update permission set `config` = 'ADMIN' where action like '/materialModel/assignTo';
update permission set `config` = 'ADMIN' where action like '/sys/all';
update permission set `config` = 'ADMIN' where action like '/sys/update';
update permission set `config` = 'ADMIN' where action like '/sys/add';
update permission set `config` = 'ADMIN' where action like '/sys/delete';

-- 0713

ALTER TABLE `creative_element` ADD COLUMN `belong` VARCHAR(16) NOT NULL DEFAULT 'SYSTEM' COMMENT '归属，SYSTEM、CUSTOM';
ALTER TABLE `creative_element` ADD COLUMN `user_id` INT UNSIGNED NULL COMMENT '归属主账号';
ALTER TABLE `creative_element` ADD COLUMN `operator_id` INT UNSIGNED NULL COMMENT '操作员id';
ALTER TABLE `creative_element` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER TABLE `creative_element` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER TABLE `creative_batch` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER TABLE `material_model` MODIFY COLUMN `show_image` VARCHAR(512) NULL COMMENT '图片url';
ALTER table `server` MODIFY COLUMN `type` VARCHAR(16) NULL COMMENT '类型，生图、Lora训练、文件服务';
ALTER table `server` ADD COLUMN `status` VARCHAR(16) NOT NULL COMMENT '状态，可用、禁用、空闲、繁忙';
ALTER TABLE `creative_batch` MODIFY COLUMN `model_id` INT UNSIGNED NULL COMMENT '模型id';
ALTER TABLE `creative_batch` MODIFY COLUMN `model_type` VARCHAR(32) NULL DEFAULT 'CUSTOM' COMMENT '模型类型，SYSTEM、CUSTOM';
ALTER TABLE `creative_task` MODIFY COLUMN `model_id` INT UNSIGNED NULL COMMENT '模型id';

insert into creative_element(name, `level`, parent_id, config_key, tags, ext_tags, ext_info, show_image, `order`, memo, `type`, deleted) values
('服装款式', 1, null, 'CLOTH_STYLE', null, null, null, null, 3, null, null, 0),
('服装模特参考图', 1, null, 'REFER', null, null, null, null, 4, null, null, 0);

 insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
 ('CLOTH_STYLE_TYPE_CFG', '[{"code":"Male","name":"男款"}, {"code":"Female","name":"女款"}]','', now(), 0, '服装款式分类');

 insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
 ('CLOTH_COLOR_TYPE_CFG', '["#FFFFFF","#E9EEF0","#ACACAC","#262626","#E9DAD2","#DEBDAD","#BE807D","#662B3A","#FCECD0","#EBC191","#EF4656","#8F6EB2","#929F7D","#345C4D","#75BFE2","#4B71AF"]','', now(), 0, '服装颜色分类');

 insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
 ('LOGO_LOCATION_TYPE_CFG', '[{"code":"front","name":"正面印花"}, {"code":"back","name":"背面印花"}]','', now(), 0, '印花位置分类');

insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('LOGO_COMBINE_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"13":{"inputs":{"text":["195",0]},"class_type":"ShowText|pysssss","_meta":{"title":"Show Text 🐍"}},"23":{"inputs":{"target_size":1024,"image":["81",0]},"class_type":"UpscaleSizeCalculator","_meta":{"title":"Upscale Size Calculator"}},"24":{"inputs":{"upscale_method":"nearest-exact","scale_by":["23",0],"image":["81",0]},"class_type":"ImageScaleBy","_meta":{"title":"Upscale Image By"}},"65":{"inputs":{"input1":["66",0],"input2":["195",0]},"class_type":"MergeString","_meta":{"title":"Merge String"}},"66":{"inputs":{"text":"${FACE.extInfo.modelDesc} wearing "},"class_type":"CR Text","_meta":{"title":"模特prompt"}},"76":{"inputs":{"text":["65",0]},"class_type":"ShowText|pysssss","_meta":{"title":"Show Text 🐍"}},"79":{"inputs":{"weight_dtype":"float16"},"class_type":"PipelineLoader","_meta":{"title":"Load IDM-VTON Pipeline"}},"81":{"inputs":{"image":"${logoImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"输入衣服"}},"83":{"inputs":{"model_name":"sam_hq_vit_h (2.57GB)"},"class_type":"SAMModelLoader (segment anything)","_meta":{"title":"SAMModelLoader (segment anything)"}},"84":{"inputs":{"model_name":"GroundingDINO_SwinB (938MB)"},"class_type":"GroundingDinoModelLoader (segment anything)","_meta":{"title":"GroundingDinoModelLoader (segment anything)"}},"85":{"inputs":{"prompt":"t-shirt","background":"black","threshold":0.3,"sam_model":["83",0],"grounding_dino_model":["84",0],"image":["147",0]},"class_type":"GroundingDinoSAMSegment (segment anything)","_meta":{"title":"处理中"}},"86":{"inputs":{"mask":["166",0]},"class_type":"MaskToImage","_meta":{"title":"Convert Mask to Image"}},"88":{"inputs":{"model":"densepose_r50_fpn_dl.torchscript","cmap":"Parula (CivitAI)","resolution":768,"image":["147",0]},"class_type":"DensePosePreprocessor","_meta":{"title":"DensePose Estimator"}},"90":{"inputs":{"garment_description":["65",0],"negative_prompt":"monochrome, lowres, bad anatomy, worst quality, low quality","width":["144",0],"height":["144",1],"num_inference_steps":20,"guidance_scale":4,"strength":1,"seed":"${seed}","pipeline":["79",0],"human_img":["147",0],"pose_img":["88",0],"mask_img":["86",0],"garment_img":["81",0]},"class_type":"IDM-VTON","_meta":{"title":"处理中"}},"113":{"inputs":{"model_name":"bbox/face_yolov8m.pt"},"class_type":"UltralyticsDetectorProvider","_meta":{"title":"UltralyticsDetectorProvider"}},"114":{"inputs":{"model_name":"sam_vit_b_01ec64.pth","device_mode":"Prefer GPU"},"class_type":"SAMLoader","_meta":{"title":"SAMLoader (Impact)"}},"115":{"inputs":{"text":"realistic, ${FACE.extTags}","token_normalization":"none","weight_interpretation":"A1111","clip":["125",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"修脸"}},"116":{"inputs":{"text":"EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,","token_normalization":"none","weight_interpretation":"A1111","clip":["125",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"124":{"inputs":{"guide_size":384,"guide_size_for":true,"max_size":512,"seed":"${seed2}","steps":8,"cfg":"3","sampler_name":"euler","scheduler":"normal","denoise":0.4,"feather":5,"noise_mask":true,"force_inpaint":true,"bbox_threshold":0.5,"bbox_dilation":500,"bbox_crop_factor":3,"sam_detection_hint":"center-1","sam_dilation":0,"sam_threshold":0.93,"sam_bbox_expansion":0,"sam_mask_hint_threshold":0.7,"sam_mask_hint_use_negative":"False","drop_size":10,"wildcard":"","cycle":1,"top_k":1,"inpaint_model":0,"noise_mask_feather":0,"image":["90",0],"model":["125",0],"clip":["125",1],"vae":["125",2],"positive":["115",0],"negative":["116",0],"bbox_detector":["113",0],"sam_model_opt":["114",0]},"class_type":"FaceDetailer","_meta":{"title":"处理中"}},"125":{"inputs":{"ckpt_name":"真实系：majicmixRealistic_v7.safetensors"},"class_type":"CheckpointLoaderSimple","_meta":{"title":"Load Checkpoint"}},"139":{"inputs":{"target_size":1024,"image":["154",0]},"class_type":"UpscaleSizeCalculator","_meta":{"title":"Upscale Size Calculator"}},"140":{"inputs":{"upscale_method":"nearest-exact","scale_by":["139",0],"image":["154",0]},"class_type":"ImageScaleBy","_meta":{"title":"Upscale Image By"}},"144":{"inputs":{"image":["147",0]},"class_type":"GetImageSize+","_meta":{"title":"🔧 Get Image Size"}},"147":{"inputs":{"width":["149",0],"height":["152",0],"position":"center","x_offset":0,"y_offset":0,"image":["140",0]},"class_type":"ImageCrop+","_meta":{"title":"🔧 Image Crop"}},"148":{"inputs":{"expression":"a/8","a":["150",0]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"149":{"inputs":{"expression":"a*8","a":["148",0]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"150":{"inputs":{"image":["140",0]},"class_type":"GetImageSize+","_meta":{"title":"🔧 Get Image Size"}},"151":{"inputs":{"expression":"a/8","a":["150",1]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"152":{"inputs":{"expression":"a*8","a":["151",0]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"154":{"inputs":{"image":"${referImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"输入模特"}},"166":{"inputs":{"expand":20,"tapered_corners":true,"mask":["85",1]},"class_type":"GrowMask","_meta":{"title":"GrowMask"}},"168":{"inputs":{"enabled":true,"face_restore_visibility":1,"codeformer_weight":0.5,"detect_gender_input":"no","detect_gender_source":"no","input_faces_index":"0","source_faces_index":"0","console_log_level":1,"keep_largest":"yes","input_image":["124",0],"swap_model":["169",0],"facedetection":["169",1],"face_restore_model":["169",2],"faceparse_model":["169",3],"source_image":["170",0]},"class_type":"ConrainReActorFaceSwap","_meta":{"title":"处理中"}},"169":{"inputs":{"swap_model":"inswapper_128.onnx","facedetection_model":"retinaface_resnet50","face_restore_model":"GFPGANv1.4.pth","parse_model":"parsenet"},"class_type":"LoadConrainReactorModels","_meta":{"title":"Load ConrainReactor Models"}},"170":{"inputs":{"image":"${FACE.extInfo.faceImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"模特脸"}},"174":{"inputs":{"text":"${CLOTH_STYLE.tags}"},"class_type":"CR Text","_meta":{"title":"衣服prompt"}},"175":{"inputs":{"ANY":["190",0],"IF_TRUE":["179",0],"IF_FALSE":["178",0]},"class_type":"ConrainIfExecute","_meta":{"title":"Conrain If"}},"176":{"inputs":{"text":["174",0]},"class_type":"CR Text Length","_meta":{"title":"🔤 CR Text Length"}},"178":{"inputs":{"text":"I want to use stable diffusion to generate this dress. Please describe this dress in detail, including the dress style, length, slenderness, collar, pattern, pattern, and text.\n\nExample:\n\nA white short-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a regular length. The t-shirt has a yellow flower graphic on the center.\n\nA black long-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a short length. The t-shirt has a text 【Hello】.\n\nA green long-sleeve t-shirt. The t-shirt has a  shirt collar. The t-shirt has a mid length."},"class_type":"CR Text","_meta":{"title":"衣服描述prompt"}},"179":{"inputs":{"text":""},"class_type":"CR Text","_meta":{"title":"空"}},"190":{"inputs":{"cmp":"a > b","a":["176",0],"b":["194",0]},"class_type":"ImpactCompare","_meta":{"title":"ImpactCompare"}},"194":{"inputs":{"seed":0},"class_type":"CR Seed","_meta":{"title":"0"}},"195":{"inputs":{"input1":["174",0],"input2":["199",0]},"class_type":"MergeString","_meta":{"title":"Merge String"}},"198":{"inputs":{"text":"I want to use stable diffusion to generate this dress. Please describe this dress in detail, including the dress style, length, slenderness, collar, pattern, pattern, and text.\n\nExample:\n\nA white short-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a regular length. The t-shirt has a yellow flower graphic on the center.\n\nA black long-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a short length. The t-shirt has a text 【Hello】.\n\nA green long-sleeve t-shirt. The t-shirt has a  shirt collar. The t-shirt has a mid length.","anything":["175",0]},"class_type":"easy showAnything","_meta":{"title":"Show Any"}},"199":{"inputs":{"prompt":["175",0],"llm_model":"default","image_list":["24",0]},"class_type":"LLModel","_meta":{"title":"llmodel"}},"202":{"inputs":{"text":"${outputPath}","text_b":"${fileNamePrefix}","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"203":{"inputs":{"text":["65",0],"path":["205",0],"filename_prefix":["202",1],"filename_delimiter":"_","filename_number_padding":4},"class_type":"Save Text File","_meta":{"title":"Save Text File"}},"204":{"inputs":{"output_path":["202",0],"filename_prefix":["202",1],"extension":"jpg","dpi":100,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"false","use_time_str":"true","output_as_root":"true","images":["168",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"205":{"inputs":{"delimiter":"/","clean_whitespace":"false","text_a":["206",0],"text_b":["202",0]},"class_type":"Text Concatenate","_meta":{"title":"Text Concatenate"}},"206":{"inputs":{"string":"output"},"class_type":"String to Text","_meta":{"title":"String to Text"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":207,"last_link_id":302,"nodes":[{"id":113,"type":"UltralyticsDetectorProvider","pos":[4629.230383548338,3416.687317462742],"size":{"0":315,"1":78},"flags":{},"order":0,"mode":0,"outputs":[{"name":"BBOX_DETECTOR","type":"BBOX_DETECTOR","links":[176],"shape":3,"label":"BBOX_DETECTOR","slot_index":0},{"name":"SEGM_DETECTOR","type":"SEGM_DETECTOR","links":[],"shape":3,"label":"SEGM_DETECTOR","slot_index":1}],"properties":{"Node name for S&R":"UltralyticsDetectorProvider"},"widgets_values":["bbox/face_yolov8m.pt"]},{"id":114,"type":"SAMLoader","pos":[4639.230383548338,3576.687317462743],"size":{"0":315,"1":82},"flags":{},"order":1,"mode":0,"outputs":[{"name":"SAM_MODEL","type":"SAM_MODEL","links":[177],"shape":3,"label":"SAM_MODEL","slot_index":0}],"properties":{"Node name for S&R":"SAMLoader"},"widgets_values":["sam_vit_b_01ec64.pth","Prefer GPU"]},{"id":116,"type":"BNK_CLIPTextEncodeAdvanced","pos":[4607.23038354834,2929.687317462743],"size":{"0":400,"1":200},"flags":{},"order":23,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":159,"label":"clip"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[175],"shape":3,"label":"CONDITIONING","slot_index":0}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,","none","A1111"]},{"id":23,"type":"UpscaleSizeCalculator","pos":[4596.922008572657,-591.2543406117027],"size":{"0":210,"1":120},"flags":{},"order":29,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":52,"label":"image","slot_index":0}],"outputs":[{"name":"rescale_factor","type":"FLOAT","links":[28],"shape":3,"label":"rescale_factor","slot_index":0},{"name":"rescale_width","type":"INT","links":null,"shape":3,"label":"rescale_width"},{"name":"recover_factor","type":"FLOAT","links":null,"shape":3,"label":"recover_factor"},{"name":"recover_width","type":"INT","links":null,"shape":3,"label":"recover_width"}],"properties":{"Node name for S&R":"UpscaleSizeCalculator"},"widgets_values":[1024]},{"id":92,"type":"Reroute","pos":[4701.742509333443,1480.586500080242],"size":[75,26],"flags":{},"order":50,"mode":0,"inputs":[{"name":"","type":"*","link":236,"label":"","slot_index":0}],"outputs":[{"name":"","type":"IMAGE","links":[126,127,128],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":144,"type":"GetImageSize+","pos":[5291.742509333441,1940.5865000802428],"size":{"0":210,"1":46},"flags":{},"order":51,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":237,"label":"image"}],"outputs":[{"name":"width","type":"INT","links":[222],"shape":3,"label":"width","slot_index":0},{"name":"height","type":"INT","links":[223],"shape":3,"label":"height","slot_index":1}],"properties":{"Node name for S&R":"GetImageSize+"}},{"id":153,"type":"Reroute","pos":[5546.922008572656,118.74565938829767],"size":[75,26],"flags":{},"order":48,"mode":0,"inputs":[{"name":"","type":"*","link":238,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[236,237],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":150,"type":"GetImageSize+","pos":[4846.922008572658,138.74565938829767],"size":{"0":210,"1":46},"flags":{},"order":35,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":228,"label":"image"}],"outputs":[{"name":"width","type":"INT","links":[229],"shape":3,"label":"width","slot_index":0},{"name":"height","type":"INT","links":[244],"shape":3,"label":"height","slot_index":1}],"properties":{"Node name for S&R":"GetImageSize+"}},{"id":147,"type":"ImageCrop+","pos":[5206.922008572656,98.74565938829812],"size":{"0":230,"1":150},"flags":{},"order":45,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":241,"label":"image"},{"name":"width","type":"INT","link":231,"widget":{"name":"width"},"label":"width"},{"name":"height","type":"INT","link":234,"widget":{"name":"height"},"label":"height"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[238],"shape":3,"label":"IMAGE","slot_index":0},{"name":"x","type":"INT","links":null,"shape":3,"label":"x"},{"name":"y","type":"INT","links":null,"shape":3,"label":"y"}],"properties":{"Node name for S&R":"ImageCrop+"},"widgets_values":[256,256,"center",0,0]},{"id":148,"type":"MathExpressionPlus","pos":[4796.922008572658,288.74565938829767],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":38,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":229,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[230],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a/8"]},{"id":149,"type":"MathExpressionPlus","pos":[5006.922008572659,288.74565938829767],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":41,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":230,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[231],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a*8"]},{"id":151,"type":"MathExpressionPlus","pos":[4796.922008572658,348.7456593882978],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":39,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":244,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[232],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a/8"]},{"id":152,"type":"MathExpressionPlus","pos":[5006.922008572659,348.7456593882978],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":42,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":232,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[234],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a*8"]},{"id":50,"type":"Reroute","pos":[4546.9220085726565,-841.2543406117022],"size":[75,26],"flags":{},"order":20,"mode":0,"inputs":[{"name":"","type":"*","link":247,"slot_index":0,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[52,59,123],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":159,"type":"GetNode","pos":[4486.9220085726565,-951.2543406117022],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":2,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[247],"slot_index":0,"label":"IMAGE"}],"title":"Get_clothes","properties":{},"widgets_values":["clothes"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":139,"type":"UpscaleSizeCalculator","pos":[4636.922008572657,-161.25434061170074],"size":{"0":210,"1":120},"flags":{},"order":30,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":211,"label":"image","slot_index":0}],"outputs":[{"name":"rescale_factor","type":"FLOAT","links":[209],"shape":3,"label":"rescale_factor","slot_index":0},{"name":"rescale_width","type":"INT","links":null,"shape":3,"label":"rescale_width"},{"name":"recover_factor","type":"FLOAT","links":null,"shape":3,"label":"recover_factor"},{"name":"recover_width","type":"INT","links":null,"shape":3,"label":"recover_width"}],"properties":{"Node name for S&R":"UpscaleSizeCalculator"},"widgets_values":[1024]},{"id":140,"type":"ImageScaleBy","pos":[4506.9220085726565,108.74565938829767],"size":{"0":280,"1":100},"flags":{},"order":33,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":212,"label":"image"},{"name":"scale_by","type":"FLOAT","link":209,"widget":{"name":"scale_by"},"label":"scale_by"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[228,241],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageScaleBy"},"widgets_values":["nearest-exact",1]},{"id":141,"type":"Reroute","pos":[4516.9220085726565,-101.25434061170108],"size":[75,26],"flags":{},"order":21,"mode":0,"inputs":[{"name":"","type":"*","link":249,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[211,212],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":86,"type":"MaskToImage","pos":[5638.656143016241,1800.717312751493],"size":{"0":210,"1":26},"flags":{},"order":55,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":255,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[121],"shape":3,"slot_index":0,"label":"IMAGE"}],"properties":{"Node name for S&R":"MaskToImage"}},{"id":166,"type":"GrowMask","pos":[5538.656143016243,1530.717312751491],"size":{"0":210,"1":82},"flags":{},"order":54,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":254,"label":"mask"}],"outputs":[{"name":"MASK","type":"MASK","links":[255],"shape":3,"label":"MASK","slot_index":0}],"properties":{"Node name for S&R":"GrowMask"},"widgets_values":[20,true]},{"id":169,"type":"LoadConrainReactorModels","pos":[5829.238240408111,2690.74263958436],"size":{"0":315,"1":190},"flags":{"collapsed":true},"order":3,"mode":0,"outputs":[{"name":"faceswapper_model","type":"FACE_MODEL","links":[261],"shape":3,"label":"faceswapper_model","slot_index":0},{"name":"facedetection_model","type":"FACE_MODEL","links":[258],"shape":3,"label":"facedetection_model","slot_index":1},{"name":"facerestore_model","type":"FACE_MODEL","links":[262],"shape":3,"label":"facerestore_model","slot_index":2},{"name":"faceparse_model","type":"FACE_MODEL","links":[263],"shape":3,"label":"faceparse_model","slot_index":3}],"properties":{"Node name for S&R":"LoadConrainReactorModels"},"widgets_values":["inswapper_128.onnx","retinaface_resnet50","GFPGANv1.4.pth","parsenet"]},{"id":161,"type":"GetNode","pos":[4446.180282632403,-326.0492457568355],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":4,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[249],"slot_index":0,"label":"IMAGE"}],"title":"Get_pose","properties":{},"widgets_values":["pose"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":171,"type":"GetNode","pos":[5859.238240408111,2900.74263958436],"size":{"0":210,"1":58},"flags":{},"order":5,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[267],"slot_index":0,"label":"IMAGE"}],"title":"Get_face","properties":{},"widgets_values":["face"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":84,"type":"GroundingDinoModelLoader (segment anything)","pos":[4518.656143016245,1320.7173127514911],"size":{"0":361.20001220703125,"1":58},"flags":{},"order":6,"mode":0,"outputs":[{"name":"GROUNDING_DINO_MODEL","type":"GROUNDING_DINO_MODEL","links":[112],"shape":3,"slot_index":0,"label":"GROUNDING_DINO_MODEL"}],"properties":{"Node name for S&R":"GroundingDinoModelLoader (segment anything)"},"widgets_values":["GroundingDINO_SwinB (938MB)"]},{"id":172,"type":"SetNode","pos":[2620,1150],"size":{"0":210,"1":58},"flags":{},"order":24,"mode":0,"inputs":[{"name":"IMAGE","type":"IMAGE","link":268,"label":"IMAGE"}],"outputs":[{"name":"*","type":"*","links":null,"label":"*"}],"title":"Set_face","properties":{"previousName":"face"},"widgets_values":["face"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":160,"type":"SetNode","pos":[1960,1240],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":25,"mode":0,"inputs":[{"name":"IMAGE","type":"IMAGE","link":248,"label":"IMAGE"}],"outputs":[{"name":"*","type":"*","links":null,"label":"*"}],"title":"Set_pose","properties":{"previousName":"pose"},"widgets_values":["pose"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":158,"type":"SetNode","pos":[1330,1220],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":28,"mode":0,"inputs":[{"name":"IMAGE","type":"IMAGE","link":253,"label":"IMAGE"}],"outputs":[{"name":"*","type":"*","links":null,"label":"*"}],"title":"Set_clothes","properties":{"previousName":"clothes"},"widgets_values":["clothes"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":76,"type":"ShowText|pysssss","pos":[6576.922008572656,-141.25434061170074],"size":{"0":460,"1":150},"flags":{},"order":46,"mode":0,"inputs":[{"name":"text","type":"STRING","link":103,"widget":{"name":"text"},"label":"text"}],"outputs":[{"name":"STRING","type":"STRING","links":null,"shape":6,"label":"STRING"}],"properties":{"Node name for S&R":"ShowText|pysssss"},"widgets_values":["","A Asia male model wearing A white long-sleeve sweatshirt. The sweatshirt has a crew collar. The sweatshirt has a regular length. The sweatshirt has no pattern or text."]},{"id":176,"type":"CR Text Length","pos":[5226.93374847536,-976.0492457568355],"size":{"0":210,"1":70},"flags":{"collapsed":true},"order":26,"mode":0,"inputs":[{"name":"text","type":"STRING","link":270,"widget":{"name":"text"},"label":"text"}],"outputs":[{"name":"INT","type":"INT","links":[275],"shape":3,"label":"INT","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"properties":{"Node name for S&R":"CR Text Length"},"widgets_values":[""]},{"id":194,"type":"CR Seed","pos":[5256.93374847536,-906.0492457568355],"size":{"0":315,"1":102},"flags":{"collapsed":true},"order":7,"mode":0,"outputs":[{"name":"seed","type":"INT","links":[274],"shape":3,"label":"seed","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"0","properties":{"Node name for S&R":"CR Seed"},"widgets_values":[0,"fixed"]},{"id":66,"type":"CR Text","pos":[5346.93374847536,-186.0492457568355],"size":{"0":380,"1":140},"flags":{},"order":8,"mode":0,"outputs":[{"name":"text","type":"*","links":[104],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"模特prompt","properties":{"Node name for S&R":"CR Text"},"widgets_values":["${FACE.extInfo.modelDesc} wearing "],"color":"#232","bgcolor":"#353"},{"id":90,"type":"IDM-VTON","pos":[6097.652065502115,1397.0510859168128],"size":{"0":400,"1":370},"flags":{},"order":56,"mode":0,"inputs":[{"name":"pipeline","type":"PIPELINE","link":118,"label":"pipeline"},{"name":"human_img","type":"IMAGE","link":126,"label":"human_img"},{"name":"pose_img","type":"IMAGE","link":120,"label":"pose_img"},{"name":"mask_img","type":"IMAGE","link":121,"label":"mask_img"},{"name":"garment_img","type":"IMAGE","link":123,"label":"garment_img"},{"name":"garment_description","type":"STRING","link":300,"widget":{"name":"garment_description"},"label":"garment_description"},{"name":"width","type":"INT","link":222,"widget":{"name":"width"},"label":"width"},{"name":"height","type":"INT","link":223,"widget":{"name":"height"},"label":"height"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[178],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK"}],"title":"处理中","properties":{"Node name for S&R":"IDM-VTON"},"widgets_values":["model is wearing a tee-shirt","monochrome, lowres, bad anatomy, worst quality, low quality",768,1024,20,4,1,"${seed}","fixed"]},{"id":125,"type":"CheckpointLoaderSimple","pos":[4472.589205199074,2375.1738358368966],"size":{"0":315,"1":98},"flags":{},"order":9,"mode":0,"outputs":[{"name":"MODEL","type":"MODEL","links":[171],"shape":3,"slot_index":0,"label":"MODEL"},{"name":"CLIP","type":"CLIP","links":[158,159,172],"shape":3,"slot_index":1,"label":"CLIP"},{"name":"VAE","type":"VAE","links":[173],"shape":3,"slot_index":2,"label":"VAE"}],"properties":{"Node name for S&R":"CheckpointLoaderSimple"},"widgets_values":["真实系：majicmixRealistic_v7.safetensors"]},{"id":115,"type":"BNK_CLIPTextEncodeAdvanced","pos":[4624.665332234161,2606.07211320984],"size":{"0":389.95330810546875,"1":157.71157836914062},"flags":{},"order":22,"mode":0,"inputs":[{"name":"clip","type":"CLIP","link":158,"label":"clip"}],"outputs":[{"name":"CONDITIONING","type":"CONDITIONING","links":[174],"shape":3,"label":"CONDITIONING","slot_index":0}],"title":"修脸","properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["realistic, ${FACE.extTags}","none","A1111"],"color":"#232","bgcolor":"#353"},{"id":170,"type":"LoadImage","pos":[2430,1000],"size":[400,470],"flags":{},"order":10,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[268],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK","slot_index":1}],"title":"模特脸","properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${FACE.extInfo.faceImage}","image"],"color":"#232","bgcolor":"#353","locked":true},{"id":24,"type":"ImageScaleBy","pos":[4946.180282632405,-576.0492457568355],"size":{"0":280,"1":100},"flags":{},"order":32,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":59,"label":"image"},{"name":"scale_by","type":"FLOAT","link":28,"widget":{"name":"scale_by"},"label":"scale_by"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[286],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageScaleBy"},"widgets_values":["nearest-exact",1]},{"id":190,"type":"ImpactCompare","pos":[5426.93374847536,-996.0492457568355],"size":{"0":210,"1":90},"flags":{},"order":31,"mode":0,"inputs":[{"name":"a","type":"INT","link":275,"label":"a"},{"name":"b","type":"INT","link":274,"label":"b"}],"outputs":[{"name":"BOOLEAN","type":"BOOLEAN","links":[277],"shape":3,"label":"BOOLEAN","slot_index":0}],"properties":{"Node name for S&R":"ImpactCompare"},"widgets_values":["a > b"]},{"id":175,"type":"ConrainIfExecute","pos":[5720,-890],"size":{"0":210,"1":66},"flags":{},"order":34,"mode":0,"inputs":[{"name":"ANY","type":"*","link":277,"label":"ANY"},{"name":"IF_TRUE","type":"*","link":290,"label":"IF_TRUE"},{"name":"IF_FALSE","type":"*","link":289,"label":"IF_FALSE"}],"outputs":[{"name":"?","type":"*","links":[285,287],"shape":3,"label":"?","slot_index":0}],"properties":{"Node name for S&R":"ConrainIfExecute"}},{"id":198,"type":"easy showAnything","pos":[6100,-870],"size":{"0":350,"1":170},"flags":{},"order":36,"mode":0,"inputs":[{"name":"anything","type":"*","link":285,"label":"anything"}],"properties":{"Node name for S&R":"easy showAnything"},"widgets_values":["I want to use stable diffusion to generate this dress. Please describe this dress in detail, including the dress style, length, slenderness, collar, pattern, pattern, and text.\n\nExample:\n\nA white short-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a regular length. The t-shirt has a yellow flower graphic on the center.\n\nA black long-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a short length. The t-shirt has a text 【Hello】.\n\nA green long-sleeve t-shirt. The t-shirt has a  shirt collar. The t-shirt has a mid length."]},{"id":179,"type":"CR Text","pos":[5280,-860],"size":{"0":370,"1":100},"flags":{"collapsed":true},"order":11,"mode":0,"outputs":[{"name":"text","type":"*","links":[290],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"空","properties":{"Node name for S&R":"CR Text"},"widgets_values":[""]},{"id":124,"type":"FaceDetailer","pos":[5184.665332234161,2636.07211320984],"size":{"0":506.4000244140625,"1":904},"flags":{},"order":57,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":178,"label":"image"},{"name":"model","type":"MODEL","link":171,"label":"model"},{"name":"clip","type":"CLIP","link":172,"label":"clip"},{"name":"vae","type":"VAE","link":173,"label":"vae"},{"name":"positive","type":"CONDITIONING","link":174,"label":"positive"},{"name":"negative","type":"CONDITIONING","link":175,"label":"negative","slot_index":5},{"name":"bbox_detector","type":"BBOX_DETECTOR","link":176,"label":"bbox_detector","slot_index":6},{"name":"sam_model_opt","type":"SAM_MODEL","link":177,"label":"sam_model_opt","slot_index":7},{"name":"segm_detector_opt","type":"SEGM_DETECTOR","link":null,"label":"segm_detector_opt","slot_index":8},{"name":"detailer_hook","type":"DETAILER_HOOK","link":null,"label":"detailer_hook"}],"outputs":[{"name":"image","type":"IMAGE","links":[265],"shape":3,"label":"image","slot_index":0},{"name":"cropped_refined","type":"IMAGE","links":[],"shape":6,"label":"cropped_refined","slot_index":1},{"name":"cropped_enhanced_alpha","type":"IMAGE","links":[],"shape":6,"label":"cropped_enhanced_alpha","slot_index":2},{"name":"mask","type":"MASK","links":null,"shape":3,"label":"mask"},{"name":"detailer_pipe","type":"DETAILER_PIPE","links":null,"shape":3,"label":"detailer_pipe"},{"name":"cnet_images","type":"IMAGE","links":null,"shape":6,"label":"cnet_images"}],"title":"处理中","properties":{"Node name for S&R":"FaceDetailer"},"widgets_values":[384,true,512,"${seed2}","fixed",8,"3","euler","normal",0.4,5,true,true,0.5,500,3,"center-1",0,0.93,0,0.7,"False",10,"",1,1,0,0]},{"id":79,"type":"PipelineLoader","pos":[5641.742509333443,1220.5865000802426],"size":{"0":210,"1":58},"flags":{},"order":12,"mode":0,"outputs":[{"name":"PIPELINE","type":"PIPELINE","links":[118],"shape":3,"slot_index":0,"label":"PIPELINE"}],"properties":{"Node name for S&R":"PipelineLoader"},"widgets_values":["float16"]},{"id":85,"type":"GroundingDinoSAMSegment (segment anything)","pos":[5150.884619901704,1269.4677706489997],"size":{"0":350,"1":150},"flags":{},"order":53,"mode":0,"inputs":[{"name":"sam_model","type":"SAM_MODEL","link":111,"label":"sam_model"},{"name":"grounding_dino_model","type":"GROUNDING_DINO_MODEL","link":112,"label":"grounding_dino_model"},{"name":"image","type":"IMAGE","link":128,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[],"shape":3,"slot_index":0,"label":"IMAGE"},{"name":"MASK","type":"MASK","links":[254],"shape":3,"slot_index":1,"label":"MASK"}],"title":"处理中","properties":{"Node name for S&R":"GroundingDinoSAMSegment (segment anything)"},"widgets_values":["t-shirt","black",0.3]},{"id":83,"type":"SAMModelLoader (segment anything)","pos":[4538.656143016245,1210.717312751491],"size":{"0":330,"1":60},"flags":{},"order":13,"mode":0,"outputs":[{"name":"SAM_MODEL","type":"SAM_MODEL","links":[111],"shape":3,"slot_index":0,"label":"SAM_MODEL"}],"properties":{"Node name for S&R":"SAMModelLoader (segment anything)"},"widgets_values":["sam_hq_vit_h (2.57GB)"]},{"id":88,"type":"DensePosePreprocessor","pos":[5190.884619901704,1679.4677706489997],"size":{"0":315,"1":106},"flags":{},"order":52,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":127,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[120],"shape":3,"slot_index":0,"label":"IMAGE"}],"properties":{"Node name for S&R":"DensePosePreprocessor"},"widgets_values":["densepose_r50_fpn_dl.torchscript","Parula (CivitAI)",768]},{"id":154,"type":"LoadImage","pos":[1830,940],"size":[560,880],"flags":{},"order":14,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[248],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK"}],"title":"输入模特","properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${referImage}","image"],"color":"#232","bgcolor":"#353","locked":true},{"id":174,"type":"CR Text","pos":[4790,-960],"size":{"0":330,"1":140},"flags":{},"order":15,"mode":0,"outputs":[{"name":"text","type":"*","links":[270,278],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"衣服prompt","properties":{"Node name for S&R":"CR Text"},"widgets_values":["${CLOTH_STYLE.tags}"],"color":"#232","bgcolor":"#353"},{"id":178,"type":"CR Text","pos":[5250,-820],"size":{"0":610,"1":250},"flags":{"collapsed":true},"order":16,"mode":0,"outputs":[{"name":"text","type":"*","links":[289],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"衣服描述prompt","properties":{"Node name for S&R":"CR Text"},"widgets_values":["I want to use stable diffusion to generate this dress. Please describe this dress in detail, including the dress style, length, slenderness, collar, pattern, pattern, and text.\n\nExample:\n\nA white short-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a regular length. The t-shirt has a yellow flower graphic on the center.\n\nA black long-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a short length. The t-shirt has a text 【Hello】.\n\nA green long-sleeve t-shirt. The t-shirt has a  shirt collar. The t-shirt has a mid length."]},{"id":199,"type":"LLModel","pos":[5550,-670],"size":{"0":260,"1":100},"flags":{},"order":37,"mode":0,"inputs":[{"name":"image_list","type":"IMAGE","link":286,"label":"image_list"},{"name":"ref_image","type":"IMAGE","link":null,"label":"ref_image"},{"name":"prompt","type":"STRING","link":287,"widget":{"name":"prompt"},"label":"prompt"}],"outputs":[{"name":"result_text","type":"STRING","links":[288],"shape":3,"label":"result_text","slot_index":0},{"name":"result_detail","type":"STRING","links":null,"shape":3,"label":"result_detail"}],"properties":{"Node name for S&R":"LLModel"},"widgets_values":["你能干嘛","default"]},{"id":195,"type":"MergeString","pos":[5920,-460],"size":{"0":140,"1":50},"flags":{},"order":40,"mode":0,"inputs":[{"name":"input1","type":"*","link":278,"label":"input1"},{"name":"input2","type":"*","link":288,"label":"input2"}],"outputs":[{"name":"STRING","type":"STRING","links":[280,281],"shape":3,"label":"STRING","slot_index":0}],"properties":{"Node name for S&R":"MergeString"}},{"id":13,"type":"ShowText|pysssss","pos":[6166.93374847536,-586.0492457568355],"size":{"0":510,"1":200},"flags":{},"order":44,"mode":0,"inputs":[{"name":"text","type":"STRING","link":281,"widget":{"name":"text"},"label":"text"}],"outputs":[{"name":"STRING","type":"STRING","links":null,"shape":6,"label":"STRING"}],"properties":{"Node name for S&R":"ShowText|pysssss"},"widgets_values":["","A white long-sleeve sweatshirt. The sweatshirt has a crew collar. The sweatshirt has a regular length. The sweatshirt has no pattern or text."],"color":"#494949","bgcolor":"#353535"},{"id":205,"type":"Text Concatenate","pos":[7655.024423658283,1255.4457367070218],"size":{"0":290,"1":142},"flags":{},"order":27,"mode":0,"inputs":[{"widget":{"name":"text_a"},"link":297,"name":"text_a","label":"text_a","type":"STRING"},{"widget":{"name":"text_b"},"link":298,"name":"text_b","label":"text_b","type":"STRING"},{"widget":{"name":"text_c"},"link":null,"name":"text_c","label":"text_c","type":"STRING"},{"widget":{"name":"text_d"},"link":null,"name":"text_d","label":"text_d","type":"STRING"}],"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[293],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text Concatenate"},"widgets_values":["/","false","","","",""]},{"id":204,"type":"ConrainImageSave","pos":[8035.024423658283,1665.4457367070213],"size":{"0":320,"1":266},"flags":{},"order":59,"mode":0,"inputs":[{"link":302,"name":"images","label":"images","type":"IMAGE"},{"widget":{"name":"output_path"},"link":295,"name":"output_path","label":"output_path","type":"STRING"},{"widget":{"name":"filename_prefix"},"link":296,"name":"filename_prefix","label":"filename_prefix","type":"STRING"}],"outputs":[{"shape":3,"name":"image_cnt","slot_index":0,"links":[],"label":"image_cnt","type":"INT"}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["[time(%Y-%m-%d)]","ComfyUI","jpg",100,100,"true","false","false","true","true"]},{"id":203,"type":"Save Text File","pos":[8065.024423658282,1265.4457367070218],"size":{"0":315,"1":154},"flags":{},"order":49,"mode":0,"inputs":[{"widget":{"name":"text"},"link":301,"name":"text","label":"text","type":"STRING"},{"widget":{"name":"path"},"link":293,"name":"path","label":"path","type":"STRING"},{"widget":{"name":"filename_prefix"},"link":294,"name":"filename_prefix","label":"filename_prefix","type":"STRING"}],"properties":{"Node name for S&R":"Save Text File"},"widgets_values":["","./ComfyUI/output/[time(%Y-%m-%d)]","ComfyUI","_",4]},{"id":65,"type":"MergeString","pos":[6186.93374847536,-26.049245756835376],"size":{"0":210,"1":46},"flags":{},"order":43,"mode":0,"inputs":[{"name":"input1","type":"*","link":104,"label":"input1"},{"name":"input2","type":"*","link":280,"label":"input2"}],"outputs":[{"name":"STRING","type":"STRING","links":[103,299],"shape":3,"label":"STRING","slot_index":0}],"properties":{"Node name for S&R":"MergeString"}},{"id":207,"type":"Reroute","pos":[6130,1120],"size":[75,26],"flags":{},"order":47,"mode":0,"inputs":[{"name":"","type":"*","link":299,"label":"","widget":{"name":"value"}}],"outputs":[{"name":"","type":"STRING","links":[300,301],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":168,"type":"ConrainReActorFaceSwap","pos":[6239.238240408107,2600.7426395843595],"size":{"0":280,"1":380},"flags":{},"order":58,"mode":0,"inputs":[{"name":"input_image","type":"IMAGE","link":265,"label":"input_image"},{"name":"swap_model","type":"FACE_MODEL","link":261,"label":"swap_model"},{"name":"facedetection","type":"FACE_MODEL","link":258,"label":"facedetection"},{"name":"face_restore_model","type":"FACE_MODEL","link":262,"label":"face_restore_model"},{"name":"faceparse_model","type":"FACE_MODEL","link":263,"label":"faceparse_model"},{"name":"source_image","type":"IMAGE","link":267,"label":"source_image"},{"name":"face_model","type":"FACE_MODEL","link":null,"label":"face_model"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[302],"shape":3,"label":"IMAGE","slot_index":0},{"name":"FACE_MODEL","type":"FACE_MODEL","links":null,"shape":3,"label":"FACE_MODEL"}],"title":"处理中","properties":{"Node name for S&R":"ConrainReActorFaceSwap"},"widgets_values":[true,1,0.5,"no","no","0","0",1,"yes"]},{"id":206,"type":"String to Text","pos":[7240,1010],"size":{"0":315,"1":58},"flags":{"collapsed":false},"order":17,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[297],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["output"]},{"id":202,"type":"Text String","pos":[7255.024423658283,1705.4457367070213],"size":{"0":315,"1":190},"flags":{},"order":18,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[295,298],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","slot_index":1,"links":[294,296],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${outputPath}","${fileNamePrefix}","",""]},{"id":81,"type":"LoadImage","pos":[1180,940],"size":{"0":600,"1":870},"flags":{},"order":19,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[253],"shape":3,"slot_index":0,"label":"IMAGE"},{"name":"MASK","type":"MASK","links":null,"shape":3,"slot_index":1,"label":"MASK"}],"title":"输入衣服","properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${logoImage}","image"],"color":"#232","bgcolor":"#353"}],"links":[[28,23,0,24,1,"FLOAT"],[52,50,0,23,0,"IMAGE"],[59,50,0,24,0,"IMAGE"],[103,65,0,76,0,"STRING"],[104,66,0,65,0,"*"],[111,83,0,85,0,"SAM_MODEL"],[112,84,0,85,1,"GROUNDING_DINO_MODEL"],[118,79,0,90,0,"PIPELINE"],[120,88,0,90,2,"IMAGE"],[121,86,0,90,3,"IMAGE"],[123,50,0,90,4,"IMAGE"],[126,92,0,90,1,"IMAGE"],[127,92,0,88,0,"IMAGE"],[128,92,0,85,2,"IMAGE"],[158,125,1,115,0,"CLIP"],[159,125,1,116,0,"CLIP"],[171,125,0,124,1,"MODEL"],[172,125,1,124,2,"CLIP"],[173,125,2,124,3,"VAE"],[174,115,0,124,4,"CONDITIONING"],[175,116,0,124,5,"CONDITIONING"],[176,113,0,124,6,"BBOX_DETECTOR"],[177,114,0,124,7,"SAM_MODEL"],[178,90,0,124,0,"IMAGE"],[209,139,0,140,1,"FLOAT"],[211,141,0,139,0,"IMAGE"],[212,141,0,140,0,"IMAGE"],[222,144,0,90,6,"INT"],[223,144,1,90,7,"INT"],[228,140,0,150,0,"IMAGE"],[229,150,0,148,0,"INT,FLOAT"],[230,148,0,149,0,"INT,FLOAT"],[231,149,0,147,1,"INT"],[232,151,0,152,0,"INT,FLOAT"],[234,152,0,147,2,"INT"],[236,153,0,92,0,"*"],[237,153,0,144,0,"IMAGE"],[238,147,0,153,0,"*"],[241,140,0,147,0,"IMAGE"],[244,150,1,151,0,"INT,FLOAT"],[247,159,0,50,0,"*"],[248,154,0,160,0,"*"],[249,161,0,141,0,"*"],[253,81,0,158,0,"IMAGE"],[254,85,1,166,0,"MASK"],[255,166,0,86,0,"MASK"],[258,169,1,168,2,"FACE_MODEL"],[261,169,0,168,1,"FACE_MODEL"],[262,169,2,168,3,"FACE_MODEL"],[263,169,3,168,4,"FACE_MODEL"],[265,124,0,168,0,"IMAGE"],[267,171,0,168,5,"IMAGE"],[268,170,0,172,0,"*"],[270,174,0,176,0,"STRING"],[274,194,0,190,1,"*"],[275,176,0,190,0,"INT"],[277,190,0,175,0,"*"],[278,174,0,195,0,"*"],[280,195,0,65,1,"*"],[281,195,0,13,0,"STRING"],[285,175,0,198,0,"*"],[286,24,0,199,0,"IMAGE"],[287,175,0,199,2,"STRING"],[288,199,0,195,1,"*"],[289,178,0,175,2,"*"],[290,179,0,175,1,"*"],[293,205,0,203,1,"STRING"],[294,202,1,203,2,"STRING"],[295,202,0,204,1,"STRING"],[296,202,1,204,2,"STRING"],[297,206,0,205,0,"STRING"],[298,202,0,205,1,"STRING"],[299,65,0,207,0,"*"],[300,207,0,90,5,"STRING"],[301,207,0,203,0,"STRING"],[302,168,0,204,0,"IMAGE"]],"groups":[{"title":"step2","bounding":[4444,762,2288,1280],"color":"#3f789e","font_size":24,"locked":false},{"title":"1","bounding":[4420,-1057,2741,1732],"color":"#3f789e","font_size":24,"locked":false},{"title":"3","bounding":[4444,2257,2554,1743],"color":"#3f789e","font_size":24,"locked":false},{"title":"保存结果","bounding":[7158,801,1517,1177],"color":"#3f789e","font_size":24,"locked":false}],"config":{},"extra":{"ds":{"scale":0.3452271214393175,"offset":{"0":-4274.1471081855725,"1":-489.0378754847875}}},"version":0.4,"widget_idx_map":{"90":{"seed":7},"124":{"seed":3,"sampler_name":7,"scheduler":8},"194":{"seed":0}},"seed_widgets":{"90":7,"124":3,"194":0}}}}}',null,now(),0,'印花上身流程参数配置');
('LOGO_COMBINE_BACK_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"23":{"inputs":{"target_size":1024,"image":["81",0]},"class_type":"UpscaleSizeCalculator","_meta":{"title":"Upscale Size Calculator"}},"24":{"inputs":{"upscale_method":"nearest-exact","scale_by":["23",0],"image":["81",0]},"class_type":"ImageScaleBy","_meta":{"title":"Upscale Image By"}},"65":{"inputs":{"input1":["66",0],"input2":["199",0]},"class_type":"MergeString","_meta":{"title":"Merge String"}},"66":{"inputs":{"text":"${FACE.extInfo.modelDesc} wearing "},"class_type":"CR Text","_meta":{"title":"模特prompt"}},"79":{"inputs":{"weight_dtype":"float16"},"class_type":"PipelineLoader","_meta":{"title":"Load IDM-VTON Pipeline"}},"81":{"inputs":{"image":"${logoImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"输入衣服"}},"83":{"inputs":{"model_name":"sam_hq_vit_h (2.57GB)"},"class_type":"SAMModelLoader (segment anything)","_meta":{"title":"SAMModelLoader (segment anything)"}},"84":{"inputs":{"model_name":"GroundingDINO_SwinB (938MB)"},"class_type":"GroundingDinoModelLoader (segment anything)","_meta":{"title":"GroundingDinoModelLoader (segment anything)"}},"85":{"inputs":{"prompt":"t-shirt","background":"black","threshold":0.3,"sam_model":["83",0],"grounding_dino_model":["84",0],"image":["147",0]},"class_type":"GroundingDinoSAMSegment (segment anything)","_meta":{"title":"处理中"}},"86":{"inputs":{"mask":["166",0]},"class_type":"MaskToImage","_meta":{"title":"Convert Mask to Image"}},"88":{"inputs":{"model":"densepose_r50_fpn_dl.torchscript","cmap":"Parula (CivitAI)","resolution":768,"image":["147",0]},"class_type":"DensePosePreprocessor","_meta":{"title":"DensePose Estimator"}},"90":{"inputs":{"garment_description":["65",0],"negative_prompt":"monochrome, lowres, bad anatomy, worst quality, low quality","width":["144",0],"height":["144",1],"num_inference_steps":20,"guidance_scale":4,"strength":1,"seed":"${seed}","pipeline":["79",0],"human_img":["147",0],"pose_img":["88",0],"mask_img":["86",0],"garment_img":["81",0]},"class_type":"IDM-VTON","_meta":{"title":"处理中"}},"139":{"inputs":{"target_size":1024,"image":["154",0]},"class_type":"UpscaleSizeCalculator","_meta":{"title":"Upscale Size Calculator"}},"140":{"inputs":{"upscale_method":"nearest-exact","scale_by":["139",0],"image":["154",0]},"class_type":"ImageScaleBy","_meta":{"title":"Upscale Image By"}},"144":{"inputs":{"image":["147",0]},"class_type":"GetImageSize+","_meta":{"title":"🔧 Get Image Size"}},"147":{"inputs":{"width":["149",0],"height":["152",0],"position":"center","x_offset":0,"y_offset":0,"image":["140",0]},"class_type":"ImageCrop+","_meta":{"title":"🔧 Image Crop"}},"148":{"inputs":{"expression":"a/8","a":["150",0]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"149":{"inputs":{"expression":"a*8","a":["148",0]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"150":{"inputs":{"image":["140",0]},"class_type":"GetImageSize+","_meta":{"title":"🔧 Get Image Size"}},"151":{"inputs":{"expression":"a/8","a":["150",1]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"152":{"inputs":{"expression":"a*8","a":["151",0]},"class_type":"MathExpressionPlus","_meta":{"title":"Math Expression +"}},"154":{"inputs":{"image":"${referImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"输入模特"}},"166":{"inputs":{"expand":20,"tapered_corners":true,"mask":["85",1]},"class_type":"GrowMask","_meta":{"title":"GrowMask"}},"170":{"inputs":{"image":"prod_fix_f7566fdea8694b9dad3e72feae2b9d84.png","upload":"image"},"class_type":"LoadImage","_meta":{"title":"模特脸"}},"174":{"inputs":{"text":"${CLOTH_STYLE.tags}"},"class_type":"CR Text","_meta":{"title":"衣服prompt"}},"178":{"inputs":{"text":"I want to use stable diffusion to generate this dress. Please describe this dress in detail, including the dress style, length, slenderness, collar, pattern, pattern, and text.\n\nExample:\n\nA white short-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a regular length. The t-shirt has a yellow flower graphic on the center.\n\nA black long-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a short length. The t-shirt has a text 【Hello】.\n\nA green long-sleeve t-shirt. The t-shirt has a  shirt collar. The t-shirt has a mid length.\n\nA white long-sleeve sweatshirt. The sweatshirt has a crew collar. The sweatshirt has a regular length. The sweatshirt has a green logo graphic on the center. The has a text 【STARBUCKS COFFEE】"},"class_type":"CR Text","_meta":{"title":"衣服描述prompt"}},"199":{"inputs":{"prompt":["178",0],"llm_model":"default","image_list":["24",0]},"class_type":"LLModel","_meta":{"title":"llmodel"}},"202":{"inputs":{"text":"${outputPath}","text_b":"${fileNamePrefix}","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"203":{"inputs":{"text":["65",0],"path":["205",0],"filename_prefix":["202",1],"filename_delimiter":"_","filename_number_padding":4},"class_type":"Save Text File","_meta":{"title":"Save Text File"}},"204":{"inputs":{"output_path":["202",0],"filename_prefix":["202",1],"extension":"jpg","dpi":100,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"false","use_time_str":"true","output_as_root":"true","images":["90",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"205":{"inputs":{"delimiter":"/","clean_whitespace":"false","text_a":["206",0],"text_b":["202",0]},"class_type":"Text Concatenate","_meta":{"title":"Text Concatenate"}},"206":{"inputs":{"string":"output"},"class_type":"String to Text","_meta":{"title":"String to Text"}},"208":{"inputs":{"text":["174",0]},"class_type":"ShowText|pysssss","_meta":{"title":"Show Text 🐍"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":208,"last_link_id":309,"nodes":[{"id":23,"type":"UpscaleSizeCalculator","pos":[4596.922008572657,-591.2543406117027],"size":{"0":210,"1":120},"flags":{},"order":20,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":52,"label":"image","slot_index":0}],"outputs":[{"name":"rescale_factor","type":"FLOAT","links":[28],"shape":3,"label":"rescale_factor","slot_index":0},{"name":"rescale_width","type":"INT","links":null,"shape":3,"label":"rescale_width"},{"name":"recover_factor","type":"FLOAT","links":null,"shape":3,"label":"recover_factor"},{"name":"recover_width","type":"INT","links":null,"shape":3,"label":"recover_width"}],"properties":{"Node name for S&R":"UpscaleSizeCalculator"},"widgets_values":[1024]},{"id":92,"type":"Reroute","pos":[4701.742509333443,1480.586500080242],"size":[75,26],"flags":{},"order":35,"mode":0,"inputs":[{"name":"","type":"*","link":236,"label":"","slot_index":0}],"outputs":[{"name":"","type":"IMAGE","links":[126,127,128],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":144,"type":"GetImageSize+","pos":[5291.742509333441,1940.5865000802428],"size":{"0":210,"1":46},"flags":{},"order":36,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":237,"label":"image"}],"outputs":[{"name":"width","type":"INT","links":[222],"shape":3,"label":"width","slot_index":0},{"name":"height","type":"INT","links":[223],"shape":3,"label":"height","slot_index":1}],"properties":{"Node name for S&R":"GetImageSize+"}},{"id":153,"type":"Reroute","pos":[5546.922008572656,118.74565938829767],"size":[75,26],"flags":{},"order":34,"mode":0,"inputs":[{"name":"","type":"*","link":238,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[236,237],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":150,"type":"GetImageSize+","pos":[4846.922008572658,138.74565938829767],"size":{"0":210,"1":46},"flags":{},"order":25,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":228,"label":"image"}],"outputs":[{"name":"width","type":"INT","links":[229],"shape":3,"label":"width","slot_index":0},{"name":"height","type":"INT","links":[244],"shape":3,"label":"height","slot_index":1}],"properties":{"Node name for S&R":"GetImageSize+"}},{"id":147,"type":"ImageCrop+","pos":[5206.922008572656,98.74565938829812],"size":{"0":230,"1":150},"flags":{},"order":33,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":241,"label":"image"},{"name":"width","type":"INT","link":231,"widget":{"name":"width"},"label":"width"},{"name":"height","type":"INT","link":234,"widget":{"name":"height"},"label":"height"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[238],"shape":3,"label":"IMAGE","slot_index":0},{"name":"x","type":"INT","links":null,"shape":3,"label":"x"},{"name":"y","type":"INT","links":null,"shape":3,"label":"y"}],"properties":{"Node name for S&R":"ImageCrop+"},"widgets_values":[256,256,"center",0,0]},{"id":148,"type":"MathExpressionPlus","pos":[4796.922008572658,288.74565938829767],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":27,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":229,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[230],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a/8"]},{"id":149,"type":"MathExpressionPlus","pos":[5006.922008572659,288.74565938829767],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":30,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":230,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[231],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a*8"]},{"id":151,"type":"MathExpressionPlus","pos":[4796.922008572658,348.7456593882978],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":28,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":244,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[232],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a/8"]},{"id":152,"type":"MathExpressionPlus","pos":[5006.922008572659,348.7456593882978],"size":{"0":210,"1":100},"flags":{"collapsed":true},"order":31,"mode":0,"inputs":[{"name":"a","type":"INT,FLOAT","link":232,"label":"a"},{"name":"b","type":"INT,FLOAT","link":null,"label":"b"},{"name":"c","type":"INT,FLOAT","link":null,"label":"c"}],"outputs":[{"name":"INT","type":"INT","links":[234],"shape":3,"label":"INT","slot_index":0},{"name":"FLOAT","type":"FLOAT","links":null,"shape":3,"label":"FLOAT"}],"properties":{"Node name for S&R":"MathExpressionPlus"},"widgets_values":["a*8"]},{"id":50,"type":"Reroute","pos":[4546.9220085726565,-841.2543406117022],"size":[75,26],"flags":{},"order":13,"mode":0,"inputs":[{"name":"","type":"*","link":247,"slot_index":0,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[52,59,123],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":159,"type":"GetNode","pos":[4486.9220085726565,-951.2543406117022],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":0,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[247],"slot_index":0,"label":"IMAGE"}],"title":"Get_clothes","properties":{},"widgets_values":["clothes"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":139,"type":"UpscaleSizeCalculator","pos":[4636.922008572657,-161.25434061170074],"size":{"0":210,"1":120},"flags":{},"order":21,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":211,"label":"image","slot_index":0}],"outputs":[{"name":"rescale_factor","type":"FLOAT","links":[209],"shape":3,"label":"rescale_factor","slot_index":0},{"name":"rescale_width","type":"INT","links":null,"shape":3,"label":"rescale_width"},{"name":"recover_factor","type":"FLOAT","links":null,"shape":3,"label":"recover_factor"},{"name":"recover_width","type":"INT","links":null,"shape":3,"label":"recover_width"}],"properties":{"Node name for S&R":"UpscaleSizeCalculator"},"widgets_values":[1024]},{"id":140,"type":"ImageScaleBy","pos":[4506.9220085726565,108.74565938829767],"size":{"0":280,"1":100},"flags":{},"order":23,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":212,"label":"image"},{"name":"scale_by","type":"FLOAT","link":209,"widget":{"name":"scale_by"},"label":"scale_by"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[228,241],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageScaleBy"},"widgets_values":["nearest-exact",1]},{"id":141,"type":"Reroute","pos":[4516.9220085726565,-101.25434061170108],"size":[75,26],"flags":{},"order":14,"mode":0,"inputs":[{"name":"","type":"*","link":249,"label":""}],"outputs":[{"name":"","type":"IMAGE","links":[211,212],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":86,"type":"MaskToImage","pos":[5638.656143016241,1800.717312751493],"size":{"0":210,"1":26},"flags":{},"order":40,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":255,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[121],"shape":3,"slot_index":0,"label":"IMAGE"}],"properties":{"Node name for S&R":"MaskToImage"}},{"id":166,"type":"GrowMask","pos":[5538.656143016243,1530.717312751491],"size":{"0":210,"1":82},"flags":{},"order":39,"mode":0,"inputs":[{"name":"mask","type":"MASK","link":254,"label":"mask"}],"outputs":[{"name":"MASK","type":"MASK","links":[255],"shape":3,"label":"MASK","slot_index":0}],"properties":{"Node name for S&R":"GrowMask"},"widgets_values":[20,true]},{"id":161,"type":"GetNode","pos":[4446.180282632403,-326.0492457568355],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":1,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[249],"slot_index":0,"label":"IMAGE"}],"title":"Get_pose","properties":{},"widgets_values":["pose"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":84,"type":"GroundingDinoModelLoader (segment anything)","pos":[4518.656143016245,1320.7173127514911],"size":{"0":361.20001220703125,"1":58},"flags":{},"order":2,"mode":0,"outputs":[{"name":"GROUNDING_DINO_MODEL","type":"GROUNDING_DINO_MODEL","links":[112],"shape":3,"slot_index":0,"label":"GROUNDING_DINO_MODEL"}],"properties":{"Node name for S&R":"GroundingDinoModelLoader (segment anything)"},"widgets_values":["GroundingDINO_SwinB (938MB)"]},{"id":172,"type":"SetNode","pos":[2620,1150],"size":{"0":210,"1":58},"flags":{},"order":15,"mode":0,"inputs":[{"name":"IMAGE","type":"IMAGE","link":268,"label":"IMAGE"}],"outputs":[{"name":"*","type":"*","links":null,"label":"*"}],"title":"Set_face","properties":{"previousName":"face"},"widgets_values":["face"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":160,"type":"SetNode","pos":[1960,1240],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":19,"mode":0,"inputs":[{"name":"IMAGE","type":"IMAGE","link":248,"label":"IMAGE"}],"outputs":[{"name":"*","type":"*","links":null,"label":"*"}],"title":"Set_pose","properties":{"previousName":"pose"},"widgets_values":["pose"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":158,"type":"SetNode","pos":[1330,1220],"size":{"0":210,"1":58},"flags":{"collapsed":true},"order":18,"mode":0,"inputs":[{"name":"IMAGE","type":"IMAGE","link":253,"label":"IMAGE"}],"outputs":[{"name":"*","type":"*","links":null,"label":"*"}],"title":"Set_clothes","properties":{"previousName":"clothes"},"widgets_values":["clothes"],"color":"#2a363b","bgcolor":"#3f5159"},{"id":170,"type":"LoadImage","pos":[2430,1000],"size":[400,470],"flags":{},"order":3,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[268],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK","slot_index":1}],"title":"模特脸","properties":{"Node name for S&R":"LoadImage"},"widgets_values":["prod_fix_f7566fdea8694b9dad3e72feae2b9d84.png","image"],"color":"#232","bgcolor":"#353","locked":true},{"id":24,"type":"ImageScaleBy","pos":[4946.180282632405,-576.0492457568355],"size":{"0":280,"1":100},"flags":{},"order":22,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":59,"label":"image"},{"name":"scale_by","type":"FLOAT","link":28,"widget":{"name":"scale_by"},"label":"scale_by"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[286],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageScaleBy"},"widgets_values":["nearest-exact",1]},{"id":79,"type":"PipelineLoader","pos":[5641.742509333443,1220.5865000802426],"size":{"0":210,"1":58},"flags":{},"order":4,"mode":0,"outputs":[{"name":"PIPELINE","type":"PIPELINE","links":[118],"shape":3,"slot_index":0,"label":"PIPELINE"}],"properties":{"Node name for S&R":"PipelineLoader"},"widgets_values":["float16"]},{"id":85,"type":"GroundingDinoSAMSegment (segment anything)","pos":[5150.884619901704,1269.4677706489997],"size":{"0":350,"1":150},"flags":{},"order":38,"mode":0,"inputs":[{"name":"sam_model","type":"SAM_MODEL","link":111,"label":"sam_model"},{"name":"grounding_dino_model","type":"GROUNDING_DINO_MODEL","link":112,"label":"grounding_dino_model"},{"name":"image","type":"IMAGE","link":128,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[],"shape":3,"slot_index":0,"label":"IMAGE"},{"name":"MASK","type":"MASK","links":[254],"shape":3,"slot_index":1,"label":"MASK"}],"title":"处理中","properties":{"Node name for S&R":"GroundingDinoSAMSegment (segment anything)"},"widgets_values":["t-shirt","black",0.3]},{"id":83,"type":"SAMModelLoader (segment anything)","pos":[4538.656143016245,1210.717312751491],"size":{"0":330,"1":60},"flags":{},"order":5,"mode":0,"outputs":[{"name":"SAM_MODEL","type":"SAM_MODEL","links":[111],"shape":3,"slot_index":0,"label":"SAM_MODEL"}],"properties":{"Node name for S&R":"SAMModelLoader (segment anything)"},"widgets_values":["sam_hq_vit_h (2.57GB)"]},{"id":88,"type":"DensePosePreprocessor","pos":[5190.884619901704,1679.4677706489997],"size":{"0":315,"1":106},"flags":{},"order":37,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":127,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[120],"shape":3,"slot_index":0,"label":"IMAGE"}],"properties":{"Node name for S&R":"DensePosePreprocessor"},"widgets_values":["densepose_r50_fpn_dl.torchscript","Parula (CivitAI)",768]},{"id":205,"type":"Text Concatenate","pos":[7655.024423658283,1255.4457367070218],"size":{"0":290,"1":142},"flags":{},"order":16,"mode":0,"inputs":[{"widget":{"name":"text_a"},"link":297,"name":"text_a","label":"text_a","type":"STRING"},{"widget":{"name":"text_b"},"link":298,"name":"text_b","label":"text_b","type":"STRING"},{"widget":{"name":"text_c"},"link":null,"name":"text_c","label":"text_c","type":"STRING"},{"widget":{"name":"text_d"},"link":null,"name":"text_d","label":"text_d","type":"STRING"}],"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[293],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text Concatenate"},"widgets_values":["/","false","","","",""]},{"id":204,"type":"ConrainImageSave","pos":[8035.024423658283,1665.4457367070213],"size":{"0":320,"1":266},"flags":{},"order":42,"mode":0,"inputs":[{"link":308,"name":"images","label":"images","type":"IMAGE"},{"widget":{"name":"output_path"},"link":295,"name":"output_path","label":"output_path","type":"STRING"},{"widget":{"name":"filename_prefix"},"link":296,"name":"filename_prefix","label":"filename_prefix","type":"STRING"}],"outputs":[{"shape":3,"name":"image_cnt","slot_index":0,"links":[],"label":"image_cnt","type":"INT"}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["[time(%Y-%m-%d)]","ComfyUI","jpg",100,100,"true","false","false","true","true"]},{"id":203,"type":"Save Text File","pos":[8065.024423658282,1265.4457367070218],"size":{"0":315,"1":154},"flags":{},"order":32,"mode":0,"inputs":[{"widget":{"name":"text"},"link":301,"name":"text","label":"text","type":"STRING"},{"widget":{"name":"path"},"link":293,"name":"path","label":"path","type":"STRING"},{"widget":{"name":"filename_prefix"},"link":294,"name":"filename_prefix","label":"filename_prefix","type":"STRING"}],"properties":{"Node name for S&R":"Save Text File"},"widgets_values":["","./ComfyUI/output/[time(%Y-%m-%d)]","ComfyUI","_",4]},{"id":65,"type":"MergeString","pos":[6186.93374847536,-26.049245756835376],"size":{"0":210,"1":46},"flags":{},"order":26,"mode":0,"inputs":[{"name":"input1","type":"*","link":104,"label":"input1"},{"name":"input2","type":"*","link":306,"label":"input2"}],"outputs":[{"name":"STRING","type":"STRING","links":[299],"shape":3,"label":"STRING","slot_index":0}],"properties":{"Node name for S&R":"MergeString"}},{"id":207,"type":"Reroute","pos":[6130,1120],"size":[75,26],"flags":{},"order":29,"mode":0,"inputs":[{"name":"","type":"*","link":299,"label":"","widget":{"name":"value"}}],"outputs":[{"name":"","type":"STRING","links":[300,301],"slot_index":0,"label":""}],"properties":{"showOutputText":false,"horizontal":false}},{"id":206,"type":"String to Text","pos":[7240,1010],"size":{"0":315,"1":58},"flags":{"collapsed":false},"order":6,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[297],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["output"]},{"id":202,"type":"Text String","pos":[7255.024423658283,1705.4457367070213],"size":{"0":315,"1":190},"flags":{},"order":7,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[295,298],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","slot_index":1,"links":[294,296],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${outputPath}","${fileNamePrefix}","",""]},{"id":208,"type":"ShowText|pysssss","pos":[5357.6849161173395,-889.2129401589234],"size":{"0":320,"1":60},"flags":{},"order":17,"mode":0,"inputs":[{"name":"text","type":"STRING","link":305,"widget":{"name":"text"},"label":"text"}],"outputs":[{"name":"STRING","type":"STRING","links":null,"shape":6,"label":"STRING"}],"properties":{"Node name for S&R":"ShowText|pysssss"},"widgets_values":[""]},{"id":174,"type":"CR Text","pos":[4830,-910],"size":{"0":330,"1":140},"flags":{},"order":8,"mode":0,"outputs":[{"name":"text","type":"*","links":[305],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"衣服prompt","properties":{"Node name for S&R":"CR Text"},"widgets_values":["${CLOTH_STYLE.tags}"],"color":"#232","bgcolor":"#353"},{"id":81,"type":"LoadImage","pos":[1180,940],"size":[600,870],"flags":{},"order":9,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[253],"shape":3,"slot_index":0,"label":"IMAGE"},{"name":"MASK","type":"MASK","links":null,"shape":3,"slot_index":1,"label":"MASK"}],"title":"输入衣服","properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${logoImage}","image"],"color":"#232","bgcolor":"#353","locked":true},{"id":154,"type":"LoadImage","pos":[1830,940],"size":[560,880],"flags":{},"order":10,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[248],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK"}],"title":"输入模特","properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${referImage}","image"],"color":"#232","bgcolor":"#353","locked":true},{"id":199,"type":"LLModel","pos":[5690,-760],"size":{"0":260,"1":100},"flags":{},"order":24,"mode":0,"inputs":[{"name":"image_list","type":"IMAGE","link":286,"label":"image_list"},{"name":"ref_image","type":"IMAGE","link":null,"label":"ref_image"},{"name":"prompt","type":"STRING","link":303,"widget":{"name":"prompt"},"label":"prompt"}],"outputs":[{"name":"result_text","type":"STRING","links":[306],"shape":3,"label":"result_text","slot_index":0},{"name":"result_detail","type":"STRING","links":null,"shape":3,"label":"result_detail"}],"properties":{"Node name for S&R":"LLModel"},"widgets_values":["你能干嘛","default"]},{"id":90,"type":"IDM-VTON","pos":[6097.652065502115,1397.0510859168128],"size":{"0":400,"1":370},"flags":{},"order":41,"mode":0,"inputs":[{"name":"pipeline","type":"PIPELINE","link":118,"label":"pipeline"},{"name":"human_img","type":"IMAGE","link":126,"label":"human_img"},{"name":"pose_img","type":"IMAGE","link":120,"label":"pose_img"},{"name":"mask_img","type":"IMAGE","link":121,"label":"mask_img"},{"name":"garment_img","type":"IMAGE","link":123,"label":"garment_img"},{"name":"garment_description","type":"STRING","link":300,"widget":{"name":"garment_description"},"label":"garment_description"},{"name":"width","type":"INT","link":222,"widget":{"name":"width"},"label":"width"},{"name":"height","type":"INT","link":223,"widget":{"name":"height"},"label":"height"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[308],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK"}],"title":"处理中","properties":{"Node name for S&R":"IDM-VTON"},"widgets_values":["model is wearing a tee-shirt","monochrome, lowres, bad anatomy, worst quality, low quality",768,1024,20,4,1,"${seed}","fixed"]},{"id":178,"type":"CR Text","pos":[4930,-410],"size":{"0":610,"1":250},"flags":{"collapsed":false},"order":11,"mode":0,"outputs":[{"name":"text","type":"*","links":[303],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"衣服描述prompt","properties":{"Node name for S&R":"CR Text"},"widgets_values":["I want to use stable diffusion to generate this dress. Please describe this dress in detail, including the dress style, length, slenderness, collar, pattern, pattern, and text.\n\nExample:\n\nA white short-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a regular length. The t-shirt has a yellow flower graphic on the center.\n\nA black long-sleeve t-shirt. The t-shirt has a crew collar. The t-shirt has a short length. The t-shirt has a text 【Hello】.\n\nA green long-sleeve t-shirt. The t-shirt has a  shirt collar. The t-shirt has a mid length.\n\nA white long-sleeve sweatshirt. The sweatshirt has a crew collar. The sweatshirt has a regular length. The sweatshirt has a green logo graphic on the center. The has a text 【STARBUCKS COFFEE】"]},{"id":66,"type":"CR Text","pos":[5610,-120],"size":{"0":380,"1":140},"flags":{},"order":12,"mode":0,"outputs":[{"name":"text","type":"*","links":[104],"shape":3,"label":"text","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"title":"模特prompt","properties":{"Node name for S&R":"CR Text"},"widgets_values":["${FACE.extInfo.modelDesc} wearing "],"color":"#232","bgcolor":"#353"}],"links":[[28,23,0,24,1,"FLOAT"],[52,50,0,23,0,"IMAGE"],[59,50,0,24,0,"IMAGE"],[104,66,0,65,0,"*"],[111,83,0,85,0,"SAM_MODEL"],[112,84,0,85,1,"GROUNDING_DINO_MODEL"],[118,79,0,90,0,"PIPELINE"],[120,88,0,90,2,"IMAGE"],[121,86,0,90,3,"IMAGE"],[123,50,0,90,4,"IMAGE"],[126,92,0,90,1,"IMAGE"],[127,92,0,88,0,"IMAGE"],[128,92,0,85,2,"IMAGE"],[209,139,0,140,1,"FLOAT"],[211,141,0,139,0,"IMAGE"],[212,141,0,140,0,"IMAGE"],[222,144,0,90,6,"INT"],[223,144,1,90,7,"INT"],[228,140,0,150,0,"IMAGE"],[229,150,0,148,0,"INT,FLOAT"],[230,148,0,149,0,"INT,FLOAT"],[231,149,0,147,1,"INT"],[232,151,0,152,0,"INT,FLOAT"],[234,152,0,147,2,"INT"],[236,153,0,92,0,"*"],[237,153,0,144,0,"IMAGE"],[238,147,0,153,0,"*"],[241,140,0,147,0,"IMAGE"],[244,150,1,151,0,"INT,FLOAT"],[247,159,0,50,0,"*"],[248,154,0,160,0,"*"],[249,161,0,141,0,"*"],[253,81,0,158,0,"IMAGE"],[254,85,1,166,0,"MASK"],[255,166,0,86,0,"MASK"],[268,170,0,172,0,"*"],[286,24,0,199,0,"IMAGE"],[293,205,0,203,1,"STRING"],[294,202,1,203,2,"STRING"],[295,202,0,204,1,"STRING"],[296,202,1,204,2,"STRING"],[297,206,0,205,0,"STRING"],[298,202,0,205,1,"STRING"],[299,65,0,207,0,"*"],[300,207,0,90,5,"STRING"],[301,207,0,203,0,"STRING"],[303,178,0,199,2,"STRING"],[305,174,0,208,0,"STRING"],[306,199,0,65,1,"*"],[308,90,0,204,0,"IMAGE"]],"groups":[],"config":{},"extra":{"ds":{"scale":0.2143588810000001,"offset":{"0":529.47835983924,"1":728.8255602909276}}},"version":0.4,"widget_idx_map":{"90":{"seed":7}},"seed_widgets":{"90":7}}}}}',null,now(),0,'印花上身for背面流程参数配置');

update permission set `config` = 'ADMIN' where action like '/pipeline/%';
update permission set `config` = 'ADMIN' where action like '/server/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/queryCustomByPage';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/addCustom';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/element/getTypes/*';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/sys/oss/fetchToken';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/sys/oss/download';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/logoCombine';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/removeBg';
update permission set `config` = 'ADMIN' where action like '/materialModel/assignPlatformOperator';

-- 0729 场景区分男女
ALTER TABLE `creative_element` MODIFY COLUMN `type` VARCHAR(128) NULL COMMENT '业务类型标签，如果有多个则用逗号隔开';

insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,3 as level,id as parent_id,show_image,1 as `order`,tags,ext_tags,ext_info,memo,'Female' as type,deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'SCENE' and level = 2 and type not in ('autumn and winder male','children') and deleted = 0;

insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,3 as level,id as parent_id,show_image,1 as `order`,tags,ext_tags,ext_info,memo,'Male' as type,deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'SCENE' and level = 2 and type in ('autumn and winder male') and deleted = 0;

insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,3 as level,id as parent_id,show_image,1 as `order`,tags,ext_tags,ext_info,memo,'Child' as type,deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'SCENE' and level = 2 and type in ('children') and deleted = 0;

update creative_element set type =  CONCAT(type,',Female')
where config_key = 'SCENE' and level = 2 and type not in ('autumn and winder male','children') and deleted = 0;

update creative_element set type =  CONCAT(type,',Male')
where config_key = 'SCENE' and level = 2 and type in ('autumn and winder male') and deleted = 0;

update creative_element set type =  CONCAT(type,',Child')
where config_key = 'SCENE' and level = 2 and type in ('children') and deleted = 0;

-- 7.30
insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('CREATIVE_PURE_BG_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"1":{"inputs":{"seed":"${seed}","steps":20,"cfg":"${lora.extInfo.cfg}","sampler_name":"dpmpp_2m_sde_gpu","scheduler":"karras","denoise":1,"model":["135",0],"positive":["205",0],"negative":["122",0],"latent_image":["93",0]},"class_type":"KSampler","_meta":{"title":"KSampler"}},"2":{"inputs":{"ckpt_name":"sdxl/zavychromaxl_v60.safetensors"},"class_type":"CheckpointLoaderSimple","_meta":{"title":"Load Checkpoint"}},"6":{"inputs":{"samples":["1",0],"vae":["2",2]},"class_type":"VAEDecode","_meta":{"title":"VAE Decode"}},"93":{"inputs":{"width":"${width}","height":"${height}","batch_size":"${imageNum}"},"class_type":"EmptyLatentImage","_meta":{"title":"Empty Latent Image"}},"122":{"inputs":{"text":["171",0],"token_normalization":"none","weight_interpretation":"A1111","clip":["135",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"135":{"inputs":{"lora_name":"${lora.loraName}","strength_model":1,"strength_clip":1,"model":["2",0],"clip":["2",1]},"class_type":"LoraLoader","_meta":{"title":"Load LoRA"}},"141":{"inputs":{"model_name":"bbox/face_yolov8m.pt"},"class_type":"UltralyticsDetectorProvider","_meta":{"title":"UltralyticsDetectorProvider"}},"142":{"inputs":{"model_name":"sam_vit_b_01ec64.pth","device_mode":"Prefer GPU"},"class_type":"SAMLoader","_meta":{"title":"SAMLoader (Impact)"}},"144":{"inputs":{"text":"realistic, ${FACE.extTags}","token_normalization":"none","weight_interpretation":"A1111","clip":["215",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"145":{"inputs":{"text":"EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,","token_normalization":"none","weight_interpretation":"A1111","clip":["215",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"152":{"inputs":{"image":"${FACE.extInfo.faceImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"Load Image"}},"171":{"inputs":{"text":"(Breasts exposed:1.2), (Butt naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, worst quality, (mannequin:1.2), blurry, low quality, bad quality, NSFW, text, watermark, bad hands, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}","text_b":"","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"172":{"inputs":{"prompts":"${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.lens}${SCENE.extInfo.style}","seed":"${promptSeed}"},"class_type":"ConrainRandomPrompts","_meta":{"title":"conrain random prompts"}},"177":{"inputs":{"text":"${outputPath}","text_b":"${fileNamePrefix}","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"179":{"inputs":{"text":["185",0],"path":["232",0],"filename_prefix":["177",1],"filename_delimiter":"_","filename_number_padding":4},"class_type":"Save Text File","_meta":{"title":"Save Text File"}},"185":{"inputs":{"delimiter":" ","clean_whitespace":"true","text_a":["201",0],"text_b":["172",0],"text_c":["200",0],"text_d":["171",0]},"class_type":"Text Concatenate","_meta":{"title":"Text Concatenate"}},"200":{"inputs":{"string":"【negative】:"},"class_type":"String to Text","_meta":{"title":"String to Text"}},"201":{"inputs":{"string":"【positive】:"},"class_type":"String to Text","_meta":{"title":"String to Text"}},"205":{"inputs":{"text":["172",0],"token_normalization":"none","weight_interpretation":"A1111","clip":["135",1]},"class_type":"BNK_CLIPTextEncodeAdvanced","_meta":{"title":"CLIP Text Encode (Advanced)"}},"209":{"inputs":{"swap_model":"inswapper_128.onnx","facedetection_model":"retinaface_resnet50","face_restore_model":"GFPGANv1.4.pth","parse_model":"parsenet"},"class_type":"LoadConrainReactorModels","_meta":{"title":"Load ConrainReactor Models"}},"210":{"inputs":{"enabled":true,"face_restore_visibility":"${FACE.extInfo.faceRestoreVisibility}","codeformer_weight":"0.7","detect_gender_input":"no","detect_gender_source":"no","input_faces_index":"0","source_faces_index":"0","console_log_level":1,"keep_largest":"yes","input_image":["211",0],"swap_model":["209",0],"facedetection":["209",1],"face_restore_model":["209",2],"faceparse_model":["209",3],"source_image":["152",0]},"class_type":"ConrainReActorFaceSwap","_meta":{"title":"ConrainReactor Fast Face Swap"}},"211":{"inputs":{"guide_size":384,"guide_size_for":true,"max_size":512,"seed":"${faceSeed}","steps":8,"cfg":"${faceCfg}""sampler_name":"euler","scheduler":"normal","denoise":0.4,"feather":5,"noise_mask":true,"force_inpaint":true,"bbox_threshold":0.5,"bbox_dilation":500,"bbox_crop_factor":3,"sam_detection_hint":"center-1","sam_dilation":0,"sam_threshold":0.93,"sam_bbox_expansion":0,"sam_mask_hint_threshold":0.7,"sam_mask_hint_use_negative":"False","drop_size":10,"wildcard":"","cycle":1,"top_k":1,"inpaint_model":0,"noise_mask_feather":0,"image":["6",0],"model":["215",0],"clip":["215",1],"vae":["215",2],"positive":["144",0],"negative":["145",0],"bbox_detector":["141",0],"sam_model_opt":["142",0]},"class_type":"FaceDetailer","_meta":{"title":"FaceDetailer"}},"215":{"inputs":{"ckpt_name":"真实系：majicmixRealistic_v7.safetensors"},"class_type":"CheckpointLoaderSimple","_meta":{"title":"Load Checkpoint"}},"216":{"inputs":{"output_path":["177",0],"filename_prefix":["177",1],"extension":"${fileType}","dpi":100,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"${embedWorkflow}","use_time_str":"true","output_as_root":"true","images":["238",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"232":{"inputs":{"delimiter":"/","clean_whitespace":"false","text_a":["233",0],"text_b":["177",0]},"class_type":"Text Concatenate","_meta":{"title":"Text Concatenate"}},"233":{"inputs":{"string":"output"},"class_type":"String to Text","_meta":{"title":"String to Text"}},"235":{"inputs":{"torchscript_jit":"default","image":["236",0]},"class_type":"InspyrenetRembg","_meta":{"title":"Inspyrenet Rembg"}},"236":{"inputs":{"upscale_model":"4xUltrasharp_4xUltrasharpV10.pt","mode":"rescale","rescale_factor":2,"resize_width":1024,"resampling_method":"lanczos","supersample":"true","rounding_modulus":8,"image":["210",0]},"class_type":"CR Upscale Image","_meta":{"title":"🔍 CR Upscale Image"}},"238":{"inputs":{"x":0,"y":0,"resize_source":false,"destination":["248",0],"source":["258",0],"mask":["235",1]},"class_type":"ImageCompositeMasked","_meta":{"title":"ImageCompositeMasked"}},"248":{"inputs":{"width":["261",4],"height":["261",5],"batch_size":1,"color":16777215},"class_type":"EmptyImage","_meta":{"title":"EmptyImage"}},"258":{"inputs":{"image":["235",0]},"class_type":"ImageRGBA2RGB","_meta":{"title":"Convert RGBA to RGB 🌌 ReActor"}},"261":{"inputs":{"image":["236",0]},"class_type":"Image Size to Number","_meta":{"title":"Image Size to Number"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":261,"last_link_id":443,"nodes":[{"id":122,"type":"BNK_CLIPTextEncodeAdvanced","pos":[2024,-699],"size":{"0":226.8000030517578,"1":160},"flags":{},"order":20,"mode":0,"inputs":[{"link":290,"name":"clip","label":"clip","type":"CLIP"},{"widget":{"name":"text"},"link":328,"name":"text","label":"text","type":"STRING"}],"outputs":[{"shape":3,"name":"CONDITIONING","slot_index":0,"links":[211],"label":"CONDITIONING","type":"CONDITIONING"}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["(Breasts exposed:1.2), (Butt naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, worst quality, (mannequin:1.2), blurry, low quality, bad quality, NSFW, text, watermark, bad hands, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}","none","A1111"],"color":"#322","bgcolor":"#533"},{"id":171,"type":"Text String","pos":[1539,-519],"size":{"0":315,"1":190},"flags":{},"order":0,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[315,328],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["(Breasts exposed:1.2), (Butt naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, worst quality, (mannequin:1.2), blurry, low quality, bad quality, NSFW, text, watermark, bad hands, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}","","",""]},{"id":200,"type":"String to Text","pos":[4528,-376],"size":{"0":315,"1":58},"flags":{"collapsed":true},"order":1,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[318],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["【negative】:"]},{"id":201,"type":"String to Text","pos":[4532,-440],"size":{"0":315,"1":58},"flags":{"collapsed":true},"order":2,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[319],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["【positive】:"]},{"id":142,"type":"SAMLoader","pos":[3330,-410],"size":{"0":315,"1":82},"flags":{},"order":3,"mode":0,"outputs":[{"shape":3,"name":"SAM_MODEL","slot_index":0,"links":[364],"label":"SAM_MODEL","type":"SAM_MODEL"}],"properties":{"Node name for S&R":"SAMLoader"},"widgets_values":["sam_vit_b_01ec64.pth","Prefer GPU"]},{"id":141,"type":"UltralyticsDetectorProvider","pos":[3320,-570],"size":{"0":315,"1":78},"flags":{},"order":4,"mode":0,"outputs":[{"shape":3,"name":"BBOX_DETECTOR","slot_index":0,"links":[355],"label":"BBOX_DETECTOR","type":"BBOX_DETECTOR"},{"shape":3,"name":"SEGM_DETECTOR","slot_index":1,"links":[],"label":"SEGM_DETECTOR","type":"SEGM_DETECTOR"}],"properties":{"Node name for S&R":"UltralyticsDetectorProvider"},"widgets_values":["bbox/face_yolov8m.pt"]},{"id":6,"type":"VAEDecode","pos":[2899,-1032],"size":{"0":210,"1":46},"flags":{"collapsed":false},"order":23,"mode":0,"inputs":[{"link":213,"name":"samples","label":"samples","type":"LATENT"},{"link":8,"name":"vae","label":"vae","type":"VAE"}],"outputs":[{"name":"IMAGE","slot_index":0,"links":[361],"label":"IMAGE","type":"IMAGE"}],"properties":{"Node name for S&R":"VAEDecode"}},{"id":144,"type":"BNK_CLIPTextEncodeAdvanced","pos":[3310,-1274],"size":{"0":389.95330810546875,"1":157.71157836914062},"flags":{},"order":14,"mode":0,"inputs":[{"link":371,"name":"clip","label":"clip","type":"CLIP"}],"outputs":[{"shape":3,"name":"CONDITIONING","slot_index":0,"links":[352],"label":"CONDITIONING","type":"CONDITIONING"}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["realistic, ${FACE.extTags}","none","A1111"]},{"id":215,"type":"CheckpointLoaderSimple","pos":[2840,-1351],"size":{"0":315,"1":98},"flags":{},"order":5,"mode":0,"outputs":[{"shape":3,"name":"MODEL","slot_index":0,"links":[368],"label":"MODEL","type":"MODEL"},{"shape":3,"name":"CLIP","slot_index":1,"links":[369,371,372],"label":"CLIP","type":"CLIP"},{"shape":3,"name":"VAE","slot_index":2,"links":[370],"label":"VAE","type":"VAE"}],"properties":{"Node name for S&R":"CheckpointLoaderSimple"},"widgets_values":["真实系：majicmixRealistic_v7.safetensors"]},{"id":145,"type":"BNK_CLIPTextEncodeAdvanced","pos":[3298,-1057],"size":{"0":400,"1":200},"flags":{},"order":15,"mode":0,"inputs":[{"link":372,"name":"clip","label":"clip","type":"CLIP"}],"outputs":[{"shape":3,"name":"CONDITIONING","slot_index":0,"links":[353],"label":"CONDITIONING","type":"CONDITIONING"}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,","none","A1111"]},{"id":209,"type":"LoadConrainReactorModels","pos":[4500,-1550],"size":{"0":315,"1":190},"flags":{},"order":6,"mode":0,"outputs":[{"shape":3,"name":"faceswapper_model","slot_index":0,"links":[342],"label":"faceswapper_model","type":"FACE_MODEL"},{"shape":3,"name":"facedetection_model","slot_index":1,"links":[343],"label":"facedetection_model","type":"FACE_MODEL"},{"shape":3,"name":"facerestore_model","slot_index":2,"links":[344],"label":"facerestore_model","type":"FACE_MODEL"},{"shape":3,"name":"faceparse_model","slot_index":3,"links":[345],"label":"faceparse_model","type":"FACE_MODEL"}],"properties":{"Node name for S&R":"LoadConrainReactorModels"},"widgets_values":["inswapper_128.onnx","retinaface_resnet50","GFPGANv1.4.pth","parsenet"]},{"id":135,"type":"LoraLoader","pos":[1420,-1040],"size":{"0":372.77825927734375,"1":126},"flags":{},"order":18,"mode":0,"inputs":[{"link":236,"name":"model","label":"model","type":"MODEL"},{"link":237,"name":"clip","label":"clip","type":"CLIP"}],"outputs":[{"shape":3,"name":"MODEL","slot_index":0,"links":[288],"label":"MODEL","type":"MODEL"},{"shape":3,"name":"CLIP","slot_index":1,"links":[290,329],"label":"CLIP","type":"CLIP"}],"properties":{"Node name for S&R":"LoraLoader"},"widgets_values":["${lora.loraName}",1,1],"color":"#232","bgcolor":"#353"},{"id":93,"type":"EmptyLatentImage","pos":[2015,-423],"size":{"0":282.0785217285156,"1":106},"flags":{},"order":7,"mode":0,"outputs":[{"shape":3,"name":"LATENT","links":[154],"label":"LATENT","type":"LATENT"}],"properties":{"Node name for S&R":"EmptyLatentImage"},"widgets_values":["${width}","${height}","${imageNum}"]},{"id":232,"type":"Text Concatenate","pos":[5590,-990],"size":{"0":250,"1":142},"flags":{},"order":16,"mode":0,"inputs":[{"widget":{"name":"text_a"},"link":395,"name":"text_a","label":"text_a","type":"STRING"},{"widget":{"name":"text_b"},"link":394,"name":"text_b","label":"text_b","type":"STRING"},{"widget":{"name":"text_c"},"link":null,"name":"text_c","label":"text_c","type":"STRING"},{"widget":{"name":"text_d"},"link":null,"name":"text_d","label":"text_d","type":"STRING"}],"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[390],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text Concatenate"},"widgets_values":["/","false","","","",""]},{"id":233,"type":"String to Text","pos":[5000,-1130],"size":{"0":315,"1":58},"flags":{"collapsed":false},"order":8,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[395],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"String to Text"},"widgets_values":["output"]},{"id":177,"type":"Text String","pos":[4990,-1000],"size":{"0":315,"1":190},"flags":{},"order":9,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[392,394],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","slot_index":1,"links":[385,386],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${outputPath}","${fileNamePrefix}","",""]},{"id":216,"type":"ConrainImageSave","pos":[7640,-1210],"size":{"0":320,"1":266},"flags":{},"order":29,"mode":0,"inputs":[{"link":404,"name":"images","label":"images","type":"IMAGE"},{"widget":{"name":"output_path"},"link":392,"name":"output_path","label":"output_path","type":"STRING"},{"widget":{"name":"filename_prefix"},"link":386,"name":"filename_prefix","label":"filename_prefix","type":"STRING"}],"outputs":[{"shape":3,"name":"image_cnt","slot_index":0,"links":[],"label":"image_cnt","type":"INT"}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["[time(%Y-%m-%d)]","ComfyUI","${fileType}",100,100,"true","false","${embedWorkflow}","true","true"]},{"id":152,"type":"LoadImage","pos":[4510,-1210],"size":{"0":320,"1":314},"flags":{},"order":10,"mode":0,"outputs":[{"shape":3,"name":"IMAGE","slot_index":0,"links":[346],"label":"IMAGE","type":"IMAGE"},{"shape":3,"name":"MASK","links":null,"label":"MASK","type":"MASK"}],"properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${FACE.extInfo.faceImage}","image"]},{"id":235,"type":"InspyrenetRembg","pos":[5990,-1860],"size":{"0":230,"1":90},"flags":{},"order":27,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":396,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[432],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":[434],"shape":3,"label":"MASK","slot_index":1}],"properties":{"Node name for S&R":"InspyrenetRembg"},"widgets_values":["default"]},{"id":248,"type":"EmptyImage","pos":[6430,-2210],"size":{"0":320,"1":130},"flags":{},"order":31,"mode":0,"inputs":[{"name":"width","type":"INT","link":442,"widget":{"name":"width"},"label":"width"},{"name":"height","type":"INT","link":443,"widget":{"name":"height"},"label":"height"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[438],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"EmptyImage"},"widgets_values":[512,512,1,16777215],"color":"#232","bgcolor":"#353"},{"id":236,"type":"CR Upscale Image","pos":[5480,-1860],"size":{"0":315,"1":222},"flags":{},"order":26,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":397,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[396,402],"shape":3,"label":"IMAGE","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"properties":{"Node name for S&R":"CR Upscale Image"},"widgets_values":["4xUltrasharp_4xUltrasharpV10.pt","rescale",2,1024,"lanczos","true",8]},{"id":210,"type":"ConrainReActorFaceSwap","pos":[4990,-1560],"size":{"0":367.79998779296875,"1":370},"flags":{},"order":25,"mode":0,"inputs":[{"link":362,"name":"input_image","label":"input_image","type":"IMAGE"},{"link":342,"name":"swap_model","label":"swap_model","type":"FACE_MODEL"},{"link":343,"name":"facedetection","label":"facedetection","type":"FACE_MODEL"},{"link":344,"name":"face_restore_model","label":"face_restore_model","type":"FACE_MODEL"},{"link":345,"name":"faceparse_model","label":"faceparse_model","type":"FACE_MODEL"},{"link":346,"name":"source_image","label":"source_image","type":"IMAGE"},{"link":null,"name":"face_model","label":"face_model","type":"FACE_MODEL"}],"outputs":[{"shape":3,"name":"IMAGE","slot_index":0,"links":[397],"label":"IMAGE","type":"IMAGE"},{"shape":3,"name":"FACE_MODEL","links":null,"label":"FACE_MODEL","type":"FACE_MODEL"}],"properties":{"Node name for S&R":"ConrainReActorFaceSwap"},"widgets_values":[true,"${FACE.extInfo.faceRestoreVisibility}","0.7","no","no","0","0",1,"yes"]},{"id":185,"type":"Text Concatenate","pos":[4990,-450],"size":{"0":315,"1":178},"flags":{},"order":17,"mode":0,"inputs":[{"widget":{"name":"text_a"},"link":319,"name":"text_a","label":"text_a","type":"STRING"},{"widget":{"name":"text_b"},"link":314,"name":"text_b","label":"text_b","type":"STRING"},{"widget":{"name":"text_c"},"link":318,"name":"text_c","label":"text_c","type":"STRING"},{"widget":{"name":"text_d"},"link":315,"name":"text_d","label":"text_d","type":"STRING"}],"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[309],"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text Concatenate"},"widgets_values":[" ","true","","","",""]},{"id":179,"type":"Save Text File","pos":[7660,-480],"size":{"0":315,"1":154},"flags":{},"order":19,"mode":0,"inputs":[{"widget":{"name":"text"},"link":309,"name":"text","label":"text","type":"STRING"},{"widget":{"name":"path"},"link":390,"name":"path","label":"path","type":"STRING"},{"widget":{"name":"filename_prefix"},"link":385,"name":"filename_prefix","label":"filename_prefix","type":"STRING"}],"properties":{"Node name for S&R":"Save Text File"},"widgets_values":["","./ComfyUI/output/[time(%Y-%m-%d)]","ComfyUI","_",4]},{"id":211,"type":"FaceDetailer","pos":[3880,-1380],"size":{"0":506.4000244140625,"1":904},"flags":{},"order":24,"mode":0,"inputs":[{"link":361,"name":"image","label":"image","type":"IMAGE"},{"link":368,"name":"model","label":"model","type":"MODEL"},{"link":369,"name":"clip","label":"clip","type":"CLIP"},{"link":370,"name":"vae","label":"vae","type":"VAE"},{"link":352,"name":"positive","label":"positive","type":"CONDITIONING"},{"link":353,"name":"negative","slot_index":5,"label":"negative","type":"CONDITIONING"},{"link":355,"name":"bbox_detector","slot_index":6,"label":"bbox_detector","type":"BBOX_DETECTOR"},{"link":364,"name":"sam_model_opt","slot_index":7,"label":"sam_model_opt","type":"SAM_MODEL"},{"link":null,"name":"segm_detector_opt","slot_index":8,"label":"segm_detector_opt","type":"SEGM_DETECTOR"},{"link":null,"name":"detailer_hook","label":"detailer_hook","type":"DETAILER_HOOK"}],"outputs":[{"shape":3,"name":"image","slot_index":0,"links":[362],"label":"image","type":"IMAGE"},{"shape":6,"name":"cropped_refined","slot_index":1,"links":[],"label":"cropped_refined","type":"IMAGE"},{"shape":6,"name":"cropped_enhanced_alpha","slot_index":2,"links":[],"label":"cropped_enhanced_alpha","type":"IMAGE"},{"shape":3,"name":"mask","links":null,"label":"mask","type":"MASK"},{"shape":3,"name":"detailer_pipe","links":null,"label":"detailer_pipe","type":"DETAILER_PIPE"},{"shape":6,"name":"cnet_images","links":null,"label":"cnet_images","type":"IMAGE"}],"properties":{"Node name for S&R":"FaceDetailer"},"widgets_values":[384,true,512,"${faceSeed}","fixed",8,"${faceCfg}""euler","normal",0.4,5,true,true,0.5,500,3,"center-1",0,0.93,0,0.7,"False",10,"",1,1,0,0]},{"id":172,"type":"ConrainRandomPrompts","pos":[1050,-740],"size":{"0":443.5765380859375,"1":408.1977233886719},"flags":{},"order":11,"mode":0,"outputs":[{"shape":3,"name":"prompt","slot_index":0,"links":[314,440],"label":"prompt","type":"STRING"}],"properties":{"Node name for S&R":"ConrainRandomPrompts"},"widgets_values":["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.lens}${SCENE.extInfo.style}","${promptSeed}","randomize"]},{"id":205,"type":"BNK_CLIPTextEncodeAdvanced","pos":[2020,-940],"size":{"0":230,"1":160},"flags":{},"order":21,"mode":0,"inputs":[{"link":329,"name":"clip","label":"clip","type":"CLIP"},{"name":"text","type":"STRING","link":440,"widget":{"name":"text"},"label":"text"}],"outputs":[{"shape":3,"name":"CONDITIONING","slot_index":0,"links":[331],"label":"CONDITIONING","type":"CONDITIONING"}],"properties":{"Node name for S&R":"BNK_CLIPTextEncodeAdvanced"},"widgets_values":["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.lens}${SCENE.extInfo.style}","none","A1111"],"color":"#322","bgcolor":"#533"},{"id":2,"type":"CheckpointLoaderSimple","pos":[1030,-1040],"size":{"0":315,"1":98},"flags":{},"order":12,"mode":0,"outputs":[{"name":"MODEL","slot_index":0,"links":[236],"label":"MODEL","type":"MODEL"},{"name":"CLIP","slot_index":1,"links":[237],"label":"CLIP","type":"CLIP"},{"name":"VAE","slot_index":2,"links":[8],"label":"VAE","type":"VAE"}],"properties":{"Node name for S&R":"CheckpointLoaderSimple"},"widgets_values":["sdxl/zavychromaxl_v60.safetensors"]},{"id":1,"type":"KSampler","pos":[2430,-1030],"size":{"0":320,"1":262},"flags":{},"order":22,"mode":0,"inputs":[{"link":288,"name":"model","label":"model","type":"MODEL"},{"link":331,"name":"positive","label":"positive","type":"CONDITIONING"},{"link":211,"name":"negative","label":"negative","type":"CONDITIONING"},{"link":154,"name":"latent_image","slot_index":3,"label":"latent_image","type":"LATENT"}],"outputs":[{"name":"LATENT","slot_index":0,"links":[213],"label":"LATENT","type":"LATENT"}],"properties":{"Node name for S&R":"KSampler"},"widgets_values":["${seed}","randomize",20,"${lora.extInfo.cfg}","dpmpp_2m_sde_gpu","karras",1]},{"id":238,"type":"ImageCompositeMasked","pos":[7210,-2190],"size":{"0":220,"1":150},"flags":{},"order":30,"mode":0,"inputs":[{"name":"destination","type":"IMAGE","link":438,"label":"destination"},{"name":"source","type":"IMAGE","link":437,"label":"source"},{"name":"mask","type":"MASK","link":434,"label":"mask"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[404],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageCompositeMasked"},"widgets_values":[0,0,false],"color":"#494949","bgcolor":"#353535"},{"id":261,"type":"Image Size to Number","pos":[5995,-2197],"size":{"0":229.20001220703125,"1":126},"flags":{},"order":32,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":441,"label":"image"}],"outputs":[{"name":"width_num","type":"NUMBER","links":null,"shape":3,"label":"width_num"},{"name":"height_num","type":"NUMBER","links":null,"shape":3,"label":"height_num"},{"name":"width_float","type":"FLOAT","links":null,"shape":3,"label":"width_float"},{"name":"height_float","type":"FLOAT","links":null,"shape":3,"label":"height_float"},{"name":"width_int","type":"INT","links":[442],"shape":3,"slot_index":4,"label":"width_int"},{"name":"height_int","type":"INT","links":[443],"shape":3,"slot_index":5,"label":"height_int"}],"properties":{"Node name for S&R":"Image Size to Number"}},{"id":240,"type":"Note","pos":[6453,-2379],"size":{"0":260,"1":110},"flags":{},"order":13,"mode":2,"properties":{"text":""},"widgets_values":["切换颜色，输入以下颜色的数值\n\n红色：16711680\n绿色：65280\n蓝色：255\n白色：16777215\n黑色：0"],"color":"#432","bgcolor":"#653"},{"id":258,"type":"ImageRGBA2RGB","pos":[6445,-1931],"size":{"0":252,"1":26},"flags":{},"order":28,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":432,"label":"image"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[437],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ImageRGBA2RGB"}}],"links":[[8,2,2,6,1,"VAE"],[154,93,0,1,3,"LATENT"],[211,122,0,1,2,"CONDITIONING"],[213,1,0,6,0,"LATENT"],[236,2,0,135,0,"MODEL"],[237,2,1,135,1,"CLIP"],[288,135,0,1,0,"MODEL"],[290,135,1,122,0,"CLIP"],[309,185,0,179,0,"STRING"],[314,172,0,185,1,"STRING"],[315,171,0,185,3,"STRING"],[318,200,0,185,2,"STRING"],[319,201,0,185,0,"STRING"],[328,171,0,122,1,"STRING"],[329,135,1,205,0,"CLIP"],[331,205,0,1,1,"CONDITIONING"],[342,209,0,210,1,"FACE_MODEL"],[343,209,1,210,2,"FACE_MODEL"],[344,209,2,210,3,"FACE_MODEL"],[345,209,3,210,4,"FACE_MODEL"],[346,152,0,210,5,"IMAGE"],[352,144,0,211,4,"CONDITIONING"],[353,145,0,211,5,"CONDITIONING"],[355,141,0,211,6,"BBOX_DETECTOR"],[361,6,0,211,0,"IMAGE"],[362,211,0,210,0,"IMAGE"],[364,142,0,211,7,"SAM_MODEL"],[368,215,0,211,1,"MODEL"],[369,215,1,211,2,"CLIP"],[370,215,2,211,3,"VAE"],[371,215,1,144,0,"CLIP"],[372,215,1,145,0,"CLIP"],[385,177,1,179,2,"STRING"],[386,177,1,216,2,"STRING"],[390,232,0,179,1,"STRING"],[392,177,0,216,1,"STRING"],[394,177,0,232,1,"STRING"],[395,233,0,232,0,"STRING"],[396,236,0,235,0,"IMAGE"],[397,210,0,236,0,"IMAGE"],[404,238,0,216,0,"IMAGE"],[432,235,0,258,0,"IMAGE"],[434,235,1,238,2,"MASK"],[437,258,0,238,1,"IMAGE"],[438,248,0,238,0,"IMAGE"],[440,172,0,205,1,"STRING"],[441,236,0,261,0,"IMAGE"],[442,261,4,248,0,"INT"],[443,261,5,248,1,"INT"]],"groups":[],"config":{},"extra":{"ds":{"scale":0.37974983358326925,"offset":{"0":-2802.3236738148935,"1":2373.4500275751343}}},"version":0.4,"widget_idx_map":{"1":{"seed":0,"sampler_name":4,"scheduler":5},"172":{"seed":1},"211":{"seed":3,"sampler_name":7,"scheduler":8}},"seed_widgets":{"1":0,"172":1,"211":3}}}}}',null,now(),0,'纯色背景创作流程参数配置');

-- 0729 渠道商配置
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/user/getPageUser';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/distributor/%';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/distributorCustomer/%';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/distributorSettlement/queryByPage';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/orderSettlement/queryByPage';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/user/sub/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/user/deleteById';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialInfo/getById/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/getAndSync/%';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/point/queryByImage';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/userProfile/queryUserProfileByKey';



-- 8.9 4K放大
insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('IMAGE_UPSCALE_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"1":{"inputs":{"image":"${targetImage}","upload":"image"},"class_type":"LoadImage","_meta":{"title":"Load Image"}},"4":{"inputs":{"upscale_model":"4xUltrasharp_4xUltrasharpV10.pt","mode":"rescale","rescale_factor":["21",0],"resize_width":3072,"resampling_method":"lanczos","supersample":"true","rounding_modulus":8,"image":["1",0]},"class_type":"CR Upscale Image","_meta":{"title":"🔍 CR Upscale Image"}},"8":{"inputs":{"output_path":["10",0],"filename_prefix":["10",1],"extension":"jpg","dpi":300,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"false","use_time_str":"true","output_as_root":"true","images":["4",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"10":{"inputs":{"text":"${outputPath}","text_b":"${fileNamePrefix}","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"21":{"inputs":{"value":"${rescaleFactor}"},"class_type":"ImpactFloat","_meta":{"title":"ImpactFloat"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":22,"last_link_id":15,"nodes":[{"id":10,"type":"Text String","pos":[910,500],"size":{"0":360,"1":190},"flags":{},"order":0,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[6],"shape":3,"label":"STRING","slot_index":0},{"name":"STRING","type":"STRING","links":[7],"shape":3,"label":"STRING","slot_index":1},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${outputPath}","${fileNamePrefix}","",""]},{"id":1,"type":"LoadImage","pos":[400,140],"size":[240,310],"flags":{},"order":1,"mode":0,"outputs":[{"name":"IMAGE","type":"IMAGE","links":[13],"shape":3,"label":"IMAGE","slot_index":0},{"name":"MASK","type":"MASK","links":null,"shape":3,"label":"MASK"}],"properties":{"Node name for S&R":"LoadImage"},"widgets_values":["${targetImage}","image"]},{"id":4,"type":"CR Upscale Image","pos":[890,140],"size":{"0":380,"1":260},"flags":{},"order":3,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":13,"label":"image"},{"name":"rescale_factor","type":"FLOAT","link":14,"widget":{"name":"rescale_factor"},"label":"rescale_factor"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[5],"shape":3,"label":"IMAGE","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"properties":{"Node name for S&R":"CR Upscale Image"},"widgets_values":["4xUltrasharp_4xUltrasharpV10.pt","rescale",4,3072,"lanczos","true",8]},{"id":8,"type":"ConrainImageSave","pos":[1570,140],"size":{"0":320,"1":270},"flags":{},"order":4,"mode":0,"inputs":[{"name":"images","type":"IMAGE","link":5,"label":"images"},{"name":"output_path","type":"STRING","link":6,"widget":{"name":"output_path"},"label":"output_path"},{"name":"filename_prefix","type":"STRING","link":7,"widget":{"name":"filename_prefix"},"label":"filename_prefix"}],"outputs":[{"name":"image_cnt","type":"INT","links":null,"shape":3,"label":"image_cnt"}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["[time(%Y-%m-%d)]","ComfyUI","jpg",300,100,"true","false","false","true","true"]},{"id":21,"type":"ImpactFloat","pos":[400,540],"size":{"0":280,"1":60},"flags":{},"order":2,"mode":0,"outputs":[{"name":"FLOAT","type":"FLOAT","links":[14],"shape":3,"label":"FLOAT","slot_index":0}],"properties":{"Node name for S&R":"ImpactFloat"},"widgets_values":["${rescaleFactor}"]}],"links":[[5,4,0,8,0,"IMAGE"],[6,10,0,8,1,"STRING"],[7,10,1,8,2,"STRING"],[13,1,0,4,0,"IMAGE"],[14,21,0,4,1,"FLOAT"]],"groups":[],"config":{},"extra":{"ds":{"scale":0.7400249944258184,"offset":[125.10363972623473,265.238502892535]}},"version":0.4,"seed_widgets":{},"widget_idx_map":{}}}}}',null,now(),0,'图片放大流程参数配置');

-- 新流程
--insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
--('IMAGE_UPSCALE_FLOW_PARAMS','{"client_id":"${clientId}","prompt":{"4":{"inputs":{"upscale_model":"4xUltrasharp_4xUltrasharpV10.pt","mode":"rescale","rescale_factor":"${rescaleFactor}","resize_width":4096,"resampling_method":"bicubic","supersample":"true","rounding_modulus":8,"image":["31",0]},"class_type":"CR Upscale Image","_meta":{"title":"🔍 CR Upscale Image"}},"8":{"inputs":{"output_path":["10",0],"filename_prefix":["10",1],"extension":"jpg","dpi":300,"quality":100,"optimize_image":"true","lossless_webp":"false","embed_workflow":"false","use_time_str":"true","output_as_root":"true","images":["4",0]},"class_type":"ConrainImageSave","_meta":{"title":"conrain save image"}},"10":{"inputs":{"text":"${outputPath}","text_b":"${fileNamePrefix}","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}},"30":{"inputs":{"call_code":"\n\n# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\nfrom PIL import Image,ImageOps\nimport torch\nimport numpy as np\n\ndef call(any_a, any_b, any_c, any_d):\n\timage_path = any_a\n\ti = Image.open(image_path)\n\n\ti = ImageOps.exif_transpose(i)\n\n\timage = i.convert(\"RGB\")\n\n\timage = np.array(image).astype(np.float32) / 255.0\n\n\timage = torch.from_numpy(image)[None,]\n\n\ti.close()\n\n\treturn [[image]]","any_a":["32",0]},"class_type":"ConrainPythonExecutor","_meta":{"title":"conrain python executor"}},"31":{"inputs":{"any_a":["30",0]},"class_type":"ConrainAnyToImages","_meta":{"title":"conrain any to images"}},"32":{"inputs":{"text":"${targetImage}","text_b":"","text_c":"","text_d":""},"class_type":"Text String","_meta":{"title":"Text String"}}},"extra_data":{"extra_pnginfo":{"workflow":{"last_node_id":34,"last_link_id":23,"nodes":[{"id":10,"type":"Text String","pos":[1000,190],"size":{"0":360,"1":190},"flags":{},"order":0,"mode":0,"outputs":[{"name":"STRING","type":"STRING","links":[6],"shape":3,"label":"STRING","slot_index":0},{"name":"STRING","type":"STRING","links":[7],"shape":3,"label":"STRING","slot_index":1},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"},{"name":"STRING","type":"STRING","links":null,"shape":3,"label":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${outputPath}","${fileNamePrefix}","",""]},{"id":4,"type":"CR Upscale Image","pos":[980,-170],"size":{"0":380,"1":260},"flags":{},"order":4,"mode":0,"inputs":[{"name":"image","type":"IMAGE","link":20,"label":"image","slot_index":0}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[5],"shape":3,"label":"IMAGE","slot_index":0},{"name":"show_help","type":"STRING","links":null,"shape":3,"label":"show_help"}],"properties":{"Node name for S&R":"CR Upscale Image"},"widgets_values":["4xUltrasharp_4xUltrasharpV10.pt","rescale","${rescaleFactor}",4096,"bicubic","true",8]},{"id":30,"type":"ConrainPythonExecutor","pos":[80,-170],"size":{"0":450,"1":290},"flags":{},"order":2,"mode":0,"inputs":[{"name":"any_a","type":"*","link":21,"label":"any_a"},{"name":"any_b","type":"*","link":null,"label":"any_b"},{"name":"any_c","type":"*","link":null,"label":"any_c"},{"name":"any_d","type":"*","link":null,"label":"any_d"}],"outputs":[{"name":"any","type":"*","links":[19],"shape":3,"label":"any","slot_index":0}],"properties":{"Node name for S&R":"ConrainPythonExecutor"},"widgets_values":["\n\n# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\nfrom PIL import Image,ImageOps\nimport torch\nimport numpy as np\n\ndef call(any_a, any_b, any_c, any_d):\n\timage_path = any_a\n\ti = Image.open(image_path)\n\n\ti = ImageOps.exif_transpose(i)\n\n\timage = i.convert(\"RGB\")\n\n\timage = np.array(image).astype(np.float32) / 255.0\n\n\timage = torch.from_numpy(image)[None,]\n\n\ti.close()\n\n\treturn [[image]]"]},{"id":8,"type":"ConrainImageSave","pos":[1540,-190],"size":{"0":320,"1":270},"flags":{},"order":5,"mode":0,"inputs":[{"name":"images","type":"IMAGE","link":5,"label":"images"},{"name":"output_path","type":"STRING","link":6,"widget":{"name":"output_path"},"label":"output_path"},{"name":"filename_prefix","type":"STRING","link":7,"widget":{"name":"filename_prefix"},"label":"filename_prefix"}],"outputs":[{"name":"image_cnt","type":"INT","links":null,"shape":3,"label":"image_cnt"}],"properties":{"Node name for S&R":"ConrainImageSave"},"widgets_values":["[time(%Y-%m-%d)]","ComfyUI","jpg",300,100,"true","false","false","true","true"]},{"id":31,"type":"ConrainAnyToImages","pos":[660,-170],"size":{"0":210,"1":26},"flags":{},"order":3,"mode":0,"inputs":[{"name":"any_a","type":"*","link":19,"label":"any_a"}],"outputs":[{"name":"IMAGE","type":"IMAGE","links":[20],"shape":3,"label":"IMAGE","slot_index":0}],"properties":{"Node name for S&R":"ConrainAnyToImages"}},{"id":32,"type":"Text String","pos":[-420,-170],"size":{"0":315,"1":190},"flags":{},"order":1,"mode":0,"outputs":[{"shape":3,"name":"STRING","slot_index":0,"links":[21],"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"},{"shape":3,"name":"STRING","links":null,"label":"STRING","type":"STRING"}],"properties":{"Node name for S&R":"Text String"},"widgets_values":["${targetImage}","","",""]}],"links":[[5,4,0,8,0,"IMAGE"],[6,10,0,8,1,"STRING"],[7,10,1,8,2,"STRING"],[19,30,0,31,0,"*"],[20,31,0,4,0,"IMAGE"],[21,32,0,30,0,"*"]],"groups":[],"config":{},"extra":{"ds":{"scale":0.5559917313492245,"offset":[1028.607951349036,810.4399664119198]}},"version":0.4,"widget_idx_map":{},"seed_widgets":{}}}}}',null,now(),0,'图片放大流程参数配置');

update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/imageUpscale';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/monitorImgDownload';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT' where action like '/creative/imageUpscale';

--8.7
ALTER table `pipeline` ADD COLUMN `memo` VARCHAR(128) NULL COMMENT '备注';


CREATE OR REPLACE VIEW user_pipeline_mapping AS
SELECT
    user_id,
    role_type,
    pipeline_id,
    match_type
FROM (
    SELECT
        u.id user_id,
        u.role_type,
        p.id pipeline_id,
        CASE
            WHEN JSON_CONTAINS(p.user_relation->'$.user', CAST(u.id AS JSON)) THEN 'user'
            WHEN JSON_CONTAINS(p.user_relation->'$.role', JSON_QUOTE(u.role_type)) THEN 'role'
            WHEN JSON_UNQUOTE(p.user_relation->'$.isDefault') = 'true' THEN 'isDefault'
            ELSE NULL
        END AS match_type,
        ROW_NUMBER() OVER (PARTITION BY u.id ORDER BY
            FIELD(CASE
                    WHEN JSON_CONTAINS(p.user_relation->'$.user', CAST(u.id AS JSON)) THEN 'user'
                    WHEN JSON_CONTAINS(p.user_relation->'$.role', JSON_QUOTE(u.role_type)) THEN 'role'
                    WHEN JSON_UNQUOTE(p.user_relation->'$.isDefault') = 'true' THEN 'isDefault'
                    ELSE NULL
                END, 'user', 'role', 'isDefault')) AS rn
    FROM
        user u
    JOIN
        pipeline p
    ON
        JSON_CONTAINS(p.user_relation->'$.user', CAST(u.id AS JSON))
        OR JSON_CONTAINS(p.user_relation->'$.role', JSON_QUOTE(u.role_type))
        OR JSON_UNQUOTE(p.user_relation->'$.isDefault') = 'true'
    where u.deleted != 1 and p.deleted != 1
) AS prioritized
WHERE
    rn = 1;

INSERT INTO `pipeline` (`id`,`name`,`user_relation`,`deleted`,`create_time`,`modify_time`,`memo`) VALUES (1,'运营集群','{"role": ["ADMIN", "OPERATOR"]}',0,now(),now(),null);
INSERT INTO `pipeline` (`id`,`name`,`user_relation`,`deleted`,`create_time`,`modify_time`,`memo`) VALUES (2,'客户集群','{"role": ["MERCHANT"], "isDefault": true}',0,now(),now(),null);

INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (1,'A100',1,'http://*************',null,null,1,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (2,'',2,'11171','FILE_SERVICE',1,1,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (3,'',2,'11172','IMAGE_GENERATE',1,1,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (4,'',2,'11173','IMAGE_GENERATE',1,1,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (5,'',2,'11174','IMAGE_GENERATE',1,1,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (6,'A100',1,'http://*************',null,null,2,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (7,'',2,'11175','IMAGE_GENERATE',6,2,0,now(),now(),'ENABLE');
INSERT INTO `server` (`id`,`name`,`level`,`config`,`type`,`parent_id`,`pipeline_id`,`deleted`,`create_time`,`modify_time`,`status`) VALUES (8,'',2,'11171','FILE_SERVICE',6,2,0,now(),now(),'ENABLE');

--8.12 渠道商代上传素材、创作、精选图
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialInfo/clothExamCfg';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/userProfile/setUserProfileByKey';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialInfo/checkMaterialNameExists';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialInfo/create';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/distributor/queryCustomerPoint';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/file/upload';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/getConfig';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/getTypes/*';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryActiveAndTodayList';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/getMerchantRecentSceneElements';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/create';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/point/predict';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/creative/queryExampleImages';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryByPage';
update permission set `config` = 'ADMIN,DISTRIBUTOR' where action like '/materialModel/changeExampleImages';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/imageLike';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/fetchTaskImage/*';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/fetchTaskInfo';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/repairHands';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryModels4HistoryTasks';

--
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialModel/querySystemList';

update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialModel/queryDetailShowImage';

-- 图生视频
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/createVideo';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/batchQueryAndSync';

-- 渠道商代传服装
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/distributor/queryCustomerMusePoint';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialModel/updateById';

-- 渠道商充值
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/orderInfo/checkIfCanShowNewbiePlan';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/orderInfo/getPricePlan';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/userPointLog/queryPointUsageInfoByPage';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/orderInfo/queryMerchantTopupByPage';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/wx/pay/%';

-- 渠道运营
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialModel/updateById';

-- 0925 服务器别名配置
ALTER table `server` ADD COLUMN `config_alias` VARCHAR(128) NULL COMMENT '服务地址别名';

-- 9月需求
update creative_element set type = CONCAT(type, ',front view') where level = 3 and config_key = 'SCENE' and deleted != 1;
update creative_element set type = CONCAT(type, ',front view') where level = 2 and config_key = 'SCENE' and deleted != 1;

update creative_element set type = CONCAT(type, ',front view') where level = 2 and config_key = 'FACE' and deleted !=1;

insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,3 as level,id as parent_id,show_image,1 as `order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'FACE' and level = 2 and deleted = 0;

-- 增加判断当前用户是否vip接口
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/user/isVip';

-- flux数据初始化
update creative_element set type = CONCAT(type, ',v_1') where level = 3 and config_key = 'SCENE' and deleted != 1;
update creative_element set type = CONCAT(type, ',v_1') where level = 2 and config_key = 'SCENE' and deleted != 1;

update creative_element set type = CONCAT(type, ',v_1') where level = 3 and config_key = 'FACE' and deleted !=1;
update creative_element set type = CONCAT(type, ',v_1') where level = 2 and config_key = 'FACE' and deleted !=1;

insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,3 as level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,REPLACE(type, 'v_1', 'v_2') as type,deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'FACE' and level = 3 and deleted = 0 and parent_id in (
select id from creative_element where config_key = 'FACE' and level = 2 and deleted = 0 and type not like '%v_2%'
);

update creative_element set type = CONCAT(type, ',v_2') where config_key = 'FACE' and level = 2 and deleted = 0 and type not like '%v_2%';

-- 服装打标
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialModel/markCloth';

-- 精选图精修
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR' where action like '/creative/applyRefine';

-- 初始化场景背面
insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,level,parent_id,show_image,`order`+2,
	CONCAT('a back-view of a female model is standing in a relaxed posture. The model’s weight should be shifted slightly to one leg for a casual stance. ',tags) tags,
	ext_tags,
	JSON_REMOVE(ext_info, '$.lens', '$.style', '$.negative') ext_info,
	memo,
	REPLACE(type, 'front view', 'back view') type,
	deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'SCENE' and level =3 and deleted = 0 and type like '%v_2%' and name not in ('浅色棚拍','城市天际线','动物连体衣专用') and type like '%Female%';

insert into creative_element (name,config_key,level,parent_id,show_image,`order`,tags,ext_tags,ext_info,memo,type,deleted,status,belong,user_id,operator_id)
select name,config_key,level,parent_id,show_image,`order`+2,
	CONCAT('a back-view of a male model is standing in a relaxed posture. The model’s weight should be shifted slightly to one leg for a casual stance. ',tags) tags,
	ext_tags,
	JSON_REMOVE(ext_info, '$.lens', '$.style', '$.negative') ext_info,
	memo,
	REPLACE(type, 'front view', 'back view') type,
	deleted,status,belong,user_id,operator_id
from creative_element where config_key = 'SCENE' and level =3 and deleted = 0 and type like '%v_2%' and name not in ('浅色棚拍','城市天际线','动物连体衣专用') and type not like '%Female%' and type not like '%Child%';


-- 初始化系统配置
insert into system_config (conf_key,conf_value,conf_value_next,effect_time,operator_id,memo) values
('MERCHANT_PREFERENCE','{}','{}',now(),0,'商家偏好设置');

INSERT INTO `system_config` (`conf_key`, `conf_value`, `conf_value_next`, `status`, `effect_time`, `memo`, `operator_id`, `create_time`, `modify_time`)
VALUES
	('COLOR_NUMBER_CFG', '{\"100000\": 3}', NULL, 'ACTIVE', '2024-10-17 19:07:20', '服装颜色数量白名单，json string，{\"100010\":5}', 100000, '2024-10-17 19:07:20', '2024-10-17 19:08:45');

update permission set `config` = 'ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR' where action like '/materialInfo/getMaxColorNumberCfg';
update permission set `config` = 'ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR' where action like '/creative/downloadAll';

update permission set `config` = 'ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR' where action like '/sys/queryCreativePreference';

--11.18 prompt字典
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/promptDict/querySystemCollocation','null','ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR',1,null,'2024-11-18 18:17:47','2024-11-18 18:19:47');

INSERT INTO `prompt_dict` (`id`, `word`, `prompt`, `type`, `show_image`, `tags`, `memo`, `user_id`, `operator_id`, `create_time`, `modify_time`)
VALUES
	(1,'运动鞋','sneakers','CLOTH_COLLOCATION',NULL,'system,shoe',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:34'),
	(2,'高跟鞋','high heels','CLOTH_COLLOCATION',NULL,'system,shoe',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:36'),
	(3,'马丁鞋','Martin boots','CLOTH_COLLOCATION',NULL,'system,shoe',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:39'),
	(4,'居家袜子','home socks','CLOTH_COLLOCATION',NULL,'system,shoe',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:42'),
	(5,'光脚','barefoot','CLOTH_COLLOCATION',NULL,'system,shoe',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:45'),
	(6,'运动内衣','sports bra','CLOTH_COLLOCATION',NULL,'system,tops',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:49'),
	(7,'T恤','t-shirt','CLOTH_COLLOCATION',NULL,'system,tops',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:52'),
	(8,'牛仔裤','jeans','CLOTH_COLLOCATION',NULL,'system,bottoms',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:44:58'),
	(9,'短裙','short skirt','CLOTH_COLLOCATION',NULL,'system,bottoms',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:29'),
	(10,'长裤','long pants','CLOTH_COLLOCATION',NULL,'system,bottoms',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:34'),
	(11,'针织帽子','knit hat','CLOTH_COLLOCATION',NULL,'system,others',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:42'),
	(12,'夸张的配饰耳环','exaggerated accessory earrings','CLOTH_COLLOCATION',NULL,'system,others',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:45'),
	(13,'鸭舌帽','baseball cap','CLOTH_COLLOCATION',NULL,'system,others',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:47'),
	(14,'斜挎包','crossbody bag','CLOTH_COLLOCATION',NULL,'system,others',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:48'),
	(15,'咖啡杯','coffee cup','CLOTH_COLLOCATION',NULL,'system,props',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:53'),
	(16,'时尚杂志','fashion magazine','CLOTH_COLLOCATION',NULL,'system,props',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:57'),
	(17,'登山杖','hiking pole','CLOTH_COLLOCATION',NULL,'system,props',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:58'),
	(18,'手捧花','bouquet','CLOTH_COLLOCATION',NULL,'system,props',NULL,NULL,NULL,'2024-11-18 20:43:51','2024-11-18 20:45:59');

INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/promptDict/queryGarmentList','null','ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR',1,null,now(),now())

-- 11.23
update permission set `config` = 'ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR' where action like '/invoiceInfo/%';
update permission set `config` = 'ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR' where action like '/invoiceTitle/%';

INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/showCase/queryIndex','null','NONE',1,null,now(),now())

INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/login/status','','NONE',1,null,now(),now())

INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/sys/queryAllExperienceModelOpenCfg','','ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR',1,null,now(),now())

-- 12.05
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/promptDict/querySystemScene','null','ADMIN,MERCHANT,OPERATOR,DISTRIBUTOR',1,null,now(),now())

INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/imageCase/badCaseTags','','ADMIN,OPERATOR,DISTRIBUTOR',1,null,now(),now());
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
   VALUES ('/imageCase/addBadCaseTag','','ADMIN,OPERATOR,DISTRIBUTOR',1,null,now(),now());

-- 12.10
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
    VALUES ('/creative/swapFace','','ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT',1,null,now(),now());

-- 12.12
update permission set `config` = 'NONE' where action like '/creative/queryMenusCfg';

INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
  VALUES ('/user/isTrialAccount','','NONE',1,null,now(),now());

-- 12.17
INSERT INTO `system_config` (`conf_key`, `conf_value`, `conf_value_next`, `status`, `effect_time`, `memo`, `operator_id`, `create_time`, `modify_time`)
  VALUES ('TEXT_MODERATION_FORBID_LABEL', 'pornographic_adult,sexual_terms,sexual_suggestive,political_figure,political_entity,political_n,political_p,political_a,violent_extremist,violent_incidents,violent_weapons,contraband_drug,contraband_gambling,contraband_act,contraband_entity,inappropriate_discrimination,inappropriate_ethics,inappropriate_profanity,inappropriate_oral,inappropriate_superstition,inappropriate_nonsense,pt_to_sites,pt_by_recruitment,pt_to_contact,religion_b,religion_t,religion_c,religion_i,religion_h', NULL, 'ACTIVE', '2024-12-17 10:58:13', '文本安全检测禁止标签列表', 100014, '2024-12-17 10:58:13', '2024-12-17 10:58:13');


INSERT INTO `user` (`id`, `nick_name`, `real_name`, `login_id`, `pswd`, `mobile`, `role_type`, `user_type`, `master_id`, `status`, `operator_id`, `register_from`, `point`, `memo`, `login_fail_count`, `last_login_time`, `deleted`, `create_time`, `modify_time`, `custom_role`, `corp_org_id`, `corp_name`, `user_review_info`)
VALUES (1, '系统调度', NULL, '10000000000', NULL, '10000000000', 'SYSTEM', 'MASTER', NULL, 'ENABLED', 1, 'website', NULL, 'No remarks', 0, NULL, 0, '2024-12-22 15:58:20', '2024-12-22 15:58:20', NULL, NULL, NULL, NULL);

--12.26
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
VALUES ('/creative/faceSceneSwitch','','ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT',1,null,now(),now());
--
update permission set `config` = 'ADMIN,OPERATOR' where action like '/creative/queryTryonTasksByPage';
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT' where action like '/creative/getById/*';
-- 01.04
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
VALUES ('/creative/partialRedraw','局部重绘','ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT',1,null,now(),now());

-- 01.06
ALTER TABLE `test_item_group` ADD COLUMN `ext_info` JSON NULL COMMENT '扩展信息';

update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/imageOperate/erase';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/imageOperate/erase/query/';

-- 0110
INSERT INTO `user` (`id`, `nick_name`, `real_name`, `login_id`, `pswd`, `mobile`, `role_type`, `user_type`, `master_id`, `status`, `operator_id`, `register_from`, `point`, `memo`, `login_fail_count`, `last_login_time`, `deleted`, `create_time`, `modify_time`, `custom_role`, `corp_org_id`, `corp_name`, `user_review_info`)
VALUES (2, '自动创作', NULL, '20000000000', NULL, '20000000000', 'SYSTEM', 'MASTER', NULL, 'ENABLED', 1, 'website', NULL, 'No remarks', 0, NULL, 0, '2024-12-22 15:58:20', '2024-12-22 15:58:20', NULL, NULL, NULL, NULL);

-- 0113
ALTER TABLE `merchant_preference` ADD COLUMN `enable_auto_creative` BOOLEAN NULL COMMENT '是否开启自动创作';
ALTER TABLE `merchant_preference` ADD COLUMN `image_num` INT UNSIGNED NULL COMMENT '图片数量';
ALTER TABLE `merchant_preference` ADD COLUMN `image_proportion` VARCHAR(16) NULL COMMENT '图片比例，3:4、1:1等';
update permission set `config` = 'ADMIN,OPERATOR' where action like '/creative/fetchStyleImages';

--0114
ALTER TABLE `creative_batch` ADD COLUMN `title` VARCHAR(32) NULL COMMENT '标题';

--0115
ALTER TABLE `test_plan` ADD COLUMN conclusion VARCHAR(1024) NULL COMMENT '结论';
ALTER TABLE `test_item` ADD COLUMN conclusion VARCHAR(1024) NULL COMMENT '结论';


ALTER TABLE `merchant_preference` ADD COLUMN `ext_info` JSON NULL COMMENT '扩展信息';

--0122
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/applyRemoveWrinkle';

--0222
ALTER table `comfyui_task` MODIFY COLUMN `req_params` JSON NULL COMMENT '自定义请求参数';
ALTER TABLE `material_model` MODIFY COLUMN `train_detail` JSON NULL COMMENT '训练详情';


--0303
INSERT INTO `system_config` (`conf_key`, `conf_value`, `conf_value_next`, `status`, `effect_time`, `memo`, `operator_id`, `create_time`, `modify_time`)
VALUES
	('SCENE_CLOTH_SCOPE_CFG', '[{\"key\":\"袖子长短\",\"values\":[\"无袖\",\"短袖\",\"中袖\",\"七分袖\",\"长袖\"],\"bodyArea\":\"upper\"},{\"key\":\"衣服长短\",\"values\":[\"短款（肚脐眼）\",\"及腰款（屁股上沿）\",\"长款（屁股下沿）\"],\"bodyArea\":\"upper\"},{\"key\":\"衣服形状\",\"values\":[\"修身\",\"正常\",\"廓形\"],\"bodyArea\":\"upper\"},{\"key\":\"裤子长短\",\"values\":[\"短裤\",\"膝盖上\",\"膝盖下\",\"七分裤\",\"九分裤\",\"正常（及鞋）\",\"拖地\"],\"bodyArea\":\"lower\"},{\"key\":\"裤子形状\",\"values\":[\"legging紧身裤\",\"正常（直筒）\",\"阔腿\",\"喇叭\",\"镰刀\",\"裙裤\"],\"bodyArea\":\"upper\"},{\"key\":\"是否外套\",\"values\":[\"是\",\"否\"],\"bodyArea\":\"upper\"},{\"key\":\"是否是带有内搭的外套\",\"values\":[\"是\",\"否\"],\"bodyArea\":\"upper\"}]', NULL, 'ACTIVE', '2025-02-25 20:20:54', '后台的场景适合的服装类型配置', '100009', '2025-02-25 20:20:54', '2025-03-04 18:51:19');

update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/queryRecommendAutoGenElementsByCloth';


--0311
INSERT INTO `system_config` (`conf_key`, `conf_value`, `conf_value_next`, `status`, `effect_time`, `memo`, `operator_id`, `create_time`, `modify_time`)
VALUES
	('TRAIN_FLOW_PREPARE_VIEW_SCENE', '{\"client_id\":\"${clientId}\",\"prompt\":{\"1570\":{\"inputs\":{\"face_mask\":false,\"background_mask\":false,\"hair_mask\":true,\"body_mask\":false,\"clothes_mask\":false,\"confidence\":0.5,\"images\":[\"1691\",0]},\"class_type\":\"APersonMaskGenerator\",\"_meta\":{\"title\":\"A Person Mask Generator\"}},\"1571\":{\"inputs\":{\"model_name\":\"sam_vit_h_4b8939.pth\",\"device_mode\":\"AUTO\"},\"class_type\":\"SAMLoader\",\"_meta\":{\"title\":\"SAMLoader (Impact)\"}},\"1572\":{\"inputs\":{\"model_name\":\"bbox/face_yolov8m.pt\"},\"class_type\":\"UltralyticsDetectorProvider\",\"_meta\":{\"title\":\"UltralyticsDetectorProvider\"}},\"1573\":{\"inputs\":{\"bbox_threshold\":0.5,\"bbox_dilation\":0,\"crop_factor\":3,\"drop_size\":10,\"sub_threshold\":0.5,\"sub_dilation\":0,\"sub_bbox_expansion\":0,\"sam_mask_hint_threshold\":0.7,\"post_dilation\":30,\"bbox_detector\":[\"1572\",0],\"image\":[\"1691\",0]},\"class_type\":\"ImpactSimpleDetectorSEGS\",\"_meta\":{\"title\":\"Simple Detector (SEGS)\"}},\"1574\":{\"inputs\":{\"detection_hint\":\"center-1\",\"dilation\":0,\"threshold\":0.93,\"bbox_expansion\":0,\"mask_hint_threshold\":0.7,\"mask_hint_use_negative\":\"False\",\"sam_model\":[\"1571\",0],\"segs\":[\"1573\",0],\"image\":[\"1691\",0]},\"class_type\":\"SAMDetectorCombined\",\"_meta\":{\"title\":\"SAMDetector (combined)\"}},\"1575\":{\"inputs\":{\"masks_a\":[\"1574\",0],\"masks_b\":[\"1570\",0]},\"class_type\":\"Masks Add\",\"_meta\":{\"title\":\"Masks Add\"}},\"1603\":{\"inputs\":{\"expand\":2,\"incremental_expandrate\":0,\"tapered_corners\":true,\"flip_input\":false,\"blur_radius\":10,\"lerp_alpha\":1,\"decay_factor\":1,\"fill_holes\":true,\"mask\":[\"1575\",0]},\"class_type\":\"ConrainGrowMaskWithBlur\",\"_meta\":{\"title\":\"Conrain Grow Mask With Blur\"}},\"1656\":{\"inputs\":{\"mask\":[\"1575\",0]},\"class_type\":\"MaskPreview+\",\"_meta\":{\"title\":\"🔧 Mask Preview\"}},\"1657\":{\"inputs\":{\"mask\":[\"1603\",0]},\"class_type\":\"MaskPreview+\",\"_meta\":{\"title\":\"🔧 Mask Preview\"}},\"1658\":{\"inputs\":{\"padding\":0,\"blur\":0,\"mask\":[\"1603\",0],\"image_optional\":[\"1691\",0]},\"class_type\":\"MaskBoundingBox+\",\"_meta\":{\"title\":\"🔧 Mask Bounding Box\"}},\"1662\":{\"inputs\":{\"value\":[\"1658\",2]},\"class_type\":\"JWIntegerToFloat\",\"_meta\":{\"title\":\"Integer to Float\"}},\"1664\":{\"inputs\":{\"value\":[\"1658\",3]},\"class_type\":\"JWIntegerToFloat\",\"_meta\":{\"title\":\"Integer to Float\"}},\"1666\":{\"inputs\":{\"mode\":\"pixels\",\"origin\":\"topleft\",\"x\":[\"1662\",0],\"y\":[\"1664\",0],\"width\":[\"1668\",0],\"height\":[\"1669\",0],\"image_width\":512,\"image_height\":512,\"copy_image_size\":[\"1691\",0]},\"class_type\":\"Create Rect Mask\",\"_meta\":{\"title\":\"Create Rect Mask\"}},\"1668\":{\"inputs\":{\"value\":[\"1658\",4]},\"class_type\":\"JWIntegerToFloat\",\"_meta\":{\"title\":\"Integer to Float\"}},\"1669\":{\"inputs\":{\"value\":[\"1658\",5]},\"class_type\":\"JWIntegerToFloat\",\"_meta\":{\"title\":\"Integer to Float\"}},\"1686\":{\"inputs\":{\"image\":[\"1691\",0]},\"class_type\":\"GetImageSize+\",\"_meta\":{\"title\":\"🔧 Get Image Size\"}},\"1687\":{\"inputs\":{\"x\":0,\"y\":0,\"resize_source\":false,\"destination\":[\"1691\",0],\"source\":[\"1690\",0],\"mask\":[\"1689\",0]},\"class_type\":\"ImageCompositeMasked\",\"_meta\":{\"title\":\"ImageCompositeMasked\"}},\"1688\":{\"inputs\":{\"images\":[\"1687\",0]},\"class_type\":\"PreviewImage\",\"_meta\":{\"title\":\"Preview Image\"}},\"1689\":{\"inputs\":{\"method\":\"intensity\",\"image\":[\"1666\",0]},\"class_type\":\"Image To Mask\",\"_meta\":{\"title\":\"Image To Mask\"}},\"1690\":{\"inputs\":{\"width\":[\"1686\",0],\"height\":[\"1686\",1],\"red\":255,\"green\":255,\"blue\":255},\"class_type\":\"Image Blank\",\"_meta\":{\"title\":\"Image Blank\"}},\"1691\":{\"inputs\":{\"directory\":[\"1694\",0],\"image_load_cap\":0,\"start_index\":0,\"load_always\":false,\"regex\":\"\"},\"class_type\":\"LoadImagesFromDirList_LR\",\"_meta\":{\"title\":\"LoadImagesFromDirList LR\"}},\"1694\":{\"inputs\":{\"text\":\"${clothDir}\"},\"class_type\":\"CR Text\",\"_meta\":{\"title\":\"🔤 CR Text\"}},\"1696\":{\"inputs\":{\"output_path\":[\"1697\",0],\"filename_prefix\":[\"1699\",0],\"extension\":\"jpg\",\"dpi\":100,\"quality\":100,\"optimize_image\":\"true\",\"lossless_webp\":\"false\",\"embed_workflow\":\"false\",\"use_time_str\":\"false\",\"output_as_root\":\"false\",\"images\":[\"1687\",0]},\"class_type\":\"ConrainImageSave\",\"_meta\":{\"title\":\"conrain save image\"}},\"1697\":{\"inputs\":{\"a\":[\"1694\",0],\"b\":\"/views\"},\"class_type\":\"JWStringConcat\",\"_meta\":{\"title\":\"String Concatenate\"}},\"1699\":{\"inputs\":{\"call_code\":\"import re\\nimport json\\n\\n#替换图片后缀为空\\ndef call(any_a, any_b, any_c, any_d):\\n    text = any_a\\n    pattern = r\'\\\\.(png|jpg|jpeg|webp)\\\\b\'\\n    result = re.sub(pattern, \'\', text, flags=re.IGNORECASE)\\n\\n    return [result]\\n\",\"any_a\":[\"1691\",1]},\"class_type\":\"ConrainPythonExecutor\",\"_meta\":{\"title\":\"conrain python executor\"}}},\"extra_data\":{\"extra_pnginfo\":{\"workflow\":{\"last_node_id\":1699,\"last_link_id\":2248,\"nodes\":[{\"id\":1570,\"type\":\"APersonMaskGenerator\",\"pos\":[-7654,2189],\"size\":[315,178],\"flags\":{},\"order\":8,\"mode\":0,\"inputs\":[{\"name\":\"images\",\"type\":\"IMAGE\",\"link\":2058}],\"outputs\":[{\"name\":\"masks\",\"type\":\"MASK\",\"links\":[2042],\"slot_index\":0,\"shape\":3}],\"properties\":{\"Node name for S&R\":\"APersonMaskGenerator\"},\"widgets_values\":[false,false,true,false,false,0.5]},{\"id\":1571,\"type\":\"SAMLoader\",\"pos\":[-7210.44873046875,1977.500732421875],\"size\":[315,82],\"flags\":{},\"order\":0,\"mode\":0,\"inputs\":[],\"outputs\":[{\"name\":\"SAM_MODEL\",\"type\":\"SAM_MODEL\",\"links\":[2039],\"slot_index\":0,\"shape\":3}],\"properties\":{\"Node name for S&R\":\"SAMLoader\"},\"widgets_values\":[\"sam_vit_h_4b8939.pth\",\"AUTO\"]},{\"id\":1572,\"type\":\"UltralyticsDetectorProvider\",\"pos\":[-7660.**********,2056.601806640625],\"size\":[315,78],\"flags\":{},\"order\":1,\"mode\":0,\"inputs\":[],\"outputs\":[{\"name\":\"BBOX_DETECTOR\",\"type\":\"BBOX_DETECTOR\",\"links\":[2038],\"slot_index\":0,\"shape\":3,\"label\":\"BBOX_DETECTOR\"},{\"name\":\"SEGM_DETECTOR\",\"type\":\"SEGM_DETECTOR\",\"shape\":3,\"label\":\"SEGM_DETECTOR\"}],\"properties\":{\"Node name for S&R\":\"UltralyticsDetectorProvider\"},\"widgets_values\":[\"bbox/face_yolov8m.pt\"]},{\"id\":1573,\"type\":\"ImpactSimpleDetectorSEGS\",\"pos\":[-7210.44873046875,2136.500732421875],\"size\":[315,310],\"flags\":{},\"order\":7,\"mode\":0,\"inputs\":[{\"name\":\"bbox_detector\",\"type\":\"BBOX_DETECTOR\",\"link\":2038,\"label\":\"bbox_detector\"},{\"name\":\"image\",\"type\":\"IMAGE\",\"link\":2057,\"label\":\"image\"},{\"name\":\"sam_model_opt\",\"type\":\"SAM_MODEL\",\"shape\":7,\"label\":\"sam_model_opt\"},{\"name\":\"segm_detector_opt\",\"type\":\"SEGM_DETECTOR\",\"shape\":7,\"label\":\"segm_detector_opt\"}],\"outputs\":[{\"name\":\"SEGS\",\"type\":\"SEGS\",\"links\":[2040],\"slot_index\":0,\"shape\":3,\"label\":\"SEGS\"}],\"properties\":{\"Node name for S&R\":\"ImpactSimpleDetectorSEGS\"},\"widgets_values\":[0.5,0,3,10,0.5,0,0,0.7,30]},{\"id\":1574,\"type\":\"SAMDetectorCombined\",\"pos\":[-6830.**********,1976.6014404296875],\"size\":[315,218],\"flags\":{},\"order\":10,\"mode\":0,\"inputs\":[{\"name\":\"sam_model\",\"type\":\"SAM_MODEL\",\"link\":2039},{\"name\":\"segs\",\"type\":\"SEGS\",\"link\":2040},{\"name\":\"image\",\"type\":\"IMAGE\",\"link\":2059}],\"outputs\":[{\"name\":\"MASK\",\"type\":\"MASK\",\"links\":[2041],\"slot_index\":0,\"shape\":3}],\"properties\":{\"Node name for S&R\":\"SAMDetectorCombined\"},\"widgets_values\":[\"center-1\",0,0.93,0,0.7,\"False\"]},{\"id\":1575,\"type\":\"Masks Add\",\"pos\":[-7633,2540],\"size\":[210,46],\"flags\":{},\"order\":12,\"mode\":0,\"inputs\":[{\"name\":\"masks_a\",\"type\":\"MASK\",\"link\":2041},{\"name\":\"masks_b\",\"type\":\"MASK\",\"link\":2042}],\"outputs\":[{\"name\":\"MASKS\",\"type\":\"MASK\",\"links\":[2092,2188],\"slot_index\":0,\"shape\":3}],\"properties\":{\"Node name for S&R\":\"Masks Add\"},\"widgets_values\":[]},{\"id\":1587,\"type\":\"Reroute\",\"pos\":[-7885,2645],\"size\":[75,26],\"flags\":{},\"order\":5,\"mode\":0,\"inputs\":[{\"name\":\"\",\"type\":\"*\",\"link\":2243}],\"outputs\":[{\"name\":\"\",\"type\":\"IMAGE\",\"links\":[2057,2058,2059,2208,2215,2235,2236],\"slot_index\":0}],\"properties\":{\"showOutputText\":false,\"horizontal\":false}},{\"id\":1603,\"type\":\"ConrainGrowMaskWithBlur\",\"pos\":[-7394,2520],\"size\":[340.20001220703125,246],\"flags\":{},\"order\":13,\"mode\":0,\"inputs\":[{\"name\":\"mask\",\"type\":\"MASK\",\"link\":2092}],\"outputs\":[{\"name\":\"mask\",\"type\":\"MASK\",\"links\":[2189,2191],\"slot_index\":0},{\"name\":\"mask_inverted\",\"type\":\"MASK\"}],\"properties\":{\"Node name for S&R\":\"ConrainGrowMaskWithBlur\"},\"widgets_values\":[2,0,true,false,10,1,1,true]},{\"id\":1656,\"type\":\"MaskPreview+\",\"pos\":[-7653,2643],\"size\":[210,246],\"flags\":{},\"order\":14,\"mode\":0,\"inputs\":[{\"name\":\"mask\",\"type\":\"MASK\",\"link\":2188}],\"outputs\":[],\"properties\":{\"Node name for S&R\":\"MaskPreview+\"},\"widgets_values\":[]},{\"id\":1657,\"type\":\"MaskPreview+\",\"pos\":[-7329,2796],\"size\":[210,246],\"flags\":{},\"order\":15,\"mode\":0,\"inputs\":[{\"name\":\"mask\",\"type\":\"MASK\",\"link\":2189}],\"outputs\":[],\"properties\":{\"Node name for S&R\":\"MaskPreview+\"},\"widgets_values\":[]},{\"id\":1658,\"type\":\"MaskBoundingBox+\",\"pos\":[-7020,2520],\"size\":[315,182],\"flags\":{},\"order\":16,\"mode\":0,\"inputs\":[{\"name\":\"mask\",\"type\":\"MASK\",\"link\":2191},{\"name\":\"image_optional\",\"type\":\"IMAGE\",\"link\":2208,\"shape\":7}],\"outputs\":[{\"name\":\"MASK\",\"type\":\"MASK\",\"links\":null},{\"name\":\"IMAGE\",\"type\":\"IMAGE\",\"links\":[],\"slot_index\":1},{\"name\":\"x\",\"type\":\"INT\",\"links\":[2193],\"slot_index\":2},{\"name\":\"y\",\"type\":\"INT\",\"links\":[2196],\"slot_index\":3},{\"name\":\"width\",\"type\":\"INT\",\"links\":[2202],\"slot_index\":4},{\"name\":\"height\",\"type\":\"INT\",\"links\":[2204],\"slot_index\":5}],\"properties\":{\"Node name for S&R\":\"MaskBoundingBox+\"},\"widgets_values\":[0,0]},{\"id\":1662,\"type\":\"JWIntegerToFloat\",\"pos\":[-6680,2590],\"size\":[315,58],\"flags\":{},\"order\":17,\"mode\":0,\"inputs\":[{\"name\":\"value\",\"type\":\"INT\",\"link\":2193,\"widget\":{\"name\":\"value\"}}],\"outputs\":[{\"name\":\"FLOAT\",\"type\":\"FLOAT\",\"links\":[2200],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"JWIntegerToFloat\"},\"widgets_values\":[0]},{\"id\":1664,\"type\":\"JWIntegerToFloat\",\"pos\":[-6680,2720],\"size\":[315,58],\"flags\":{},\"order\":18,\"mode\":0,\"inputs\":[{\"name\":\"value\",\"type\":\"INT\",\"link\":2196,\"widget\":{\"name\":\"value\"}}],\"outputs\":[{\"name\":\"FLOAT\",\"type\":\"FLOAT\",\"links\":[2201],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"JWIntegerToFloat\"},\"widgets_values\":[0]},{\"id\":1666,\"type\":\"Create Rect Mask\",\"pos\":[-6728,2282],\"size\":[315,226],\"flags\":{},\"order\":21,\"mode\":0,\"inputs\":[{\"name\":\"copy_image_size\",\"type\":\"IMAGE\",\"link\":2215,\"shape\":7},{\"name\":\"x\",\"type\":\"FLOAT\",\"link\":2200,\"widget\":{\"name\":\"x\"}},{\"name\":\"y\",\"type\":\"FLOAT\",\"link\":2201,\"widget\":{\"name\":\"y\"}},{\"name\":\"width\",\"type\":\"FLOAT\",\"link\":2203,\"widget\":{\"name\":\"width\"}},{\"name\":\"height\",\"type\":\"FLOAT\",\"link\":2205,\"widget\":{\"name\":\"height\"}}],\"outputs\":[{\"name\":\"IMAGE\",\"type\":\"IMAGE\",\"links\":[2237],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"Create Rect Mask\"},\"widgets_values\":[\"pixels\",\"topleft\",0,0,50,50,512,512]},{\"id\":1668,\"type\":\"JWIntegerToFloat\",\"pos\":[-6680,2830],\"size\":[315,58],\"flags\":{},\"order\":19,\"mode\":0,\"inputs\":[{\"name\":\"value\",\"type\":\"INT\",\"link\":2202,\"widget\":{\"name\":\"value\"}}],\"outputs\":[{\"name\":\"FLOAT\",\"type\":\"FLOAT\",\"links\":[2203],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"JWIntegerToFloat\"},\"widgets_values\":[0]},{\"id\":1669,\"type\":\"JWIntegerToFloat\",\"pos\":[-6680,2950],\"size\":[315,58],\"flags\":{},\"order\":20,\"mode\":0,\"inputs\":[{\"name\":\"value\",\"type\":\"INT\",\"link\":2204,\"widget\":{\"name\":\"value\"}}],\"outputs\":[{\"name\":\"FLOAT\",\"type\":\"FLOAT\",\"links\":[2205],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"JWIntegerToFloat\"},\"widgets_values\":[0]},{\"id\":1686,\"type\":\"GetImageSize+\",\"pos\":[-7610,2973],\"size\":[214.20001220703125,66],\"flags\":{},\"order\":9,\"mode\":0,\"inputs\":[{\"name\":\"image\",\"type\":\"IMAGE\",\"link\":2235}],\"outputs\":[{\"name\":\"width\",\"type\":\"INT\",\"links\":[2233],\"slot_index\":0},{\"name\":\"height\",\"type\":\"INT\",\"links\":[2234],\"slot_index\":1},{\"name\":\"count\",\"type\":\"INT\",\"links\":null}],\"properties\":{\"Node name for S&R\":\"GetImageSize+\"},\"widgets_values\":[]},{\"id\":1687,\"type\":\"ImageCompositeMasked\",\"pos\":[-7179,3160],\"size\":[315,146],\"flags\":{},\"order\":23,\"mode\":0,\"inputs\":[{\"name\":\"destination\",\"type\":\"IMAGE\",\"link\":2236},{\"name\":\"source\",\"type\":\"IMAGE\",\"link\":2230},{\"name\":\"mask\",\"type\":\"MASK\",\"link\":2231,\"shape\":7}],\"outputs\":[{\"name\":\"IMAGE\",\"type\":\"IMAGE\",\"links\":[2232,2244],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"ImageCompositeMasked\"},\"widgets_values\":[0,0,false]},{\"id\":1688,\"type\":\"PreviewImage\",\"pos\":[-7180,3370],\"size\":[210,246],\"flags\":{},\"order\":24,\"mode\":0,\"inputs\":[{\"name\":\"images\",\"type\":\"IMAGE\",\"link\":2232}],\"outputs\":[],\"properties\":{\"Node name for S&R\":\"PreviewImage\"},\"widgets_values\":[]},{\"id\":1690,\"type\":\"Image Blank\",\"pos\":[-7648,3166],\"size\":[315,154],\"flags\":{},\"order\":11,\"mode\":0,\"inputs\":[{\"name\":\"width\",\"type\":\"INT\",\"link\":2233,\"widget\":{\"name\":\"width\"}},{\"name\":\"height\",\"type\":\"INT\",\"link\":2234,\"widget\":{\"name\":\"height\"}}],\"outputs\":[{\"name\":\"IMAGE\",\"type\":\"IMAGE\",\"links\":[2230],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"Image Blank\"},\"widgets_values\":[512,512,255,255,255]},{\"id\":1694,\"type\":\"CR Text\",\"pos\":[-8563,2655],\"size\":[210,96],\"flags\":{},\"order\":2,\"mode\":0,\"inputs\":[],\"outputs\":[{\"name\":\"text\",\"type\":\"*\",\"links\":[2242,2245],\"slot_index\":0},{\"name\":\"show_help\",\"type\":\"STRING\",\"links\":null}],\"properties\":{\"Node name for S&R\":\"CR Text\"},\"widgets_values\":[\"${clothDir}\"]},{\"id\":1697,\"type\":\"JWStringConcat\",\"pos\":[-8263,2874],\"size\":[315,82],\"flags\":{},\"order\":4,\"mode\":0,\"inputs\":[{\"name\":\"a\",\"type\":\"STRING\",\"link\":2245,\"widget\":{\"name\":\"a\"}}],\"outputs\":[{\"name\":\"STRING\",\"type\":\"STRING\",\"links\":[2246],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"JWStringConcat\"},\"widgets_values\":[\"\",\"/views\"]},{\"id\":1691,\"type\":\"LoadImagesFromDirList_LR\",\"pos\":[-8274,2562],\"size\":[315,194],\"flags\":{},\"order\":3,\"mode\":0,\"inputs\":[{\"name\":\"directory\",\"type\":\"STRING\",\"link\":2242,\"widget\":{\"name\":\"directory\"}}],\"outputs\":[{\"name\":\"IMAGE\",\"type\":\"IMAGE\",\"links\":[2243],\"slot_index\":0,\"shape\":6},{\"name\":\"STRING\",\"type\":\"STRING\",\"links\":[2247],\"shape\":6,\"slot_index\":1},{\"name\":\"MASK\",\"type\":\"MASK\",\"links\":null,\"shape\":6}],\"properties\":{\"Node name for S&R\":\"LoadImagesFromDirList_LR\"},\"widgets_values\":[\"\",0,0,false,\"\"]},{\"id\":1699,\"type\":\"ConrainPythonExecutor\",\"pos\":[-8263,3101],\"size\":[481.1280517578125,292.2105712890625],\"flags\":{},\"order\":6,\"mode\":0,\"inputs\":[{\"name\":\"any_a\",\"type\":\"*\",\"link\":2247,\"shape\":7},{\"name\":\"any_b\",\"type\":\"*\",\"link\":null,\"shape\":7},{\"name\":\"any_c\",\"type\":\"*\",\"link\":null,\"shape\":7},{\"name\":\"any_d\",\"type\":\"*\",\"link\":null,\"shape\":7}],\"outputs\":[{\"name\":\"any\",\"type\":\"*\",\"links\":[2248],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"ConrainPythonExecutor\"},\"widgets_values\":[\"import re\\nimport json\\n\\n#替换图片后缀为空\\ndef call(any_a, any_b, any_c, any_d):\\n    text = any_a\\n    pattern = r\'\\\\.(png|jpg|jpeg|webp)\\\\b\'\\n    result = re.sub(pattern, \'\', text, flags=re.IGNORECASE)\\n\\n    return [result]\\n\"]},{\"id\":1696,\"type\":\"ConrainImageSave\",\"pos\":[-6708,3162],\"size\":[315,274],\"flags\":{},\"order\":25,\"mode\":0,\"inputs\":[{\"name\":\"images\",\"type\":\"IMAGE\",\"link\":2244},{\"name\":\"output_path\",\"type\":\"STRING\",\"link\":2246,\"widget\":{\"name\":\"output_path\"}},{\"name\":\"filename_prefix\",\"type\":\"STRING\",\"link\":2248,\"widget\":{\"name\":\"filename_prefix\"}}],\"outputs\":[{\"name\":\"image_cnt\",\"type\":\"INT\",\"links\":null}],\"properties\":{\"Node name for S&R\":\"ConrainImageSave\"},\"widgets_values\":[\"[time(%Y-%m-%d)]\",\"ComfyUI\",\"jpg\",100,100,\"true\",\"false\",\"false\",\"false\",\"false\"]},{\"id\":1689,\"type\":\"Image To Mask\",\"pos\":[-7644,3391],\"size\":[315,58],\"flags\":{},\"order\":22,\"mode\":0,\"inputs\":[{\"name\":\"image\",\"type\":\"IMAGE\",\"link\":2237}],\"outputs\":[{\"name\":\"MASK\",\"type\":\"MASK\",\"links\":[2231],\"slot_index\":0}],\"properties\":{\"Node name for S&R\":\"Image To Mask\"},\"widgets_values\":[\"intensity\"]}],\"links\":[[2038,1572,0,1573,0,\"BBOX_DETECTOR\"],[2039,1571,0,1574,0,\"SAM_MODEL\"],[2040,1573,0,1574,1,\"SEGS\"],[2041,1574,0,1575,0,\"MASK\"],[2042,1570,0,1575,1,\"MASK\"],[2057,1587,0,1573,1,\"IMAGE\"],[2058,1587,0,1570,0,\"IMAGE\"],[2059,1587,0,1574,2,\"IMAGE\"],[2092,1575,0,1603,0,\"MASK\"],[2188,1575,0,1656,0,\"MASK\"],[2189,1603,0,1657,0,\"MASK\"],[2191,1603,0,1658,0,\"MASK\"],[2193,1658,2,1662,0,\"INT\"],[2196,1658,3,1664,0,\"INT\"],[2200,1662,0,1666,1,\"FLOAT\"],[2201,1664,0,1666,2,\"FLOAT\"],[2202,1658,4,1668,0,\"INT\"],[2203,1668,0,1666,3,\"FLOAT\"],[2204,1658,5,1669,0,\"INT\"],[2205,1669,0,1666,4,\"FLOAT\"],[2208,1587,0,1658,1,\"IMAGE\"],[2215,1587,0,1666,0,\"IMAGE\"],[2230,1690,0,1687,1,\"IMAGE\"],[2231,1689,0,1687,2,\"MASK\"],[2232,1687,0,1688,0,\"IMAGE\"],[2233,1686,0,1690,0,\"INT\"],[2234,1686,1,1690,1,\"INT\"],[2235,1587,0,1686,0,\"IMAGE\"],[2236,1587,0,1687,0,\"IMAGE\"],[2237,1666,0,1689,0,\"IMAGE\"],[2242,1694,0,1691,0,\"STRING\"],[2243,1691,0,1587,0,\"*\"],[2244,1687,0,1696,0,\"IMAGE\"],[2245,1694,0,1697,0,\"STRING\"],[2246,1697,0,1696,1,\"STRING\"],[2247,1691,1,1699,0,\"*\"],[2248,1699,0,1696,2,\"STRING\"]],\"groups\":[{\"id\":3,\"title\":\"Group\",\"bounding\":[-7671,1903,1560,1168],\"color\":\"#3f789e\",\"font_size\":24,\"flags\":{}}],\"config\":{},\"extra\":{\"ds\":{\"scale\":0.*****************,\"offset\":[10005.************,-1755.*************]}},\"version\":0.4,\"widget_idx_map\":{},\"seed_widgets\":{}}}}}', NULL, 'ACTIVE', '2025-03-11 14:32:06', '风格场景流程：样本预处理，白头化', '100009', '2025-03-11 14:32:06', '2025-03-11 14:32:06');

--0314
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/sys/queryConfigByKeys';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialInfo/uploadCommonMaterialInfo';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/userFavor/getAllFavorInfoWithBlobs';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/materialModel/assignElementModelToUser';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryCreateImageCnt';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryAllMerchantPreference/%';


-- 0314 基础款换衣
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/pictureMatting';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/basicChangingClothes';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/intelligentRecommendation';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/getById/*';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/queryImagesByElement';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/element/batchQueryById';
update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/getById/task/';
--0315 添加演示账号权限
update permission set config = concat(config,',DEMO_ACCOUNT') where config like '%MERCHANT%' and config not like '%DEMO_ACCOUNT%' and action not like '/invoice%';

--3.21 添加权限，优化material_model_view性能
INSERT INTO `permission` (`action`,`name`,`config`,`allowed_sub`,`memo`,`create_time`,`modify_time`)
VALUES ('/creative/queryCreateImageCnt','查询图片数量','ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT',1,null,now(),now());

update permission set `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT' where action like '/creative/queryCreateImageCnt';

CREATE OR REPLACE VIEW `material_model_view` AS
select t.*,
	u.nick_name AS `operator_nick`,
    v.nick_name AS `user_nick`
from (
	SELECT *
	FROM `material_model`
	WHERE deleted = 0
) t
LEFT JOIN `user` u ON t.`operator_id` = u.`id`
LEFT JOIN `user` v ON t.`user_id` = v.`id`;


-- 0325
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/orderInfo/checkIfShowTopup';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryAllMerchantPreference/%';
update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/creative/queryCreateImageCnt';


update permission set `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR' where action like '/sys/checkCanShowProportion';

-- 3.29
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT,DEMO_ACCOUNT' where action like '/element/queryByPage';
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT,DEMO_ACCOUNT' where action like '/element/queryFavorElements';

insert into permission (`action`, name, config) values ('/element/queryFavorElements', '分页查询收藏元素', 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT,DEMO_ACCOUNT')



-- 0329
INSERT INTO `permission` (`action`, `name`, `config`, `allowed_sub`, `memo`, `create_time`, `modify_time`)
VALUES
	('/element/queryPubViewByPage', '渠道商，分页查询所有创作元素', 'ADMIN,DISTRIBUTOR', '1', NULL, now(), now());

update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT,DEMO_ACCOUNT' where action like '/element/getBasicChangeConfig';


-- 0407
update permission set `config` = 'NONE' where action like '/sys/queryHomeLoginRegisterConfig';
update permission set `config` = 'NONE' where action like '/login/register';
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT' where action like '/organization/updateById';
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT' where action like '/organization/qiChaCha/query';

/* 组织表部分字段设置为非必填字段 */
ALTER TABLE `organization`
    MODIFY COLUMN `tags` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '组织标签，作为组织类型的补充，预留' AFTER `name`,
    MODIFY COLUMN `creator_master_user_id` int UNSIGNED NULL COMMENT '创建者主账号id' AFTER `org_level`,
    MODIFY COLUMN `creator_user_role_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建人角色类型，DISTRIBUTOR：渠道商' AFTER `creator_master_user_id`,
    MODIFY COLUMN `creator_operator_user_id` int UNSIGNED NULL COMMENT '创建人操作员账号id' AFTER `creator_user_role_type`,
    MODIFY COLUMN `modifier_operator_user_id` int UNSIGNED NULL COMMENT '最近修改人的操作员账号id' AFTER `creator_operator_user_id`,
    MODIFY COLUMN `ext_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '扩展信息' AFTER `modifier_operator_user_id`
;


/* 用户组织关了表部分字段设置为非必填字段 */
ALTER TABLE `user_organization`
    MODIFY COLUMN `creator_operator_user_id` int UNSIGNED NULL COMMENT '创建人操作员账号id' AFTER `org_id`,
    MODIFY COLUMN `modifier_operator_user_id` int UNSIGNED NULL COMMENT '修改人操作员账号id' AFTER `creator_operator_user_id`
;

-- 0409
update permission set `config` = 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT' where action like '/organization/updateCorpAuthInfo';

-- 0409
update permission set `config` = 'NONE' where action like '/eventTrackingRecord/record';
update permission set `config` = 'NONE' where action like '/eventTrackingRecord/create';

-- 4.10
insert into permission (`action`, name, config) values ('/creative/facePinching', '模特捏脸', 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT,DEMO_ACCOUNT')
update permission set config = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT' where action = '/creative/facePinching'

-- 0410 审核员权限
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/workSchedule/queryList';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/workflowTask/create';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/workflowTask/updateById';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/workflowTask/updateByBizId';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/workflowTask/queryList';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/workflowTask/queryGroup';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT,REVIEWER' WHERE action = '/creative/queryByPage';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/materialModel/deliver';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/materialModel/queryListWithBlogs';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/user/allMaster';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,REVIEWER' WHERE action = '/materialModel/confirmTrainLora';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT,REVIEWER' WHERE action = '/element/getConfig';
UPDATE permission SET `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR,REVIEWER' WHERE action = '/orderInfo/checkIfShowTopup';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,REVIEWER' WHERE action = '/materialModel/getTrainDetail';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT,REVIEWER' WHERE action = '/element/getMerchantRecentSceneElements';
UPDATE permission SET `config` = 'ADMIN,DEMO_ACCOUNT,OPERATOR,MERCHANT,DISTRIBUTOR,REVIEWER' WHERE action = '/sys/queryConfigByKeys';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,MERCHANT,DEMO_ACCOUNT,REVIEWER' WHERE action = '/userProfile/queryList';
UPDATE permission SET `config` = 'ADMIN,REVIEWER' WHERE action = '/materialModel/confirmCanDeliver';

-- 0421
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE action = '/file/batchDownload';

-- 0422
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE action = '/userProfile/create';

-- 0425
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE action = '/user/queryRelatedAccounts';

-- 0427
UPDATE permission SET `config` = 'ADMIN,DISTRIBUTOR' WHERE action = '/distributor/queryAllStaffs';

-- 04428
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE action = '/statsUserOperate/getUserCreativeData';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE action = '/statsUserOperate/queryByPage';
UPDATE permission SET `config` = 'ADMIN,OPERATOR' WHERE action = '/user/allMaster';
UPDATE permission SET `config` = 'ADMIN,OPERATOR' WHERE action = '/user/querySub/*';

-- 0514
UPDATE permission SET `config` = 'ADMIN,OPERATOR,MERCHANT,DISTRIBUTOR,DEMO_ACCOUNT,REVIEWER' WHERE action = '/element/getClothCategoryCfg';


-- 0519
UPDATE permission SET `config` = 'ADMIN,DISTRIBUTOR' WHERE `action` like '%/code/%';


-- 0607
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/fixedCreativeTemplate/create';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/fixedCreativeTemplate/updateById';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/fixedCreativeTemplate/getById/*';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/fixedCreativeTemplate/deleteById';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/fixedCreativeTemplate/queryList';
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/fixedCreativeTemplate/queryByPage';


-- 0607
insert into permission (`action`, name, config) values ('/element/queryHasShowImageChildren', '查询含有参考图的列表', 'ADMIN,OPERATOR,DISTRIBUTOR,MERCHANT,DEMO_ACCOUNT')
UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/creative/fixedPosture/create';

-- 0609
INSERT INTO `user` (`id`, `nick_name`, `real_name`, `login_id`, `pswd`, `mobile`, `role_type`, `user_type`, `master_id`, `status`, `operator_id`, `register_from`, `point`, `memo`, `login_fail_count`, `last_login_time`, `deleted`, `create_time`, `modify_time`, `custom_role`, `corp_org_id`, `corp_name`, `user_review_info`)
VALUES (3, '示例图创作', NULL, '***********', NULL, '***********', 'SYSTEM', 'MASTER', NULL, 'ENABLED', 1, 'website', NULL, 'No remarks', 0, NULL, 0, '2024-12-22 15:58:20', '2024-12-22 15:58:20', NULL, NULL, NULL, NULL);


-- 0610
TRUNCATE TABLE `comfyui_workflow_template`;
INSERT INTO
  `comfyui_workflow_template` (
    `template_key`,
    `template_desc`,
    `template_data`,
    `version`,
    `create_by`,
    `modify_by`
  )
SELECT
  `conf_key` AS `template_key`,
  IFNULL(`memo`, '') AS `template_desc`,
  `conf_value` AS `template_data`,
  '********.0' AS `version`,
  `operator_id` AS `create_by`,
  `operator_id` AS `modify_by`
FROM
  `system_config`
WHERE
  `conf_value` LIKE '%prompt%';

TRUNCATE TABLE `comfyui_workflow_template_active_version`;
INSERT INTO
  `comfyui_workflow_template_active_version` (
    `template_key`,
    `template_desc`,
    `active_version`,
    `create_by`,
    `modify_by`
  )
SELECT
  `template_key`,
  `template_desc`,
  `version` AS `active_version`,
  `create_by` AS `create_by`,
  `modify_by` AS `modify_by`
FROM
  `comfyui_workflow_template`
;


-- 0617
INSERT INTO `permission` (`action`, `name`, `config`, `allowed_sub`, `memo`, `create_time`, `modify_time`)
VALUES
	('/alipay/notify', 'null', 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR', '1', NULL, '2025-06-17 12:12:04', '2025-06-17 13:56:51'),
	('/alipay/query', 'null', 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR', '1', NULL, '2025-06-17 12:12:04', '2025-06-17 13:56:51'),
	('/alipay/qrcode', 'null', 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR', '1', NULL, '2025-06-17 12:12:04', '2025-06-17 13:56:51');

UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` LIKE '/alipay/%';


-- 0618
INSERT INTO `permission` (`action`, `name`, `config`, `allowed_sub`, `memo`, `create_time`, `modify_time`)
VALUES
	('/creative/clothImgAutoSeg', '服装图片自动分割', 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR', '1', NULL, '2025-05-23 20:01:48', '2025-06-18 17:20:40');

UPDATE permission SET `config` = 'ADMIN,OPERATOR,DEMO_ACCOUNT,MERCHANT,DISTRIBUTOR' WHERE `action` like '/creative/clothImgAutoSeg';

-- 0707 服装颜色字典
INSERT INTO prompt_dict(word, prompt, memo, tags, type) VALUES
('酱黑色', 'Caviar', '#2c2f32', 'system,color,black', 'CLOTH_COLOR'),
('陨石色', 'Meteorite', '#353335', 'system,color,black', 'CLOTH_COLOR'),
('乌黑', 'Raven', '#353635', 'system,color,black', 'CLOTH_COLOR'),
('深海军蓝', 'Dark Navy', '#32353d', 'system,color,black', 'CLOTH_COLOR'),
('浓咖啡色', 'Espresso', '#3c383b', 'system,color,black', 'CLOTH_COLOR'),
('墨色', 'India Ink', '#3c434e', 'system,color,black', 'CLOTH_COLOR'),
('亮白色', 'Bright White', '#f1f2f1', 'system,color,white', 'CLOTH_COLOR'),
('神秘蓝', 'Mystic Blue', '#e6eae5', 'system,color,white', 'CLOTH_COLOR'),
('香雪球色', 'White Alyssum', '#f1eeed', 'system,color,white', 'CLOTH_COLOR'),
('水洗蓝', 'Bluewash', '#e1e8e3', 'system,color,white', 'CLOTH_COLOR'),
('蓝晕色', 'Blue Blush', '#d6dfdc', 'system,color,white', 'CLOTH_COLOR'),
('冬白色', 'Winter White', '#f2edd8', 'system,color,white', 'CLOTH_COLOR'),
('鹭白', 'Egret', '#efeee5', 'system,color,white', 'CLOTH_COLOR'),
('鸽羽色', 'Dove', '#aeaaa5', 'system,color,gray', 'CLOTH_COLOR'),
('羽毛灰', 'Feather Gray', '#b9b0a1', 'system,color,gray', 'CLOTH_COLOR'),
('高楼色', 'High-rise', '#abb1b3', 'system,color,gray', 'CLOTH_COLOR'),
('沥青色', 'Asphalt', '#4b4b50', 'system,color,gray', 'CLOTH_COLOR'),
('暴雨色', 'Cloudburst', '#868485', 'system,color,gray', 'CLOTH_COLOR'),
('炭灰色', 'Charcoal Gray', '#6a6a6a', 'system,color,gray', 'CLOTH_COLOR'),
('象皮色', 'Elephant Skin', '#908d87', 'system,color,gray', 'CLOTH_COLOR'),
('巨石色', 'Boulder', '#d3c09b', 'system,color,khaki', 'CLOTH_COLOR'),
('自然色', 'Natural', '#ae927c', 'system,color,khaki', 'CLOTH_COLOR'),
('蘑菇色', 'Fungi', '#8c8076', 'system,color,khaki', 'CLOTH_COLOR'),
('树丛绿', 'Covert Green', '#7d745e', 'system,color,khaki', 'CLOTH_COLOR'),
('咖啡酒色', 'Coffee Liqueur', '#6a5542', 'system,color,khaki', 'CLOTH_COLOR'),
('石灰华', 'Travertine', '#b5a083', 'system,color,khaki', 'CLOTH_COLOR'),
('牡蛎白', 'Oyster White', '#d7ceb3', 'system,color,khaki', 'CLOTH_COLOR'),
('斜纹布色', 'Twill', '#a89e81', 'system,color,khaki', 'CLOTH_COLOR'),
('面包皮色', 'Croissant', '#bfa57e', 'system,color,khaki', 'CLOTH_COLOR'),
('深靛蓝色', 'Mood Indigo', '#374051', 'system,color,blue', 'CLOTH_COLOR'),
('花冠蓝', 'Coronet Blue', '#51718f', 'system,color,blue', 'CLOTH_COLOR'),
('马扎林蓝', 'Mazarine Blue', '#323f7a', 'system,color,blue', 'CLOTH_COLOR'),
('牵牛花蓝', 'Omphalodes', '#bad1e1', 'system,color,blue', 'CLOTH_COLOR'),
('波塞冬蓝', 'Poseidon', '#2a425f', 'system,color,blue', 'CLOTH_COLOR'),
('蓝铃草色', 'Blue Bell', '#92b5d9', 'system,color,blue', 'CLOTH_COLOR'),
('德累斯顿蓝', 'Dresden Blue', '#008ac2', 'system,color,blue', 'CLOTH_COLOR'),
('维多利亚蓝', 'Victoria Blue', '#155da4', 'system,color,blue', 'CLOTH_COLOR'),
('石洗蓝', 'Stonewash', '#7683a0', 'system,color,blue', 'CLOTH_COLOR'),
('海岸蓝', 'Coastal Fjord', '#4d5d83', 'system,color,blue', 'CLOTH_COLOR'),
('火红', 'Fiery Red', '#d12631', 'system,color,red', 'CLOTH_COLOR'),
('珊瑚杏仁', 'Coral Almond', '#e29892', 'system,color,red', 'CLOTH_COLOR'),
('巴贝多樱桃色', 'Barbados Cherry', '#ae1932', 'system,color,red', 'CLOTH_COLOR'),
('山茱萸粉', 'Pink Dogwood', '#f6d6d8', 'system,color,red', 'CLOTH_COLOR'),
('杏花', 'Almond Blossom', '#f5bfcc', 'system,color,red', 'CLOTH_COLOR'),
('橘子网', 'Orange.com', '#d5392c', 'system,color,red', 'CLOTH_COLOR'),
('瓷玫瑰', 'Porcelain Rose', '#e46a6d', 'system,color,red', 'CLOTH_COLOR'),
('冬青浆果', 'Holly Berry', '#b34a5e', 'system,color,red', 'CLOTH_COLOR'),
('峡谷玫瑰色', 'Canyon Rose', '#b06f6b', 'system,color,red', 'CLOTH_COLOR'),
('火砖色', 'Burnt Brick', '#a04e42', 'system,color,red', 'CLOTH_COLOR'),
('薰衣草雾', 'Lavender Fog', '#d5c9dd', 'system,color,purple', 'CLOTH_COLOR'),
('无花果', 'Fig', '#503643', 'system,color,purple', 'CLOTH_COLOR'),
('薰衣草蛋糕', 'Pastel Lavender', '#dba4cc', 'system,color,purple', 'CLOTH_COLOR'),
('薰衣草雾', 'Lavender Mist', '#b091aa', 'system,color,purple', 'CLOTH_COLOR'),
('羽扇豆', 'Lupine', '#bea0c8', 'system,color,purple', 'CLOTH_COLOR'),
('暗影紫', 'Shadow Purple', '#4c2d4c', 'system,color,purple', 'CLOTH_COLOR'),
('紫丁香', 'Royal Lilac', '#754c90', 'system,color,purple', 'CLOTH_COLOR'),
('明亮的紫罗兰色', 'Bright Violet', '#794784', 'system,color,purple', 'CLOTH_COLOR'),
('薄纱紫色', 'Violet Tulle', '#c693c7', 'system,color,purple', 'CLOTH_COLOR'),
('子弹银', 'Silver Bullet', '#8788a2', 'system,color,bluepurple', 'CLOTH_COLOR'),
('柔和淡紫色', 'Misty Lilac', '#bcb8c9', 'system,color,bluepurple', 'CLOTH_COLOR'),
('晚蓝', 'Evening Blue', '#312f48', 'system,color,bluepurple', 'CLOTH_COLOR'),
('蓝鹭', 'Blue Heron', '#9fabd2', 'system,color,bluepurple', 'CLOTH_COLOR'),
('浅灰紫', 'Heirloom Lilac', '#9993b3', 'system,color,bluepurple', 'CLOTH_COLOR'),
('宇宙紫', 'Navy Cosmos', '#503f57', 'system,color,bluepurple', 'CLOTH_COLOR'),
('鬱金香紫', 'Violet Tulip', '#9b90c8', 'system,color,bluepurple', 'CLOTH_COLOR'),
('光谱蓝', 'Spectrum Blue', '#3e4081', 'system,color,bluepurple', 'CLOTH_COLOR'),
('故宫', 'Imperial Palace', '#594577', 'system,color,bluepurple', 'CLOTH_COLOR'),
('珠宝紫', 'Crown Jewel', '#473053', 'system,color,bluepurple', 'CLOTH_COLOR'),
('轻蓝色', 'Whispering Blue', '#cae0e1', 'system,color,bluegreen', 'CLOTH_COLOR'),
('铅色', 'Lead', '#76888f', 'system,color,bluegreen', 'CLOTH_COLOR'),
('午夜蓝', 'Midnight', '#315d78', 'system,color,bluegreen', 'CLOTH_COLOR'),
('港口灰', 'Harbor Gray', '#a8c4bc', 'system,color,bluegreen', 'CLOTH_COLOR'),
('尼罗蓝', 'Nile Blue', '#78a9ad', 'system,color,bluegreen', 'CLOTH_COLOR'),
('河蓝', 'River Blue', '#3bb3d0', 'system,color,bluegreen', 'CLOTH_COLOR'),
('潮水绿', 'Tidepool', '#006b64', 'system,color,bluegreen', 'CLOTH_COLOR'),
('香脂蓝', 'Balsam', '#375a60', 'system,color,bluegreen', 'CLOTH_COLOR'),
('羽蓝色', 'Plume', '#a5d3db', 'system,color,bluegreen', 'CLOTH_COLOR'),
('浅蓝色', 'Blue Tint', '#a1dddd', 'system,color,bluegreen', 'CLOTH_COLOR'),
('浅杏色', 'Almost Apricot', '#e5b49a', 'system,color,orangered', 'CLOTH_COLOR'),
('砂岩', 'Sandstone', '#c78d6b', 'system,color,orangered', 'CLOTH_COLOR'),
('淡桃色', 'Pale Peach', '#fbd1bb', 'system,color,orangered', 'CLOTH_COLOR'),
('摩卡黄', 'Mocha Bisque', '#8a553f', 'system,color,orangered', 'CLOTH_COLOR'),
('橘红', 'Mandarin Red', '#e74d3a', 'system,color,orangered', 'CLOTH_COLOR'),
('铜色', 'Copper', '#ad6b4e', 'system,color,orangered', 'CLOTH_COLOR'),
('铜钱色', 'Copper Coin', '#a45641', 'system,color,orangered', 'CLOTH_COLOR'),
('胡萝卜色', 'Carrot', '#f86a38', 'system,color,orangered', 'CLOTH_COLOR'),
('烧结砖色', 'Fired Brick', '#723838', 'system,color,orangered', 'CLOTH_COLOR'),
('大丽花', 'Red Dahlia', '#812630', 'system,color,purplered', 'CLOTH_COLOR'),
('暗红色', 'Burgundy', '#653b47', 'system,color,purplered', 'CLOTH_COLOR'),
('极光粉', 'Aurora Pink', '#e680aa', 'system,color,purplered', 'CLOTH_COLOR'),
('丁香香包', 'Lilac Sachet', '#eab0ce', 'system,color,purplered', 'CLOTH_COLOR'),
('梅红', 'Jazzy', '#b62756', 'system,color,purplered', 'CLOTH_COLOR'),
('杜鹃花', 'Azalea', '#d3305d', 'system,color,purplered', 'CLOTH_COLOR'),
('波利尼亚克', 'Polignac', '#c0889d', 'system,color,purplered', 'CLOTH_COLOR'),
('热粉红色', 'Hot Pink', '#e95985', 'system,color,purplered', 'CLOTH_COLOR'),
('紫红色', 'Fuchsia Purple', '#cc397b', 'system,color,purplered', 'CLOTH_COLOR'),
('樱桃红', 'Fuchsia Red', '#b33a7f', 'system,color,purplered', 'CLOTH_COLOR'),
('野牛色', 'Bison', '#664d3b', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('饼干色', 'Biscuit', '#b6855a', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('深草色', 'Italian Straw', '#ecd6a3', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('苏丹棕', 'Sudan Brown', '#a96d35', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('金光色', 'Golden Glow', '#de9e36', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('琥珀金', 'Amber Gold', '#bc8e48', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('黄花色', 'Golden Rod', '#e9a916', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('蜂蜜黄', 'Honey Yellow', '#cc9350', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('暮金色', 'Sunset Gold', '#fac668', 'system,color,orangeyellow', 'CLOTH_COLOR'),
('橄榄绿', 'Olive Night', '#575447', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('洛登绿', 'Loden Green', '#6d7356', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('芹菜色', 'Celery', '#d3c54d', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('微光绿', 'Gleam', '#c2d4b2', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('菊苣色', 'Endive', '#d8d37d', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('磨光金色', 'Burnished Gold', '#a89a51', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('淡清澄绿', 'Nile', '#b4bd86', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('蚱蜢色', 'Grasshopper', '#707c43', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('嫩芽绿', 'Tender Shoots', '#b9d146', 'system,color,yellowgreen', 'CLOTH_COLOR'),
('黄草绿', 'Green Oasis', '#acb350', 'system,color,yellowgreen', 'CLOTH_COLOR');


-- 0609
INSERT INTO `user` (`id`, `nick_name`, `real_name`, `login_id`, `pswd`, `mobile`, `role_type`, `user_type`, `master_id`, `status`, `operator_id`, `register_from`, `point`, `memo`, `login_fail_count`, `last_login_time`, `deleted`, `create_time`, `modify_time`, `custom_role`, `corp_org_id`, `corp_name`, `user_review_info`)
VALUES (4, '特殊评测', NULL, '20000000004', NULL, '20000000004', 'SYSTEM', 'MASTER', NULL, 'ENABLED', 1, 'website', NULL, 'No remarks', 0, NULL, 0, '2024-12-22 15:58:20', '2024-12-22 15:58:20', NULL, NULL, NULL, NULL);