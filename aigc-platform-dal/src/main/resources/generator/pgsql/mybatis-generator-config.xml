<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<!--@see https://mybatis.org/generator/quickstart.html-->
<generatorConfiguration>

    <properties resource="generator/pgsql/generator-config.properties"/>

    <!--复杂版本：用于复杂业务表，复杂动态条件sql，有example，带逻辑删除-->
    <context id="MyBatis3" targetRuntime="MyBatis3" defaultModelType="flat">

        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>

        <plugin type="com.itfsw.mybatis.generator.plugins.LogicalDeletePlugin">
            <!-- 这里配置的是全局逻辑删除列和逻辑删除值，当然在table中配置的值会覆盖该全局配置 -->
            <!-- 逻辑删除列类型只能为数字、字符串或者布尔类型 -->
            <property name="logicalDeleteColumn" value="deleted"/>
            <!-- 逻辑删除-已删除值 -->
            <property name="logicalDeleteValue" value="true"/>
            <!-- 逻辑删除-未删除值 -->
            <property name="logicalUnDeleteValue" value="false"/>
            <property name="enableLogicalDeleteConst" value="false"/>
        </plugin>

        <!-- Lombok插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.LombokPlugin">
            <!-- @Data 默认开启,同时插件会对子类自动附加@EqualsAndHashCode(callSuper = true)，@ToString(callSuper = true) -->
            <property name="@Data" value="true"/>
            <!-- @Builder 必须在 Lombok 版本 >= 1.18.2 的情况下开启，对存在继承关系的类自动替换成@SuperBuilder -->
            <property name="@Builder" value="false"/>
            <!-- @NoArgsConstructor 和 @AllArgsConstructor 使用规则和Lombok一致 -->
            <property name="@AllArgsConstructor" value="false"/>
            <property name="@NoArgsConstructor" value="false"/>
            <!-- @Getter、@Setter、@Accessors 等使用规则参见官方文档 -->
            <property name="@Accessors(chain = true)" value="false"/>
            <!-- 临时解决IDEA工具对@SuperBuilder的不支持问题，开启后(默认未开启)插件在遇到@SuperBuilder注解时会调用ModelBuilderPlugin来生成相应的builder代码 -->
            <property name="supportSuperBuilderForIdea" value="false"/>
        </plugin>

        <!-- PgSQL分页插件 -->
        <plugin type="ai.conrain.aigc.platform.dal.generator.plugin.pgsql.PostgreSQLLimitPlugin"></plugin>

        <!-- Example 目标包修改插件 -->
        <plugin type="com.itfsw.mybatis.generator.plugins.ExampleTargetPlugin">
            <!-- 修改Example类生成到目标包下 -->
            <property name="targetPackage" value="ai.conrain.aigc.platform.dal.example"/>
        </plugin>

        <!--修改example类的名称-->
        <plugin type="org.mybatis.generator.plugins.RenameExampleClassPlugin">
            <property name="searchString" value="DOExample$"/>
            <property name="replaceString" value="Example"/>
        </plugin>

        <!--自定义生成vo/convertor/service/controller的插件-->
        <plugin type="ai.conrain.aigc.platform.dal.generator.plugin.pgsql.GenBizJavaFilesPlugin4PgSQL"/>

        <plugin type="ai.conrain.aigc.platform.dal.generator.plugin.ColumnTypeAwarePlugin">
            <property name="jdbc.url" value="${ds.url}"/>
            <property name="jdbc.username" value="${ds.user}"/>
            <property name="jdbc.password" value="${ds.pwd}"/>
        </plugin>

        <!--自定义DO注释-->
        <commentGenerator type="ai.conrain.aigc.platform.dal.generator.plugin.EntityCommentGenerator">
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${ds.driver}"
                        connectionURL="${ds.url}"
                        userId="${ds.user}"
                        password="${ds.pwd}">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <javaModelGenerator targetPackage="${pkg.entity}"
                            targetProject="${proj.entity}">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="${pkg.sqlmap}"
                         targetProject="${proj.sqlmap}">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="${pkg.dao}"
                             targetProject="${proj.dao}">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--复杂版本：用于复杂业务表，复杂动态条件sql，有example，支持逻辑删除字段-->
        <!--需要生成的表名-->
<!--        <table tableName="image" domainObjectName="ImageDO" mapperName="ImageDAO"-->
<!--               enableInsert="true"-->
<!--               enableDeleteByPrimaryKey="true"-->
<!--               enableSelectByPrimaryKey="true"-->
<!--               enableUpdateByPrimaryKey="true"-->
<!--               enableCountByExample="true"-->
<!--               enableDeleteByExample="false"-->
<!--               enableSelectByExample="true"-->
<!--               enableUpdateByExample="true">-->
<!--            &lt;!&ndash; PostgreSQL 使用 SERIAL/BIGSERIAL 或 IDENTITY 列时，需指定 sqlStatement 为 "JDBC" &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>-->
<!--        </table>-->

        <table tableName="search_task" domainObjectName="SearchTaskDO" mapperName="SearchTaskDAO"
               enableInsert="true"
               enableDeleteByPrimaryKey="true"
               enableSelectByPrimaryKey="true"
               enableUpdateByPrimaryKey="true"
               enableCountByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               enableUpdateByExample="true">
            <!-- PostgreSQL 使用 SERIAL/BIGSERIAL 或 IDENTITY 列时，需指定 sqlStatement 为 "JDBC" -->
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

        <table tableName="search_result_img" domainObjectName="SearchResultImgDO" mapperName="SearchResultImgDAO"
               enableInsert="true"
               enableDeleteByPrimaryKey="true"
               enableSelectByPrimaryKey="true"
               enableUpdateByPrimaryKey="true"
               enableCountByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               enableUpdateByExample="true">
            <!-- PostgreSQL 使用 SERIAL/BIGSERIAL 或 IDENTITY 列时，需指定 sqlStatement 为 "JDBC" -->
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>

    </context>

    <!--简洁版本：用于一般配置表，简洁sql，没有example，且不带逻辑删除-->
<!--    <context id="MyBatis3Simple" targetRuntime="MyBatis3Simple" defaultModelType="flat">-->

<!--        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>-->
<!--        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>-->
<!--        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin"/>-->

<!--        <plugin type="com.itfsw.mybatis.generator.plugins.LogicalDeletePlugin">-->
<!--            &lt;!&ndash; 这里配置的是全局逻辑删除列和逻辑删除值，当然在table中配置的值会覆盖该全局配置 &ndash;&gt;-->
<!--            &lt;!&ndash; 逻辑删除列类型只能为数字、字符串或者布尔类型 &ndash;&gt;-->
<!--            <property name="logicalDeleteColumn" value="deleted"/>-->
<!--            &lt;!&ndash; 逻辑删除-已删除值 &ndash;&gt;-->
<!--            <property name="logicalDeleteValue" value="true"/>-->
<!--            &lt;!&ndash; 逻辑删除-未删除值 &ndash;&gt;-->
<!--            <property name="logicalUnDeleteValue" value="false"/>-->
<!--            <property name="enableLogicalDeleteConst" value="false"/>-->
<!--        </plugin>-->

<!--        &lt;!&ndash; Lombok插件 &ndash;&gt;-->
<!--        <plugin type="com.itfsw.mybatis.generator.plugins.LombokPlugin">-->
<!--            &lt;!&ndash; @Data 默认开启,同时插件会对子类自动附加@EqualsAndHashCode(callSuper = true)，@ToString(callSuper = true) &ndash;&gt;-->
<!--            <property name="@Data" value="true"/>-->
<!--            &lt;!&ndash; @Builder 必须在 Lombok 版本 >= 1.18.2 的情况下开启，对存在继承关系的类自动替换成@SuperBuilder &ndash;&gt;-->
<!--            <property name="@Builder" value="false"/>-->
<!--            &lt;!&ndash; @NoArgsConstructor 和 @AllArgsConstructor 使用规则和Lombok一致 &ndash;&gt;-->
<!--            <property name="@AllArgsConstructor" value="false"/>-->
<!--            <property name="@NoArgsConstructor" value="false"/>-->
<!--            &lt;!&ndash; @Getter、@Setter、@Accessors 等使用规则参见官方文档 &ndash;&gt;-->
<!--            <property name="@Accessors(chain = true)" value="false"/>-->
<!--            &lt;!&ndash; 临时解决IDEA工具对@SuperBuilder的不支持问题，开启后(默认未开启)插件在遇到@SuperBuilder注解时会调用ModelBuilderPlugin来生成相应的builder代码 &ndash;&gt;-->
<!--            <property name="supportSuperBuilderForIdea" value="false"/>-->
<!--        </plugin>-->

<!--        &lt;!&ndash; PgSQL分页插件 &ndash;&gt;-->
<!--        <plugin type="ai.conrain.aigc.platform.dal.generator.plugin.pgsql.PostgreSQLLimitPlugin"></plugin>-->

<!--        &lt;!&ndash; Example 目标包修改插件 &ndash;&gt;-->
<!--        <plugin type="com.itfsw.mybatis.generator.plugins.ExampleTargetPlugin">-->
<!--            &lt;!&ndash; 修改Example类生成到目标包下 &ndash;&gt;-->
<!--            <property name="targetPackage" value="ai.conrain.aigc.platform.dal.example"/>-->
<!--        </plugin>-->

<!--        &lt;!&ndash;修改example类的名称&ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.RenameExampleClassPlugin">-->
<!--            <property name="searchString" value="DOExample$"/>-->
<!--            <property name="replaceString" value="Example"/>-->
<!--        </plugin>-->

<!--        &lt;!&ndash;自定义生成vo/convertor/service/controller的插件&ndash;&gt;-->
<!--        <plugin type="ai.conrain.aigc.platform.dal.generator.plugin.pgsql.GenBizJavaFilesPlugin4PgSQL"/>-->

<!--        <plugin type="ai.conrain.aigc.platform.dal.generator.plugin.ColumnTypeAwarePlugin">-->
<!--            <property name="jdbc.url" value="${ds.url}"/>-->
<!--            <property name="jdbc.username" value="${ds.user}"/>-->
<!--            <property name="jdbc.password" value="${ds.pwd}"/>-->
<!--        </plugin>-->
<!--        &lt;!&ndash;自定义DO注释&ndash;&gt;-->
<!--        <commentGenerator type="ai.conrain.aigc.platform.dal.generator.plugin.EntityCommentGenerator">-->
<!--            <property name="suppressDate" value="true"/>-->
<!--            <property name="suppressAllComments" value="true"/>-->
<!--        </commentGenerator>-->

<!--        <jdbcConnection driverClass="${ds.driver}"-->
<!--                        connectionURL="${ds.url}"-->
<!--                        userId="${ds.user}"-->
<!--                        password="${ds.pwd}">-->
<!--            <property name="nullCatalogMeansCurrent" value="true"/>-->
<!--        </jdbcConnection>-->

<!--        <javaModelGenerator targetPackage="${pkg.entity}"-->
<!--                            targetProject="${proj.entity}">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--            <property name="trimStrings" value="true"/>-->
<!--        </javaModelGenerator>-->

<!--        <sqlMapGenerator targetPackage="${pkg.sqlmap}"-->
<!--                         targetProject="${proj.sqlmap}">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </sqlMapGenerator>-->

<!--        <javaClientGenerator type="XMLMAPPER"-->
<!--                             targetPackage="${pkg.dao}"-->
<!--                             targetProject="${proj.dao}">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </javaClientGenerator>-->

<!--        &lt;!&ndash;简洁版本：用于一般配置表，简洁sql，没有example，且不带逻辑删除&ndash;&gt;-->
<!--        <table tableName="image" domainObjectName="ImageDO" mapperName="ImageDAO">-->
<!--            &lt;!&ndash; PostgreSQL 使用 SERIAL/BIGSERIAL 或 IDENTITY 列时，需指定 sqlStatement 为 "JDBC" &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="image_caption" domainObjectName="ImageCaptionDO" mapperName="ImageCaptionDAO">-->
<!--            &lt;!&ndash; PostgreSQL 使用 SERIAL/BIGSERIAL 或 IDENTITY 列时，需指定 sqlStatement 为 "JDBC" &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>-->
<!--        </table>-->

<!--    </context>-->

</generatorConfiguration>