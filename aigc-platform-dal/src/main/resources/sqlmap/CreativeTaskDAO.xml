<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.dao.CreativeTaskDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="batch_id" jdbcType="INTEGER" property="batchId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="model_id" jdbcType="INTEGER" property="modelId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="image_proportion" jdbcType="VARCHAR" property="imageProportion" />
    <result column="batch_cnt" jdbcType="INTEGER" property="batchCnt" />
    <result column="prompt_id" jdbcType="VARCHAR" property="promptId" />
    <result column="result_path" jdbcType="VARCHAR" property="resultPath" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="pre_task_id" jdbcType="INTEGER" property="preTaskId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="tpl_info" jdbcType="LONGVARCHAR" property="tplInfo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    <result column="result_images" jdbcType="LONGVARCHAR" property="resultImages" />
    <result column="ext_info" jdbcType="LONGVARCHAR" property="extInfo" />
  </resultMap>
  <resultMap extends="ResultMapWithBLOBs" id="ResultMapWithFull" type="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    <result column="aigc_request" jdbcType="LONGVARCHAR" property="aigcRequest" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_id, user_id, model_id, type, image_proportion, batch_cnt, prompt_id, result_path,
    status, operator_id, deleted, create_time, modify_time, tpl_info, pre_task_id
  </sql>
  <sql id="Blob_Column_List">
    result_images, ext_info
  </sql>
  <sql id="Full_Column_List">
    aigc_request
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.example.CreativeTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from creative_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from creative_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from creative_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectFullByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithFull">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    ,
    <include refid="Full_Column_List" />
    from creative_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from creative_task
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from creative_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_task (batch_id, user_id, model_id, 
      type, image_proportion, batch_cnt, prompt_id,
      result_path, status, operator_id, 
      deleted, create_time, modify_time, pre_task_id,
      aigc_request, result_images, ext_info, tpl_info
      )
    values (#{batchId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{modelId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR},
      #{imageProportion,jdbcType=VARCHAR}, #{batchCnt,jdbcType=INTEGER}, #{promptId,jdbcType=VARCHAR},
      #{resultPath,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, 
      #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{preTaskId,jdbcType=INTEGER},
      #{aigcRequest,jdbcType=LONGVARCHAR}, #{resultImages,jdbcType=LONGVARCHAR}, #{extInfo,jdbcType=LONGVARCHAR}, #{tplInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="imageProportion != null">
        image_proportion,
      </if>
      <if test="batchCnt != null">
        batch_cnt,
      </if>
      <if test="promptId != null">
        prompt_id,
      </if>
      <if test="resultPath != null">
        result_path,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="preTaskId != null">
        pre_task_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="aigcRequest != null">
        aigc_request,
      </if>
      <if test="resultImages != null">
        result_images,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="tplInfo != null">
        tpl_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="batchId != null">
        #{batchId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="imageProportion != null">
        #{imageProportion,jdbcType=VARCHAR},
      </if>
      <if test="batchCnt != null">
        #{batchCnt,jdbcType=INTEGER},
      </if>
      <if test="promptId != null">
        #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="resultPath != null">
        #{resultPath,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="preTaskId != null">
        #{preTaskId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="aigcRequest != null">
        #{aigcRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="resultImages != null">
        #{resultImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="tplInfo != null">
        #{tplInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.CreativeTaskExample" resultType="java.lang.Long">
    select count(*) from creative_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update creative_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.modelId != null">
        model_id = #{record.modelId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.imageProportion != null">
        image_proportion = #{record.imageProportion,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCnt != null">
        batch_cnt = #{record.batchCnt,jdbcType=INTEGER},
      </if>
      <if test="record.promptId != null">
        prompt_id = #{record.promptId,jdbcType=VARCHAR},
      </if>
      <if test="record.resultPath != null">
        result_path = #{record.resultPath,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.preTaskId != null">
        pre_task_id = #{record.preTaskId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.aigcRequest != null">
        aigc_request = #{record.aigcRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.resultImages != null">
        result_images = #{record.resultImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.tplInfo != null">
        tpl_info = #{record.tplInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update creative_task
    set id = #{record.id,jdbcType=INTEGER},
      batch_id = #{record.batchId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      model_id = #{record.modelId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      image_proportion = #{record.imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{record.batchCnt,jdbcType=INTEGER},
      prompt_id = #{record.promptId,jdbcType=VARCHAR},
      result_path = #{record.resultPath,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      pre_task_id = #{record.preTaskId,jdbcType=INTEGER},
      aigc_request = #{record.aigcRequest,jdbcType=LONGVARCHAR},
      result_images = #{record.resultImages,jdbcType=LONGVARCHAR},
      ext_info = #{record.extInfo,jdbcType=LONGVARCHAR},
      tpl_info = #{record.tplInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update creative_task
    set id = #{record.id,jdbcType=INTEGER},
      batch_id = #{record.batchId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      model_id = #{record.modelId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      image_proportion = #{record.imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{record.batchCnt,jdbcType=INTEGER},
      prompt_id = #{record.promptId,jdbcType=VARCHAR},
      result_path = #{record.resultPath,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      pre_task_id = #{record.preTaskId,jdbcType=INTEGER},
      tpl_info = #{record.tplInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    update creative_task
    <set>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="imageProportion != null">
        image_proportion = #{imageProportion,jdbcType=VARCHAR},
      </if>
      <if test="batchCnt != null">
        batch_cnt = #{batchCnt,jdbcType=INTEGER},
      </if>
      <if test="promptId != null">
        prompt_id = #{promptId,jdbcType=VARCHAR},
      </if>
      <if test="resultPath != null">
        result_path = #{resultPath,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preTaskId != null">
        pre_task_id = #{preTaskId,jdbcType=INTEGER},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="aigcRequest != null">
        aigc_request = #{aigcRequest,jdbcType=LONGVARCHAR},
      </if>
      <if test="resultImages != null">
        result_images = #{resultImages,jdbcType=LONGVARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="tplInfo != null">
        tpl_info = #{tplInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    update creative_task
    set batch_id = #{batchId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      model_id = #{modelId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      image_proportion = #{imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{batchCnt,jdbcType=INTEGER},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      result_path = #{resultPath,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      pre_task_id = #{preTaskId,jdbcType=INTEGER},
      aigc_request = #{aigcRequest,jdbcType=LONGVARCHAR},
      result_images = #{resultImages,jdbcType=LONGVARCHAR},
      ext_info = #{extInfo,jdbcType=LONGVARCHAR},
      tpl_info = #{tplInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    update creative_task
    set batch_id = #{batchId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      model_id = #{modelId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      image_proportion = #{imageProportion,jdbcType=VARCHAR},
      batch_cnt = #{batchCnt,jdbcType=INTEGER},
      prompt_id = #{promptId,jdbcType=VARCHAR},
      result_path = #{resultPath,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      pre_task_id = #{preTaskId,jdbcType=INTEGER},
      tpl_info = #{tplInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update creative_task set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update creative_task set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="batchLogicalDeleteByPrimaryKeys" parameterType="java.util.List">
    update creative_task set deleted = true
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

<select id="selectByElements" resultType="ai.conrain.aigc.platform.dal.entity.CreativeTaskDO">
    select
    id, batch_id, result_images
    from creative_task
    where `status` = 'FINISHED'
    and deleted != 1
    <if test="userId != null">
        and user_id = #{userId,jdbcType=INTEGER}
    </if>
    and ext_info ->> '$.styleSceneId' IN
    <foreach collection="elementIds" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
    </foreach>
    <if test="testFlag != null">
        <choose>
            <when test="testFlag == true">
                and ext_info ->> '$.bizTag' = 'testCase'
            </when>
            <otherwise>
                and (ext_info ->> '$.bizTag' is null or ext_info ->> '$.bizTag' != 'testCase')
            </otherwise>
        </choose>
    </if>
    order by id desc
    limit #{limit,jdbcType=INTEGER}
</select>

  <select id="batchInsert" parameterType="java.util.List">
    insert into creative_task (batch_id, user_id, model_id, type, image_proportion, batch_cnt, prompt_id, result_path, status, operator_id,
      deleted, create_time, modify_time, aigc_request, result_images, ext_info, tpl_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.batchId,jdbcType=INTEGER}, #{item.userId,jdbcType=INTEGER}, #{item.modelId,jdbcType=INTEGER}, #{item.type,jdbcType=VARCHAR},
      #{item.imageProportion,jdbcType=VARCHAR}, #{item.batchCnt,jdbcType=INTEGER}, #{item.promptId,jdbcType=VARCHAR},
      #{item.resultPath,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=INTEGER}, 0, NOW(), NOW(),
      #{item.aigcRequest,jdbcType=LONGVARCHAR}, #{item.resultImages,jdbcType=LONGVARCHAR}, #{item.extInfo,jdbcType=LONGVARCHAR},
      #{item.tplInfo,jdbcType=LONGVARCHAR})
    </foreach>
  </select>

</mapper>