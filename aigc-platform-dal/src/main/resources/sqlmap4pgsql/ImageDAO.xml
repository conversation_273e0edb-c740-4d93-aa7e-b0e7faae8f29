<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="show_img_url" jdbcType="VARCHAR" property="showImgUrl" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="image_hash" jdbcType="VARCHAR" property="imageHash" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, url, show_img_url, image_path, image_hash, metadata, create_time, modify_time, 
    deleted
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from image
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAllTags" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO" resultType="java.lang.String">
      SELECT DISTINCT tag
      FROM image,
      jsonb_array_elements_text(metadata->'tags') AS tag
      WHERE NOT deleted AND type = #{type};
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from image
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO" useGeneratedKeys="true">
    insert into image (type, url, show_img_url, 
      image_path, image_hash, metadata, 
      create_time, modify_time, deleted
      )
    values (#{type,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{showImgUrl,jdbcType=VARCHAR}, 
      #{imagePath,jdbcType=VARCHAR}, #{imageHash,jdbcType=VARCHAR}, #{metadata,jdbcType=OTHER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO" useGeneratedKeys="true">
    insert into image
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="showImgUrl != null">
        show_img_url,
      </if>
      <if test="imagePath != null">
        image_path,
      </if>
      <if test="imageHash != null">
        image_hash,
      </if>
      <if test="metadata != null">
        metadata,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="showImgUrl != null">
        #{showImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null">
        #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="imageHash != null">
        #{imageHash,jdbcType=VARCHAR},
      </if>
      <if test="metadata != null">
        #{metadata,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageExample" resultType="java.lang.Long">
    select count(*) from image
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update image
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.showImgUrl != null">
        show_img_url = #{record.showImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imagePath != null">
        image_path = #{record.imagePath,jdbcType=VARCHAR},
      </if>
      <if test="record.imageHash != null">
        image_hash = #{record.imageHash,jdbcType=VARCHAR},
      </if>
      <if test="record.metadata != null">
        metadata = #{record.metadata,jdbcType=OTHER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update image
    set id = #{record.id,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      show_img_url = #{record.showImgUrl,jdbcType=VARCHAR},
      image_path = #{record.imagePath,jdbcType=VARCHAR},
      image_hash = #{record.imageHash,jdbcType=VARCHAR},
      metadata = #{record.metadata,jdbcType=OTHER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO">
    update image
    <set>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="showImgUrl != null">
        show_img_url = #{showImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null">
        image_path = #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="imageHash != null">
        image_hash = #{imageHash,jdbcType=VARCHAR},
      </if>
      <if test="metadata != null">
        metadata = COALESCE(metadata, '{}') || #{metadata,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO">
    update image
    set type = #{type,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      show_img_url = #{showImgUrl,jdbcType=VARCHAR},
      image_path = #{imagePath,jdbcType=VARCHAR},
      image_hash = #{imageHash,jdbcType=VARCHAR},
      metadata = #{metadata,jdbcType=OTHER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update image set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update image set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="countByIntendedUse" resultType="java.util.Map">
    SELECT
      COALESCE(NULLIF(metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
      COUNT(*) as count
    FROM image
    WHERE type = 'scene'
      AND deleted = false
    GROUP BY COALESCE(NULLIF(metadata->>'intendedUse', ''), 'unassigned')
    ORDER BY count DESC
  </select>
</mapper>