<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageRecordDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="pair_url" jdbcType="VARCHAR" property="pairUrl" />
    <result column="show_img_url" jdbcType="VARCHAR" property="showImgUrl" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="image_hash" jdbcType="VARCHAR" property="imageHash" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="result" property="result" jdbcType="OTHER"/>
    <result column="agg" property="agg" jdbcType="OTHER"/>
    <result column="intendedUse" jdbcType="VARCHAR" property="intendedUse" />
    <result column="clothTypeDesc" jdbcType="VARCHAR" property="clothTypeDesc" />
  </resultMap>

  <sql id="Base_Column_List">
    id, type, url, show_img_url, image_path, image_hash, metadata, create_time, modify_time,
    deleted
  </sql>

  <!-- 公共的图片查询条件 -->
  <sql id="_pageable_conditions">
    <if test="query.id != null">
      AND i.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.type != null and query.type != ''">
      AND i.type = #{query.type,jdbcType=VARCHAR}
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(i.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
  </sql>

  <!-- 公共的游标分页条件 -->
  <sql id="_cursor_conditions">
    <if test="query.cursor != null and query.direction == 'next'">
      AND i.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND i.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
  </sql>

  <!-- CTE子表的图片查询条件 -->
  <sql id="_pageable_conditions_cte">
    <if test="query.id != null">
      AND sub.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.type != null and query.type != ''">
      AND sub.type = #{query.type,jdbcType=VARCHAR}
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(sub.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
  </sql>

  <!-- CTE子表的游标分页条件 -->
  <sql id="_cursor_conditions_cte">
    <if test="query.cursor != null and query.direction == 'next'">
      AND sub.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND sub.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
  </sql>

  <!-- 公共的图片组标题关联 LEFT JOIN，用于筛选 result 字段是否存在 -->
  <sql id="_image_group_caption_join">
    LEFT JOIN image_group_caption igc ON ig.id = igc.image_group_id AND igc.deleted = false
  </sql>

  <!-- 基于CTE子表的聚合筛选条件 -->
  <sql id="_agg_filter_cte">
    <if test="query.agg != null and query.agg.size() > 0">
      AND (
        <foreach collection="query.agg" item="aggItem" separator=" AND ">
          <choose>
            <when test="aggItem.edited == true">
              EXISTS (
                SELECT 1 FROM jsonb_array_elements(sub.agg) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            </when>
            <when test="aggItem.edited == false">
              NOT EXISTS (
                SELECT 1 FROM jsonb_array_elements(sub.agg) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            </when>
            <otherwise>
              1=1
            </otherwise>
          </choose>
        </foreach>
      )
    </if>
  </sql>

  <!-- CTE子表的排序条件 -->
  <sql id="_order_by_direction_cte">
    <choose>
      <when test="query.direction == 'prev'">
          ORDER BY sub.id DESC
      </when>
      <otherwise>
          ORDER BY sub.id ASC
      </otherwise>
    </choose>
  </sql>

  <select id="findPageable" resultMap="BaseResultMap">
    WITH image_agg AS (
      SELECT
        i.id,
        i.type,
        i.url,
        i.show_img_url,
        i.image_path,
        i.image_hash,
        i.metadata,
        COALESCE(NULLIF(i.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        i.create_time,
        i.modify_time,
        i.deleted,
        COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'userId', icu.user_id,
              'editTime', icu.modify_time
            )
          ) FILTER (WHERE icu.user_id IS NOT NULL),
          '[]'::jsonb
        ) as agg
      FROM image i
      LEFT JOIN (
        SELECT DISTINCT ON (image_id, user_id) image_id, user_id, modify_time
        FROM image_caption_user
        WHERE deleted = false
        ORDER BY image_id, user_id, modify_time DESC
      ) icu ON i.id = icu.image_id
      WHERE i.deleted = false
      GROUP BY i.id, i.type, i.url, i.show_img_url, i.image_path, i.image_hash, i.metadata, i.create_time, i.modify_time, i.deleted
    )
    SELECT
      sub.id,
      sub.type,
      sub.url,
      sub.show_img_url,
      sub.image_path,
      sub.image_hash,
      sub.metadata,
      sub.intendedUse,
      sub.clothTypeDesc,
      sub.create_time,
      sub.modify_time,
      sub.deleted,
      NULL as result,
      sub.agg
    FROM image_agg sub
    <where>
      1=1
      <include refid="_pageable_conditions_cte" />
      <include refid="_cursor_conditions_cte" />
      <include refid="_agg_filter_cte" />
    </where>
    <include refid="_order_by_direction_cte" />
    LIMIT #{query.limit} OFFSET #{query.offset}
  </select>

  <select id="countPageable" resultType="long">
    WITH image_agg AS (
      SELECT
        i.id,
        i.type,
        i.url,
        i.show_img_url,
        i.image_path,
        i.image_hash,
        i.metadata,
        COALESCE(NULLIF(i.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        i.create_time,
        i.modify_time,
        i.deleted,
        COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'userId', icu.user_id,
              'editTime', icu.modify_time
            )
          ) FILTER (WHERE icu.user_id IS NOT NULL),
          '[]'::jsonb
        ) as agg
      FROM image i
      LEFT JOIN (
        SELECT DISTINCT ON (image_id, user_id) image_id, user_id, modify_time
        FROM image_caption_user
        WHERE deleted = false
        ORDER BY image_id, user_id, modify_time DESC
      ) icu ON i.id = icu.image_id
      WHERE i.deleted = false
      GROUP BY i.id, i.type, i.url, i.show_img_url, i.image_path, i.image_hash, i.metadata, i.create_time, i.modify_time, i.deleted
    )
    SELECT count(sub.id)
    FROM image_agg sub
    <where>
      1=1
      <include refid="_pageable_conditions_cte" />
      <include refid="_cursor_conditions_cte" />
      <include refid="_agg_filter_cte" />
    </where>
  </select>

  <!-- CTE子表的图片组查询条件 -->
  <sql id="_pageable_conditions_group_cte">
    <if test="query.id != null">
      AND sub.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.paired != null and query.paired == true">
      AND sub.pair_url is not null
    </if>
    <if test="query.paired != null and query.paired == false">
      AND sub.pair_url is null
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(sub.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
    <if test="query.cursor != null and query.direction == 'next'">
      AND sub.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND sub.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
    <!-- 筛选 result 字段中是否包含指定的 result 值 -->
    <if test="query.result != null and query.result != ''">
      AND sub.result::jsonb ?? #{query.result,jdbcType=VARCHAR}
    </if>
  </sql>

  <select id="findImageGroupPageable" resultMap="BaseResultMap">
    WITH image_group_agg AS (
      SELECT
        ig.id,
        'style' as type,
        i1.url,
        i2.url as pair_url,
        ig.metadata,
        COALESCE(NULLIF(i1.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i2.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        ig.create_time,
        ig.modify_time,
        igc.result,
        COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'userId', igcu.user_id,
              'editTime', igcu.modify_time
            )
          ) FILTER (WHERE igcu.user_id IS NOT NULL),
          '[]'::jsonb
        ) as agg
      FROM image_group ig
      LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
      LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
      <include refid="_image_group_caption_join" />
      LEFT JOIN (
        SELECT DISTINCT ON (image_group_id, user_id) image_group_id, user_id, modify_time
        FROM image_group_caption_user
        WHERE deleted = false
        ORDER BY image_group_id, user_id, modify_time DESC
      ) igcu ON ig.id = igcu.image_group_id
      WHERE ig.deleted = false
      GROUP BY ig.id, i1.url, i2.url, ig.metadata, ig.create_time, ig.modify_time, igc.result, i1.metadata, i2.metadata
    )
    SELECT
      sub.id,
      sub.type,
      sub.url,
      sub.pair_url,
      sub.metadata,
      sub.intendedUse,
      sub.clothTypeDesc,
      sub.create_time,
      sub.modify_time,
      sub.result,
      sub.agg
    FROM image_group_agg sub
    <where>
      1=1
      <include refid="_pageable_conditions_group_cte" />
      <include refid="_agg_filter_cte" />
    </where>
    <include refid="_order_by_direction_cte" />
    LIMIT #{query.limit} OFFSET #{query.offset}
  </select>

  <select id="countImageGroupPageable" resultType="long">
    WITH image_group_agg AS (
      SELECT
        ig.id,
        i1.url,
        i2.url as pair_url,
        ig.metadata,
        COALESCE(NULLIF(i1.metadata->>'intendedUse', ''), 'unassigned') as intendedUse,
        COALESCE(i2.metadata->>'clothTypeDesc', '') as clothTypeDesc,
        igc.result,
        COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'userId', igcu.user_id,
              'editTime', igcu.modify_time
            )
          ) FILTER (WHERE igcu.user_id IS NOT NULL),
          '[]'::jsonb
        ) as agg
      FROM image_group ig
      LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
      LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
      <include refid="_image_group_caption_join" />
      LEFT JOIN (
        SELECT DISTINCT ON (image_group_id, user_id) image_group_id, user_id, modify_time
        FROM image_group_caption_user
        WHERE deleted = false
        ORDER BY image_group_id, user_id, modify_time DESC
      ) igcu ON ig.id = igcu.image_group_id
      WHERE ig.deleted = false
      GROUP BY ig.id, i1.url, i2.url, ig.metadata, igc.result, i1.metadata, i2.metadata
    )
    SELECT count(sub.id)
    FROM image_group_agg sub
    <where>
      1=1
      <include refid="_pageable_conditions_group_cte" />
      <include refid="_agg_filter_cte" />
    </where>
  </select>
</mapper>
