package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.SearchResultImgExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SearchResultImgDAO {
    long countByExample(SearchResultImgExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SearchResultImgDO record);

    int insertSelective(SearchResultImgDO record);

    List<SearchResultImgDO> selectByExample(SearchResultImgExample example);

    SearchResultImgDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SearchResultImgDO record, @Param("example") SearchResultImgExample example);

    int updateByExample(@Param("record") SearchResultImgDO record, @Param("example") SearchResultImgExample example);

    int updateByPrimaryKeySelective(SearchResultImgDO record);

    int updateByPrimaryKey(SearchResultImgDO record);
}