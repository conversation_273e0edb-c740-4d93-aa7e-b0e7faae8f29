package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.xiutu.XiutuService;
import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateService;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.EraseBrushModelService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.MerchantPreferenceService;
import ai.conrain.aigc.platform.service.component.PromptWebSocketService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.component.creative.CreativeServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.PreferenceTypeEnum;
import ai.conrain.aigc.platform.service.enums.RemoveBGTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.biz.TryonTaskParams;
import ai.conrain.aigc.platform.service.model.biz.VideoClipGenReq;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.MerchantPreferenceConverter;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeTaskQuery;
import ai.conrain.aigc.platform.service.model.query.MerchantPreferenceQuery;
import ai.conrain.aigc.platform.service.model.query.TryonTaskQuery;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.BrandTryOnRequest;
import ai.conrain.aigc.platform.service.model.request.ClothImgAutoSegRequest;
import ai.conrain.aigc.platform.service.model.request.ClothRecolorRequest;
import ai.conrain.aigc.platform.service.model.request.CreateVideoRequest;
import ai.conrain.aigc.platform.service.model.request.DownloadCreativeImageRequest;
import ai.conrain.aigc.platform.service.model.request.EraseBrushRequest;
import ai.conrain.aigc.platform.service.model.request.EraseBrushV2Request;
import ai.conrain.aigc.platform.service.model.request.FacePinchingCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.FaceSceneSwitchRequest;
import ai.conrain.aigc.platform.service.model.request.FixVideoFaceRequest;
import ai.conrain.aigc.platform.service.model.request.ImageUpscaleRequest;
import ai.conrain.aigc.platform.service.model.request.LogoCombineRequest;
import ai.conrain.aigc.platform.service.model.request.PartialRedrawRequest;
import ai.conrain.aigc.platform.service.model.request.PictureMattingRequest;
import ai.conrain.aigc.platform.service.model.request.PoseSampleDiagramRequest;
import ai.conrain.aigc.platform.service.model.request.RemoveWrinkle4DownCoatRequest;
import ai.conrain.aigc.platform.service.model.request.RemoveWrinkleRequest;
import ai.conrain.aigc.platform.service.model.request.RepairDetailRequest;
import ai.conrain.aigc.platform.service.model.request.RepairHandsRequest;
import ai.conrain.aigc.platform.service.model.request.TryonCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeActiveAndToday;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.MerchantPreferenceVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.CreativeUtils;
import ai.conrain.aigc.platform.service.util.EasyExcelUtils;
import ai.conrain.aigc.platform.service.util.FreemarkerUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.BizConstants.BIZ_TAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_BODY_TYPE_UNLIMITED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CAMERA_ANGLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_COLOR_INDEX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CUSTOM_SCENE_DESC;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ENABLE_ANTI_BLUR_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LIKE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LOGO_IMAGE_OSS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGINAL_BATCH_INFO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CUSTOM_SCENE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_TASK;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_QUEUE_SIZE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REFINE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_SCENE_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TIME_SECS_4_VIDEO;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.ADMIN;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DEMO_ACCOUNT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.MERCHANT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.OPERATOR;

/**
 * Creative控制器
 *
 * <AUTHOR>
 * @version CreativeBatchService.java v 0.1 2024-05-08 03:35:56
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/creative")
public class CreativeController {
    /** 允许输出的扩展字段白名单 */
    public static final List<String> outputKeyWhitelist = Arrays.asList(KEY_QUEUE_SIZE, KEY_SCHEDULE, KEY_ORIGIN_IMAGE,
        KEY_LOGO_IMAGE_OSS, KEY_LIKE, KEY_ORIGIN_TASK, BIZ_TAG, KEY_REFINE_STATUS, KEY_COLOR_INDEX,
        KEY_CLOTH_ORIGIN_IMAGE, KEY_CLOTH_IMAGE, KEY_ORIGIN_CLOTH_COLLOCATION, KEY_CAMERA_ANGLE,
        KEY_ENABLE_ANTI_BLUR_LORA, KEY_ORIGIN_CUSTOM_SCENE, KEY_ORIGINAL_BATCH_INFO, KEY_BODY_TYPE_UNLIMITED,
        KEY_TIME_SECS_4_VIDEO, BizConstants.DOWNLOADED_IMGS);
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private PromptWebSocketService promptWebSocketService;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Value("${comfyui.output.path}")
    private String comfyuiOutputPath;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private OssService ossService;
    @Autowired
    private UserService userService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private DistributorService distributorService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private MerchantPreferenceService merchantPreferenceService;
    @Autowired
    private CommonTaskService commonTaskService;

    @Autowired
    private XiutuService xiutuService;

    @Autowired
    private ComfyuiWorkflowTemplateService comfyuiWorkflowTemplateService;

    @Lazy
    @Autowired
    private CreativeServiceFactory creativeService;
    @Autowired
    private EraseBrushModelService eraseBrushModelService;

    @GetMapping("/getById/{id}")
    public Result<CreativeBatchVO> getCreativeBatchById(@NotNull @PathVariable("id") Integer id) {
        CreativeBatchVO data = creativeBatchService.selectById(id);

        // 管理员权限，判断是否可展示后台视频任务的换图按钮，白名单内的管理员用户可以对【非真实商家以外的视频任务】进行换图操作
        if (data.getType().isVideoCreative() && OperationContextHolder.isAdmin()) {
            JSONArray cfg = systemConfigService.queryJsonArrValue(SystemConstants.SHOW_CHANGE_IMG_BTN_WHITELIST);
            if (CollectionUtils.isNotEmpty(cfg) && cfg.contains(OperationContextHolder.getOperatorUserId())) {

                UserVO userVO = userService.selectById(data.getUserId());

                boolean canShowChangeImg4VideoBatch = false;
                if (userVO != null && userVO.getRoleType() != null) {
                    if (userVO.getRoleType() != RoleTypeEnum.MERCHANT) {
                        canShowChangeImg4VideoBatch = true;
                    } else if (userVO.getMemo() != null && userVO.getMemo().contains("虚拟商家")) {
                        canShowChangeImg4VideoBatch = true;
                    }
                }

                data.setCanShowChangeImg4VideoBatch(canShowChangeImg4VideoBatch);
            }
        }

        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @GetMapping("/getById/task/{id}")
    public Result<CreativeBatchVO> getCreativeBatchByIdWithTask(@NotNull @PathVariable("id") Integer id) {
        CreativeBatchVO data = creativeBatchService.getCreativeBatchByIdWithTask(id);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/deleteById")
    public Result<CreativeBatchVO> deleteById(@JsonArg @NotNull Integer id) {
        creativeBatchService.deleteById(id);
        return Result.success();
    }

    @GetMapping("/getAndSync/{id}")
    public Result<CreativeBatchVO> getAndSync(@NotNull @PathVariable("id") Integer id) {
        CreativeBatchVO data = creativeBatchService.selectById(id);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @PostMapping("/batchQueryAndSync")
    public Result<List<CreativeBatchVO>> batchQueryAndSync(@NotNull @JsonArg List ids, @JsonArg Boolean isSelectTask) {
        // 设置默认值
        boolean isSelect = isSelectTask != null ? isSelectTask : false;

        List<CreativeBatchVO> list = creativeBatchService.batchQueryByIds((List<Integer>)ids, isSelect);

        if (CollectionUtils.isEmpty(list)) {
            return Result.success(list);
        }

        return Result.success(SecurityUtils.filterExtInfo(list, outputKeyWhitelist));
    }

    @PostMapping("/create")
    public Result<CreativeBatchVO> create(@Valid @RequestBody AddCreativeRequest request) throws IOException {
        AssertUtil.assertTrue(StringUtils.isBlank(request.getBizTag()), ResultCode.PARAM_INVALID, "bizTag不能为空");
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.CREATE_IMAGE, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/fixedPosture/create")
    public Result<CreativeBatchVO> fixedPostureCreate(@Valid @RequestBody AddCreativeRequest request)
        throws IOException {
        AssertUtil.assertTrue(StringUtils.isBlank(request.getBizTag()), ResultCode.PARAM_INVALID, "bizTag不能为空");
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.FIXED_POSTURE_CREATION, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/cancel")
    public Result<Boolean> cancel(@NotNull @JsonArg Integer id) {
        creativeBatchService.cancel(id);
        return Result.success();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @PostMapping("/clear")
    public Result<Boolean> clear(@JsonArg @Size(min = 1) List types, @JsonArg String loraType) {
        ModelTypeEnum modelType = ModelTypeEnum.getByCode(loraType);
        creativeBatchService.clear(types, modelType);
        return Result.success();
    }

    /**
     * 查询进行中及今日的创作批次
     *
     * @param types        类型列表
     * @param loraType     类型
     * @param isSelectTask 是否查询任务列表，默认不查询
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @PostMapping("/queryActiveAndTodayList")
    public Result<CreativeActiveAndToday> queryActiveAndTodayList(@JsonArg @Size(min = 1) List types,
                                                                  @JsonArg String loraType,
                                                                  @JsonArg Boolean isSelectTask,
                                                                  @JsonArg String bizType) {
        // 设置默认值
        boolean isSelect = isSelectTask != null ? isSelectTask : false;

        CreativeActiveAndToday result = new CreativeActiveAndToday();

        ModelTypeEnum modelType = ModelTypeEnum.getByCode(loraType);

        CreativeBizTypeEnum bizTypeEnum = ObjectUtils.defaultIfNull(CreativeBizTypeEnum.getByCode(bizType),
            CreativeBizTypeEnum.ALL);

        List<CreativeBatchVO> todayList = creativeBatchService.queryTodayList(types, modelType, isSelect, bizTypeEnum);
        result.setTodayList(SecurityUtils.filterExtInfo(todayList, outputKeyWhitelist));

        return Result.success(result);
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody CreativeBatchVO creativeBatch) {
        creativeBatchService.updateByIdSelective(creativeBatch);
        return Result.success();
    }

    @PostMapping("/setToFail")
    public Result<?> setToFail(@JsonArg @NotNull Integer id) {
        creativeBatchService.setToFail(id);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<CreativeBatchVO>> queryCreativeBatchList(@Valid @RequestBody CreativeBatchQuery query) {
        query.setUserId(OperationContextHolder.getMasterUserId());

        // 创作只能看自己的
        query.setOperatorId(OperationContextHolder.getOperatorUserId());

        List<CreativeBatchVO> list = creativeBatchService.queryCreativeBatchList(query);
        return Result.success(SecurityUtils.filterExtInfo(list, outputKeyWhitelist));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<CreativeBatchVO>> getCreativeBatchByPage(@Valid @RequestBody CreativeBatchQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        // 对于渠道商，创作记录-我的创作
        if (OperationContextHolder.getRoleType() == DISTRIBUTOR) {
            // 渠道商-创作记录-我的创作-选择了出图（渠道商的）员工，管理员查看TA的创作记录时，query会指定operatorId，这里避免覆盖
            if (query.getOperatorId() == null) {
                if (OperationContextHolder.isMasterUser()) {
                    if (!systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
                        OperationContextHolder.getOperatorUserId())) {
                        List<UserVO> allStaffs = distributorService.queryAllStaffListByCurrentUser();
                        if (CollectionUtils.isNotEmpty(allStaffs)) {
                            query.setOperatorIds(allStaffs.stream().map(UserVO::getId).collect(Collectors.toList()));
                        } else {
                            query.setOperatorId(OperationContextHolder.getOperatorUserId());
                        }
                    }
                } else {
                    query.setOperatorId(OperationContextHolder.getOperatorUserId());
                }

                // 渠道商-创作记录-我的创作-选择了出图（渠道商的）员工，管理员查看TA的创作记录时，query会指定operatorId
            } else {
                query.setUserId(OperationContextHolder.getMasterUserId());
            }

            // 演示账号,只展示带演示标的数据
        } else if (OperationContextHolder.getRoleType() == RoleTypeEnum.DEMO_ACCOUNT) {
            query.setOnlyShowDemo(true);
            // 其它角色
        } else {
            if (OperationContextHolder.getRoleType() != ADMIN
                && OperationContextHolder.getRoleType() != RoleTypeEnum.REVIEWER && !systemConfigService.isInJsonArray(
                SEE_ALL_MODELS_AND_HISTORY, OperationContextHolder.getOperatorUserId())) {
                query.setUserId(OperationContextHolder.getMasterUserId());
            }
        }

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        if (query.getQueryRoleType() != null && query.getModelId() != null) {
            MaterialModelVO modelVO = materialModelService.selectById(query.getModelId());
            Integer ownerId = modelVO.getUserId();
            UserVO owner = userService.selectById(ownerId);
            if (!owner.getRoleType().isBackRole()) {
                query.setUserIdsNotIn(Collections.singletonList(ownerId));
            }
        }

        if (query.getRelatedOperator() != null) {
            // 只有手动类型才有关联处理人
            query.setTypeList(CreativeTypeEnum.getManualTypes());
        }

        if (!OperationContextHolder.isBackRole() && !OperationContextHolder.isDistributorRole()
            && !systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
            OperationContextHolder.getOperatorUserId())) {
            query.setEnabledUserModel(OperationContextHolder.getMasterUserId());
        }

        if (!OperationContextHolder.isBackRole()) {
            if (CollectionUtils.isEmpty(query.getTypeNotIn())) {
                query.setTypeNotIn(new ArrayList<>());
            }
            query.getTypeNotIn().add(CreativeTypeEnum.FIX_VIDEO_FACE.getCode());
        }

        if (query.isOnlyShowVIP()) {
            List<UserVO> vipOrPaidCustomerUserList = userService.queryAllVIPOrPaidCustomer();
            if (CollectionUtils.isNotEmpty(vipOrPaidCustomerUserList)) {
                query.setUserIds(vipOrPaidCustomerUserList.stream().map(UserVO::getId).collect(Collectors.toList()));
            }
        }

        // 添加不查询的创作类型
        if (query.getTypeNotIn() == null) {
            query.setTypeNotIn(new ArrayList<>());
        }
        // 添加 图片抠图 类型
        query.getTypeNotIn().add(CreativeTypeEnum.PICTURE_MATTING.getCode());
        query.getTypeNotIn().add(CreativeTypeEnum.CLOTH_AUTO_SEGMENT.getCode());

        if (query.isOnlyShowProcessing()) {
            query.setStatus(CreativeStatusEnum.PROCESSING.getCode());
            query.getTypeNotIn().add(CreativeTypeEnum.CREATE_VIDEO.getCode());
            query.setOrderBy(
                "if(JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.startTime')) is null or status = 'FAILED',NOW(),"
                + "JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.startTime'))) ASC,id desc");
        }

        // 不展示 batchCnt 为 0 的创作批次
        query.setBatchCntGreaterThan(0);

        PageInfo<CreativeBatchVO> pageInfo = creativeBatchService.queryCreativeBatchByPage(query);
        pageInfo.setList(SecurityUtils.filterExtInfo(pageInfo.getList(), outputKeyWhitelist));
        return Result.success(pageInfo);
    }

    @PostMapping("/fetchPrompt")
    public Result<String> fetchPrompt(@NotBlank @JsonArg String imageName) {
        Integer taskId = ComfyUIUtils.parseTaskIdByImageName(imageName);

        if (null == taskId) {
            log.warn("imageName:{} is not a valid image name", imageName);
            return Result.error("当前图片为上传图片，无prompt");
        }

        CreativeTaskVO data = creativeTaskService.selectById(taskId);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作任务不存在");

        // 兼容老数据，没有serverId
        Integer serverId = data.getExtValue(KEY_SERVER_ID, Integer.class);
        IExtModel model = serverId == null ? creativeBatchService.selectById(data.getBatchId()) : data;

        String filePrefix = ComfyUIUtils.buildFileNamePrefix(taskId);
        String prompt = null;

        // 尝试不同前缀
        String[] prefixes = new String[] {filePrefix, "face_" + filePrefix};
        for (String prefix : prefixes) {
            prompt = comfyUIService.fetchFileContent(comfyuiOutputPath + data.getResultPath(), prefix, "txt",
                serverHelper.getFileServerUrlByTask(model));

            if (StringUtils.isNotBlank(prompt)) {
                break;
            }

            if (prefix.equals(filePrefix)) {
                log.info("使用普通前缀{}未找到prompt，尝试备选前缀", filePrefix);
            }
        }

        return Result.success(prompt);
    }

    // 当前用户创作过的模型
    @PostMapping("/queryModels4HistoryTasks")
    public Result<List<LoraOption>> queryModels4HistoryTasks() {
        return Result.success(creativeBatchService.queryModels4HistoryTasks());
    }

    @PostMapping("/imageLike")
    public Result<?> imageLike(@NotNull @JsonArg Integer taskId, @JsonArg Integer batchId,
                               @NotNull @JsonArg Boolean like, @JsonArg String imageUrl) {
        creativeBatchService.imageLike(taskId, batchId, like, imageUrl);
        return Result.success();
    }

    @PostMapping("/monitorImgDownload")
    public Result<?> monitorImgDownload(@NotNull @JsonArg Integer batchId, @NotBlank @JsonArg String imgUrl) {
        try {
            if (!RoleTypeEnum.MERCHANT.equals(OperationContextHolder.getRoleType())) {
                return Result.success();
            }

            CreativeBatchVO batch = creativeBatchService.selectById(batchId);
            if (batch != null) {
                JSONObject ext = batch.getExtInfo();
                if (ext == null) {
                    ext = new JSONObject();
                }
                JSONArray imgs = ext.getJSONArray(BizConstants.DOWNLOADED_IMGS);
                if (imgs == null) {
                    imgs = new JSONArray();
                }
                if (!imgs.contains(imgUrl)) {
                    imgs.add(imgUrl);
                }
                ext.put(BizConstants.DOWNLOADED_IMGS, imgs);
                batch.setExtInfo(ext);

                creativeBatchService.updateByIdSelective(batch);
            }
        } catch (Exception e) {
            log.error("monitorImgDownload error,ignore", e);
        }
        return Result.success();
    }

    @PostMapping("/fetchWorkflow")
    public Result<String> fetchWorkflow(@NotNull @JsonArg Integer taskId) throws JsonProcessingException {
        CreativeTaskVO task = creativeTaskService.selectFullById(taskId);
        JSONObject json = fetchWorkflowByTask(task);

        JSONObject workflow = json.getJSONObject("extra_data").getJSONObject("extra_pnginfo").getJSONObject("workflow");

        ObjectMapper localObjectMapper = objectMapper.copy();
        localObjectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);

        return Result.success(localObjectMapper.writeValueAsString(workflow));
    }

    @PostMapping("/exportBatchPrompt")
    public void exportBatchPrompt(@NotNull @JsonArg String ids, HttpServletResponse response) throws IOException {
        String[] split = ids.split(",");
        List<CreativeTaskVO> tasks = new ArrayList<>();
        for (String s : split) {
            CreativeBatchVO batch = creativeBatchService.selectById(Integer.parseInt(s));
            if (batch != null) {
                CreativeTaskQuery query = new CreativeTaskQuery();
                query.setBatchId(batch.getId());
                query.setPageSize(1);
                query.setPageNum(1);
                List<CreativeTaskVO> taskList = creativeTaskService.queryCreativeTaskList(query);
                tasks.add(taskList.get(0));
            }
        }

        // 准备数据
        List<List<String>> data = new ArrayList<>();
        data.add(Arrays.asList("id", "image_path", "prompt", "width", "height", "image_url", "batch_id"));

        for (CreativeTaskVO task : tasks) {
            String[] proportions = CreativeUtils.getImageSizeFromProportion(task.getImageProportion());
            if (proportions == null) {
                continue;
            }

            JSONObject json = fetchWorkflowByTask(task);
            String prompt = json.getJSONObject("prompt").getJSONObject("282").getJSONObject("inputs").getString(
                "prompts");

            data.add(Arrays.asList(task.getId() + "", "", prompt, proportions[0], proportions[1],
                task.getResultImages().get(0), task.getBatchId() + ""));
        }

        EasyExcelUtils.export(data, response);
    }

    @PostMapping("/fetchTaskInfo")
    public Result<CreativeTaskVO> fetchTaskInfo(@NotNull @JsonArg Integer taskId) {
        CreativeTaskVO data = creativeTaskService.selectById(taskId);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作任务不存在");

        CreativeTaskVO target = new CreativeTaskVO();
        target.setId(taskId);
        target.setImageProportion(data.getImageProportion());
        target.setResultImages(data.getResultImages());

        return Result.success(target);
    }

    @PostMapping("/fetchBatchByTask")
    public Result<?> fetchBatchByTask(@NotNull @JsonArg Integer taskId) {
        CreativeTaskVO data = creativeTaskService.selectById(taskId);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作任务不存在");

        CreativeBatchVO batchVO = creativeBatchService.selectById(data.getBatchId());

        return Result.success(SecurityUtils.filterExtInfo(batchVO, outputKeyWhitelist));
    }

    @GetMapping("/fetchTaskImage/{id}")
    public ResponseEntity<Resource> fetchTaskImage(@NotNull @PathVariable("id") Integer id) {
        CreativeTaskVO data = creativeTaskService.selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作任务不存在");
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(data.getResultImages()), "创作任务不存在图片");
        try {
            String imageUrl = CommonUtil.getFilePathAndNameFromURL(data.getResultImages().get(0));
            String imageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
            String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", imageName);

            Path path = Paths.get(tmpUrl); // 假设 imageUrl 是服务器上的一个本地路径
            Resource resource = new UrlResource(path.toUri());

            return ResponseEntity.ok().header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*").header(
                HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"").header(
                HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_PNG_VALUE).body(resource);
        } catch (MalformedURLException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/repairHands")
    public Result<?> repairHands(@Valid @ModelAttribute RepairHandsRequest request) throws IOException {
        creativeBatchService.create(CreativeTypeEnum.REPAIR_HANDS, request);
        return Result.success();
    }

    @PostMapping("/updateOriginalImg4Video")
    public Result<?> updateOriginalImg4Video(@JsonArg @NotNull Integer batchId, @JsonArg @NotNull Integer index,
                                             @JsonArg @NotBlank String imageUrl) {
        creativeBatchService.updateOriginalImg4Video(batchId, index, imageUrl);
        return Result.success();
    }

    @PostMapping("/logoCombine")
    public Result<CreativeBatchVO> logoCombine(@NotNull @RequestParam("image") MultipartFile image,
                                               @NotNull @RequestParam("payload") String payload) throws IOException {
        LogoCombineRequest request = objectMapper.readValue(payload, LogoCombineRequest.class);
        request.setImage(image);

        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.LOGO_COMBINE, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/imageUpscale")
    public Result<CreativeBatchVO> imageUpscale(@Valid @RequestBody ImageUpscaleRequest request) throws IOException {
        AssertUtil.assertTrue(request.getTaskId() != null || StringUtils.isNotBlank(request.getUpscaleImage()),
            ResultCode.PARAM_INVALID, "taskId或者imageUrl必须有一个不为空");
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.IMAGE_UPSCALE, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/createVideo")
    public Result<CreativeBatchVO> createVideo(@Valid @RequestBody CreateVideoRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.CREATE_VIDEO, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/fixVideoFace")
    public Result<CreativeBatchVO> fixVideoFace(@Valid @RequestBody FixVideoFaceRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.FIX_VIDEO_FACE, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/removeBg")
    public ResponseEntity<Resource> removeBg(@NotNull @RequestParam("image") MultipartFile image,
                                             @RequestParam("type") String type) throws IOException {
        if (type == null) {
            type = "1";
        }
        RemoveBGTypeEnum typeEnum = RemoveBGTypeEnum.getByCode(Integer.valueOf(type));
        AssertUtil.assertNotNull(typeEnum, ResultCode.PARAM_INVALID, "去除背景类型错误");

        String fileName = image.getOriginalFilename();
        log.info("接收到去除背景请求，fileName={}，size={}k，type={}", fileName, image.getSize() / 1024,
            typeEnum.getModelType());
        String serverUrl = serverHelper.getModelServerUrlByUser(OperationContextHolder.getMasterUserId());
        AssertUtil.assertNotNull(serverUrl, ResultCode.SYS_ERROR, "无去除背景服务");
        byte[] bytes = comfyUIService.removeBg(image.getBytes(), typeEnum.getModelType(), serverUrl);

        Resource resource = new ByteArrayResource(bytes);

        return ResponseEntity.ok().header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*").header(
            HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"").contentType(
            MediaType.IMAGE_PNG).contentLength(bytes.length).body(resource);
    }

    @PostMapping("/queryExampleImages")
    public Result<List<String>> queryExampleImages(@NotNull @JsonArg Integer modelId) {
        List<String> images = creativeBatchService.queryExampleImages(modelId);
        return Result.success(images);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @PostMapping("/uploadVideo")
    public Result<Boolean> uploadVideo(@NotNull @JsonArg Integer id, @Size(min = 1) @JsonArg List videos) {
        creativeBatchService.uploadVideo(id, videos);
        return Result.success();
    }

    @PostMapping("/removeFixFace")
    public Result<Boolean> removeFixFace(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer index) {
        creativeBatchService.removeFixFace(id, index);
        return Result.success();
    }

    @PostMapping("/changeTempVideo")
    public Result<Boolean> changeTempVideo(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer index,
                                           @JsonArg String videoUrl) {
        creativeBatchService.changeTempVideo(id, index, videoUrl);
        return Result.success();
    }

    @PostMapping("/apply2GenVideoClip")
    public Result<Boolean> apply2GenVideoClip(@RequestBody @Valid VideoClipGenReq item) {
        creativeBatchService.apply2GenVideoClip(item);
        return Result.success();
    }

    @PostMapping("/assignVideoOperator")
    public Result<Boolean> assignVideoOperator(@NotNull @JsonArg Integer id, @NotBlank @JsonArg String mobile) {
        creativeBatchService.assignVideoOperator(id, mobile);
        return Result.success();
    }

    @GetMapping("/queryNeedProcessCnt")
    public Result<Long> queryNeedProcessCnt() {
        CreativeBatchQuery query = new CreativeBatchQuery();
        query.setTypeList(Collections.singletonList(CreativeTypeEnum.CREATE_VIDEO.getCode()));
        query.setStatusList(CreativeStatusEnum.getUncompleteStatusList());
        query.setCustomerType("customer");
        return Result.success(creativeBatchService.queryCount(query));
    }

    @PostMapping("/applyRefine")
    public Result<Boolean> applyRefine(@NotNull @JsonArg Integer id) {
        creativeBatchService.applyRefine(id);
        return Result.success();
    }

    @PostMapping("/completeRefine")
    public Result<Boolean> completeRefine(@NotNull @JsonArg Integer id) {
        creativeBatchService.completeRefine(id);
        return Result.success();
    }

    @PostMapping("/resetProcessing")
    public Result<Boolean> resetProcessing(@NotNull @JsonArg Integer id) {
        creativeBatchService.resetProcessing(id);
        return Result.success();
    }

    @PostMapping("/downloadAll")
    public Result<String> downloadAll(@RequestBody @Valid DownloadCreativeImageRequest request) throws IOException {
        return Result.success(
            creativeBatchService.downloadAll(request.getId(), ListUtils.emptyIfNull(request.getImageUrls())));
    }

    @PostMapping("/assignTo")
    public Result<?> assignTo(@NotNull @JsonArg Integer batchId, @NotNull @JsonArg Integer userId) {
        creativeBatchService.assignTo(batchId, userId);
        return Result.success();
    }

    @PostMapping("/queryImagesByElement")
    public Result<?> queryImagesByElement(@NotNull @JsonArg Integer elementId, @NotNull @JsonArg Integer limit,
                                          @JsonArg Boolean testFlag, @JsonArg Integer userId) {
        return Result.success(creativeBatchService.queryImagesByElement(elementId, userId, testFlag, limit));
    }

    @PostMapping("/queryImagesByElementWithPage")
    public Result<?> queryImagesByElementWithPage(@Valid @RequestBody CreativeBatchQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        AssertUtil.assertTrue(query.getElementId() != null || query.getSceneId() != null, ResultCode.PARAM_INVALID,
            "元素id和场景id不能同时为空");

        query.setStatus(CreativeStatusEnum.FINISHED.getCode());

        return Result.success(creativeBatchService.queryImagesByElementWithPage(query));
    }

    @PostMapping("/queryImagesByElementWithPageFormTask")
    public Result<?> queryImagesByElementWithPageFormTask(@Valid @RequestBody CreativeTaskQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        AssertUtil.assertNotNull(query.getElementId(), ResultCode.PARAM_INVALID, "元素id不能为空");

        return Result.success(creativeTaskService.queryImagesByElementWithPage(query));
    }

    @PostMapping("/fetchStyleImagesMap")
    public Result<Map<Integer, String>> fetchStyleImagesMap(@JsonArg @NotNull Integer id) {
        List<CreativeTaskVO> tasks = creativeTaskService.queryTaskByBatchId(id);
        if (CollectionUtils.isEmpty(tasks)) {
            return Result.success();
        }

        Map<Integer, String> result = new HashMap<>();
        List<Integer> ids = tasks.stream().filter(e -> e.getExtValue(KEY_STYLE_SCENE_ID, Integer.class) != null).map(
            e -> e.getExtValue(KEY_STYLE_SCENE_ID, Integer.class)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return Result.success(result);
        }

        List<CreativeElementVO> elements = creativeElementService.batchQueryIncludesDelByIds(ids);

        if (CollectionUtils.isEmpty(elements)) {
            return Result.success(result);
        }

        for (CreativeTaskVO e : tasks) {

            Integer sceneId = e.getExtValue(KEY_STYLE_SCENE_ID, Integer.class);

            if (sceneId == null) {
                continue;
            }
            CreativeElementVO element = elements.stream().filter(e1 -> e1.getId().equals(sceneId)).findFirst().orElse(
                null);
            if (element != null) {
                String image = element.getExtInfo(KEY_STYLE_IMAGE, String.class);
                result.put(e.getId(), image);
            }
        }

        return Result.success(result);
    }

    @PostMapping("/createTryonTask")
    public Result<Integer> createTryon(@Valid @RequestBody TryonTaskParams params) throws IOException {
        if (StringUtils.equals(params.getTryonType(), CommonConstants.TRYON_TYPE_ALIYUN)) {
            return Result.success(creativeBatchService.createAliyunTryonTask(params));

            // comfyui
        } else if (StringUtils.equals(params.getTryonType(), CommonConstants.TRYON_TYPE_COMFYUI)) {
            TryonCreativeRequest req = new TryonCreativeRequest();
            req.setTopUrl(params.getTopUrl());
            req.setBottomUrl(params.getBottomUrl());
            req.setPersonImgUrl(params.getPersonImgUrl());

            CreativeBatchVO batch = creativeBatchService.create(CreativeTypeEnum.TRYON, req);
            return Result.success(batch.getId());
        } else {
            return Result.error("不支持的试衣类型:" + params.getTryonType());
        }
    }

    @PostMapping("/createTryonRefinerTask")
    public Result<Integer> createTryonRefinerTask(@JsonArg @NotNull Integer tryonTaskId,
                                                  @JsonArg @NotBlank String gender) {
        return Result.success(creativeBatchService.createTryonRefinerTask(tryonTaskId, gender));
    }

    @PostMapping("/queryTryonTask")
    public Result<Object> queryTryonTask(@NotNull @JsonArg Integer id, @JsonArg String tryonType) {
        if (StringUtils.equals(tryonType, CommonConstants.TRYON_TYPE_ALIYUN)) {
            return Result.success(creativeBatchService.queryAliyunTryonTask(id));
            // comfyui
        } else if (StringUtils.equals(tryonType, CommonConstants.TRYON_TYPE_COMFYUI)) {
            return Result.success(creativeBatchService.getAndSync(id));
        } else {
            return Result.error("不支持的试衣类型:" + tryonType);
        }
    }

    @PostMapping("/queryTryonTasksByPage")
    public Result<PageInfo<?>> queryTryonTasksByPage(@Valid @RequestBody TryonTaskQuery query) {

        if (StringUtils.equals(query.getTryonType(), CommonConstants.TRYON_TYPE_ALIYUN)) {
            CommonTaskQuery commonTaskQuery = new CommonTaskQuery();
            commonTaskQuery.setPageNum(query.getPageNum());
            commonTaskQuery.setPageSize(query.getPageSize());
            commonTaskQuery.setOrderBy(query.getOrderBy());
            return Result.success(creativeBatchService.queryAliyunTryonTasksByPage(commonTaskQuery));

        } else if (StringUtils.equals(query.getTryonType(), CommonConstants.TRYON_TYPE_COMFYUI)) {
            CreativeBatchQuery commonTaskQuery = new CreativeBatchQuery();
            commonTaskQuery.setPageNum(query.getPageNum());
            commonTaskQuery.setPageSize(query.getPageSize());
            commonTaskQuery.setOrderBy(query.getOrderBy());
            commonTaskQuery.setType(CreativeTypeEnum.TRYON.getCode());

            return Result.success(creativeBatchService.queryCreativeBatchByPage(commonTaskQuery));
        } else {
            return Result.error("不支持的试衣类型:" + query.getTryonType());
        }
    }

    @PostMapping("/faceSceneSwitch")
    public Result<CreativeBatchVO> swapFace(@Valid @RequestBody FaceSceneSwitchRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.FACE_SCENE_SWITCH, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/partialRedraw")
    public Result<?> partialRedraw(@Valid @ModelAttribute PartialRedrawRequest request) throws IOException {
        creativeBatchService.create(CreativeTypeEnum.PARTIAL_REDRAW, request);
        return Result.success();
    }

    @PostMapping("/pictureMatting")
    public Result<?> pictureMatting(@Valid @RequestBody PictureMattingRequest request) throws IOException {
        creativeBatchService.create(CreativeTypeEnum.PICTURE_MATTING, request);
        return Result.success();
    }

    @PostMapping("/basicChangingClothes")
    public Result<?> basicChangingClothes(@Valid @RequestBody AddCreativeRequest request) throws IOException {
        creativeBatchService.create(CreativeTypeEnum.BASIC_CHANGING_CLOTHES, request);
        return Result.success();
    }

    @PostMapping("/clothImgAutoSeg")
    @Roles({ADMIN, OPERATOR, DEMO_ACCOUNT, MERCHANT, DISTRIBUTOR})
    public Result<CreativeBatchVO> clothImgAutoSeg(@Valid @RequestBody ClothImgAutoSegRequest request)
        throws IOException {
        CreativeBatchVO batchVO = creativeBatchService.create(CreativeTypeEnum.CLOTH_AUTO_SEGMENT, request);
        return Result.success(SecurityUtils.filterExtInfo(batchVO, outputKeyWhitelist));
    }

    @PostMapping("/clothRecolor")
    @Roles({ADMIN, OPERATOR, DEMO_ACCOUNT, MERCHANT, DISTRIBUTOR})
    public Result<CreativeBatchVO> clothRecolor(@Valid @RequestBody ClothRecolorRequest request) throws IOException {
        CreativeBatchVO batchVO = creativeBatchService.create(CreativeTypeEnum.CLOTH_RECOLOR, request);
        return Result.success(SecurityUtils.filterExtInfo(batchVO, outputKeyWhitelist));
    }

    @PostMapping("/poseSampleDiagram")
    public Result<?> poseSampleDiagram(@Valid @RequestBody PoseSampleDiagramRequest request) throws IOException {

        // 特殊处理（需要获取其所有姿势）
        CreativeElementVO creativeElementVO = creativeElementService.selectByIdWithChildren(request.getSceneId());

        // 检查姿势场景是否存在
        if (creativeElementVO.getChildren() == null || creativeElementVO.getChildren().isEmpty()) {
            log.error("姿势场景不存在，终止创建姿势示例图，sceneId={}", request.getSceneId());
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "姿势场景不存在");
        }

        // 循环便利所有姿势
        creativeElementVO.getChildren().forEach(creativeElement -> {
            // 创建临时请求对象
            AddCreativeRequest addCreativeRequest = new AddCreativeRequest();
            PoseSampleDiagramRequest tempRequest = new PoseSampleDiagramRequest();
            BeanUtils.copyProperties(request, tempRequest, PoseSampleDiagramRequest.class);

            // 从faceIdList中随机取出一个 faceId
            List<Integer> faceIdList = request.getFaceIdList();
            if (!CollectionUtils.isEmpty(faceIdList)) {
                // 生成随机数
                int randomIndex = (int)(Math.random() * faceIdList.size());
                // 随机取出
                tempRequest.setConfigFaceId(faceIdList.get(randomIndex));
            }
            // 设置场景 id
            tempRequest.setConfigSceneId(request.getSceneId());
            // 设置姿势 id
            tempRequest.setCurrentPoseId(creativeElement.getId());
            // 设置分辨率
            tempRequest.setImgNum(request.getImgNum());
            // 设置图片数量
            tempRequest.setProportion(request.getProportion());

            // 初始化 姿势示例图信息
            AddCreativeRequest.ReferenceInfo referenceInfo = new AddCreativeRequest.ReferenceInfo();

            // 设置展示图
            referenceInfo.setImageUrl(creativeElement.getExtInfo(CommonConstants.SHOW_IMAGE, String.class));
            // 设置原始图片
            referenceInfo.setOriginalImageUrl(
                creativeElement.getExtInfo(CommonConstants.KEY_STYLE_IMAGE, String.class));
            // 设置扩展标签
            referenceInfo.setBackTags(creativeElement.getExtTags());
            // 设置标签
            referenceInfo.setBackTags(creativeElement.getTags());
            // 扩展信息添加进 referenceInfo 中
            referenceInfo.setReferenceConfig(creativeElement.getExtInfo());
            // 设置 LoraId
            referenceInfo.setLoraId(creativeElement.getId());
            // 设置 loraPath
            referenceInfo.setLoraPath(creativeElement.getExtInfo(CommonConstants.KEY_LORA_PATH, String.class));

            // 设置进入addCreativeRequest中
            addCreativeRequest.setConfigs(tempRequest.getConfigs());
            addCreativeRequest.setImageNum(request.getImgNum());
            addCreativeRequest.setProportion(request.getProportion());
            addCreativeRequest.setPoseSampleDiagram(tempRequest);
            addCreativeRequest.setReferenceInfoList(Collections.singletonList(referenceInfo));
            try {
                // 创建姿势示例图
                creativeBatchService.create(CreativeTypeEnum.POSE_SAMPLE_DIAGRAM, addCreativeRequest);
            } catch (IOException e) {
                log.error("创建姿势示例图失败，sceneId={}", creativeElement.getId(), e);
            }
        });

        return Result.success();
    }

    @PostMapping("/applyRemoveWrinkle")
    public Result<CreativeBatchVO> applyRemoveWrinkle(@Valid @RequestBody RemoveWrinkleRequest request)
        throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.REMOVE_WRINKLE, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @Roles({ADMIN, OPERATOR, DEMO_ACCOUNT, MERCHANT, DISTRIBUTOR})
    @PostMapping("/removeWrinkle4DownCoat")
    public Result<CreativeBatchVO> removeWrinkle4DownCoat(@Valid @RequestBody RemoveWrinkle4DownCoatRequest request)
        throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.REMOVE_WRINKLE_4_DC, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/repairDetail")
    public Result<CreativeBatchVO> repairDetail(@Valid @RequestBody RepairDetailRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.REPAIR_DETAIL, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/brandTryOn")
    @Roles({ADMIN, OPERATOR, DISTRIBUTOR, MERCHANT, DEMO_ACCOUNT})
    public Result<CreativeBatchVO> brandTryOn(@Valid @RequestBody BrandTryOnRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.BRAND_TRY_ON, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/eraseBrush")
    public Result<?> eraseBrush(@Valid @RequestBody EraseBrushRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.ERASE_BRUSH, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @Roles({ADMIN, OPERATOR, DISTRIBUTOR, MERCHANT, DEMO_ACCOUNT})
    @PostMapping("/createEraseBrushV2")
    public Result<CreativeBatchVO> createEraseBrushV2(@Valid @RequestBody EraseBrushV2Request request)
        throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.ERASE_BRUSH_V2, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @Roles({ADMIN, OPERATOR, DISTRIBUTOR, MERCHANT, DEMO_ACCOUNT})
    @PostMapping("/eraseBrushOperate")
    public Result<?> eraseBrushOperate(@Valid @ModelAttribute EraseBrushV2Request request) {
        CreativeBatchVO data = eraseBrushModelService.eraseBrushOperate(request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @Roles({ADMIN, OPERATOR, DISTRIBUTOR, MERCHANT, DEMO_ACCOUNT})
    @PostMapping("/closeEraseBrushV2")
    public Result<?> closeEraseBrush(@JsonArg @NotNull Integer batchId) {
        eraseBrushModelService.closeBatch(batchId);
        return Result.success();
    }

    @PostMapping("/facePinching")
    public Result<?> facePinching(@Valid @RequestBody FacePinchingCreativeRequest request) throws IOException {
        CreativeBatchVO data = creativeBatchService.create(CreativeTypeEnum.FACE_PINCHING, request);
        return Result.success(SecurityUtils.filterExtInfo(data, outputKeyWhitelist));
    }

    @PostMapping("/addDemoTag")
    public Result<?> addDemoTag(@JsonArg @NotNull Integer id) throws IOException {
        creativeBatchService.addDemoTag(id);
        return Result.success();
    }

    @PostMapping("/queryCreateImageCnt")
    public Result<Integer> queryCreateImageCnt(@JsonArg @NotNull Integer modelId) {
        Integer cnt = creativeBatchService.queryCreateImageCntByModelId(modelId);
        return Result.success(cnt);
    }

    @GetMapping("/queryAllMerchantPreference/{type}")
    public Result<List<MerchantPreferenceVO>> queryAllMerchantPreference(@Valid @PathVariable("type") String type) {
        // 参数检查
        PreferenceTypeEnum typeEnum = PreferenceTypeEnum.getByCode(type);
        AssertUtil.assertNotNull(typeEnum, ResultCode.PARAM_INVALID, "类型错误");

        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        query.setUserId(OperationContextHolder.getOperatorUserId());
        query.setType(typeEnum.getCode());
        List<MerchantPreferenceVO> preferences = merchantPreferenceService.queryMerchantPreferenceList(query);
        return Result.success(preferences);
    }

    @PostMapping("/addMerchantPreference")
    public Result<List<MerchantPreferenceVO>> addMerchantPreference(@Valid @RequestBody MerchantPreferenceQuery query) {
        // 参数检查
        merchantPreferenceService.validateParam(query);
        PreferenceTypeEnum typeEnum = PreferenceTypeEnum.getByCode(query.getType());
        // 检查现有数据
        MerchantPreferenceQuery newQuery = new MerchantPreferenceQuery();
        newQuery.setUserId(OperationContextHolder.getOperatorUserId());
        newQuery.setType(typeEnum.getCode());
        List<MerchantPreferenceVO> preferences = merchantPreferenceService.queryMerchantPreferenceList(newQuery);
        AssertUtil.assertTrue(merchantPreferenceService.checkQtyLimit(typeEnum, preferences.size() + 1),
            ResultCode.PARAM_INVALID, "数量达到上限");
        // 插入数据
        query.setUserId(OperationContextHolder.getOperatorUserId());
        query.setOperatorId(OperationContextHolder.getOperatorUserId());
        MerchantPreferenceVO preference = merchantPreferenceService.insert(MerchantPreferenceConverter.query2VO(query));
        preferences.add(preference);
        return Result.success(preferences);
    }

    @PostMapping("/updateMerchantPreference")
    public Result<List<MerchantPreferenceVO>> updateMerchantPreference(
        @Valid @RequestBody MerchantPreferenceQuery query) {
        // 参数检查
        merchantPreferenceService.validateParam(query);
        // 检查现有数据
        MerchantPreferenceQuery newQuery = new MerchantPreferenceQuery();
        newQuery.setUserId(OperationContextHolder.getOperatorUserId());
        newQuery.setType(query.getType());
        List<MerchantPreferenceVO> preferences = merchantPreferenceService.queryMerchantPreferenceListWithBlob(
            newQuery);
        Integer index = preferences.stream().filter(p -> p.getId().equals(query.getId())).findFirst().map(
            preferences::indexOf).orElse(null);
        AssertUtil.assertNotNull(index, ResultCode.PARAM_INVALID, "更新对象不存在");
        // 更新
        query.setUserId(OperationContextHolder.getOperatorUserId());
        query.setOperatorId(OperationContextHolder.getOperatorUserId());
        MerchantPreferenceVO preference = MerchantPreferenceConverter.query2VO(query);
        merchantPreferenceService.updateByIdSelective(preference);
        preferences.get(index).addExtInfo(KEY_CUSTOM_SCENE_DESC, preference.getExtInfo().get(KEY_CUSTOM_SCENE_DESC));
        preferences.get(index).setClothCollocation(preference.getClothCollocation());
        return Result.success(preferences);
    }

    @PostMapping("/deleteMerchantPreference")
    public Result<List<MerchantPreferenceVO>> deleteMerchantPreference(
        @Valid @RequestBody MerchantPreferenceQuery query) {
        // 参数检查
        PreferenceTypeEnum typeEnum = PreferenceTypeEnum.getByCode(query.getType());
        AssertUtil.assertNotNull(typeEnum, ResultCode.PARAM_INVALID, "类型错误");
        // 检查现有数据
        MerchantPreferenceQuery newQuery = new MerchantPreferenceQuery();
        newQuery.setUserId(OperationContextHolder.getOperatorUserId());
        newQuery.setType(typeEnum.getCode());
        List<MerchantPreferenceVO> preferences = merchantPreferenceService.queryMerchantPreferenceListWithBlob(
            newQuery);
        MerchantPreferenceVO preference = preferences.stream().filter(p -> p.getId().equals(query.getId())).findFirst()
            .orElse(null);
        AssertUtil.assertNotNull(preference, ResultCode.PARAM_INVALID, "删除对象不存在");
        // 删除
        merchantPreferenceService.deleteById(preference.getId());
        preferences.removeIf(p -> p.getId().equals(preference.getId()));
        return Result.success(preferences);
    }

    @GetMapping("/fetchBatchClothDetailImage/{id}")
    public Result<?> fetchBatchClothMainImage(@NotNull @PathVariable("id") Integer id) {
        CreativeBatchVO batch = creativeBatchService.selectById(id);
        AssertUtil.assertNotNull(batch, ResultCode.PARAM_INVALID, "数据不存在");
        AssertUtil.assertNotNull(batch.getModelId(), ResultCode.PARAM_INVALID, "服装不存在");

        return Result.success(materialModelService.queryDetailShowImage(batch.getModelId()));
    }

    @GetMapping("/statsQueuedCreative")
    public Result<?> statsQueuedCreative() {
        return Result.success(creativeBatchService.statsQueuedCreative());
    }

    @GetMapping("/statsCustomerQueuedCreative")
    public Result<?> statsCustomerQueuedCreative() {
        return Result.success(creativeBatchService.statsCustomerQueuedCreative());
    }

    @GetMapping("/statsBackUserQueuedCreative")
    public Result<?> statsBackUserQueuedCreative() {
        return Result.success(creativeBatchService.statsBackUserQueuedCreative());
    }

    private String composePromptByTpl(CreativeTaskVO task) {
        ComfyuiTplInfo tplInfo = task.getTplInfo();
        if (tplInfo != null && tplInfo.getTplKey() != null) {
            ComfyuiWorkflowTemplateVO template = comfyuiWorkflowTemplateService.queryTemplateByKeyAndVersion(
                tplInfo.getTplKey(), tplInfo.getTplVersion());
            if (template != null && template.getTemplateData() != null) {
                Map<String, Object> params = tplInfo.getTplParams();

                // 类型兼容 correctFlow 内部使用
                if (params.containsKey(ElementConfigKeyEnum.SCENE.name())) {
                    Object scene = params.get(ElementConfigKeyEnum.SCENE.name());
                    if (scene instanceof JSONObject) {
                        params.put(ElementConfigKeyEnum.SCENE.name(),
                            ((JSONObject)scene).toJavaObject(CreativeElementVO.class));
                    }
                }

                if (params.containsKey(ElementConfigKeyEnum.FACE.name())) {
                    Object face = params.get(ElementConfigKeyEnum.FACE.name());
                    if (face instanceof JSONObject) {
                        params.put(ElementConfigKeyEnum.FACE.name(),
                            ((JSONObject)face).toJavaObject(CreativeElementVO.class));
                    }
                }

                // 下面两行顺序不能改，correctFlow 得在FreemarkerUtils.parse之前调用
                return FreemarkerUtils.parse(
                    creativeService.correctFlow(template.getTemplateData(), params, task, tplInfo.getTplKey()), params);
            }
        }
        return null;
    }

    /**
     * 获取任务工作流
     *
     * @param task 任务
     * @return 工作流
     * @throws JsonProcessingException 异常
     */
    private JSONObject fetchWorkflowByTask(CreativeTaskVO task) throws JsonProcessingException {
        @SuppressWarnings("deprecation")
        String prompt = task.getAigcRequest();

        if (StringUtils.isBlank(prompt)) {
            prompt = composePromptByTpl(task);
        }

        //noinspection LoggingSimilarMessage
        log.info("fetchWorkflow,prompt:{}", prompt);
        AssertUtil.assertNotBlank(prompt, ResultCode.BIZ_FAIL, "prompt为空");

        return JSONObject.parseObject(prompt);
    }
}
