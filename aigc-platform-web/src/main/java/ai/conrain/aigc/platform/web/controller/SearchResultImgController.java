package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.SearchResultImgVO;
import ai.conrain.aigc.platform.service.model.query.SearchResultImgQuery;
import ai.conrain.aigc.platform.service.component.SearchResultImgService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * SearchResultImg控制器
 *
 * <AUTHOR>
 * @version SearchResultImgService.java v 0.1 2025-08-16 05:50:47
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/searchResultImg")
public class SearchResultImgController {

	/** searchResultImgService */
	@Autowired
	private SearchResultImgService searchResultImgService;
	
	@GetMapping("/getById/{id}")
	public Result<SearchResultImgVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(searchResultImgService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody SearchResultImgVO searchResultImg){
		return Result.success(searchResultImgService.insert(searchResultImg).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		searchResultImgService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody SearchResultImgVO searchResultImg){
		searchResultImgService.updateByIdSelective(searchResultImg);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<SearchResultImgVO>> querySearchResultImgList(@Valid @RequestBody SearchResultImgQuery query){
		return Result.success(searchResultImgService.querySearchResultImgList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<SearchResultImgVO>> getSearchResultImgByPage(@Valid @RequestBody SearchResultImgQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(searchResultImgService.querySearchResultImgByPage(query));
	}
}