package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 图像分析结果模型
 * 简化版的ImageAnalysisCaptionModel
 */
@Data
public class ImageAnalysisResult {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 分析结果数据
     */
    @JSONField(name = "analysis")
    private ImageAnalysisCaption analysis;
    
    /**
     * 错误消息（如果有）
     */
    private String errorMessage;
}