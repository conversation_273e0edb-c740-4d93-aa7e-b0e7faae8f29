/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.constants;

/**
 * 系统配置常量
 *
 * <AUTHOR>
 * @version : SystemConstants.java, v 0.1 2023/9/4 19:03 renxiao.wu Exp $
 */
public interface SystemConstants {

    /** 验证码模板 */
    String CAPTCHA_SMS_TEMPLATE = "CAPTCHA_SMS_TEMPLATE";

    /** 最大子账号数量 */
    String MAX_SUB_USER_COUNT = "MAX_SUB_USER_COUNT";

    /** 最大刷新用户间隔 */
    String MAX_REFRESH_USER_INTERVAL = "MAX_REFRESH_USER_INTERVAL";

    /** 创作流程参数配置 */
    String CREATIVE_FLOW_PARAMS = "CREATIVE_FLOW_PARAMS";

    /** 纯色背景创作流程参数配置 */
    String CREATIVE_PURE_BG_FLOW_PARAMS = "CREATIVE_PURE_BG_FLOW_PARAMS";

    /** flux创作流程参数配置 */
    String FLUX_CREATIVE_FLOW = "FLUX_CREATIVE_FLOW";

    /** flux创作新流程参数配置 */
    String FLUX_CREATIVE_NEW_FLOW = "FLUX_CREATIVE_NEW_FLOW";

    /** flux创作换脸新流程参数配置 */
    String FLUX_CREATIVE_NEW_FACE_FLOW = "FLUX_CREATIVE_NEW_FACE_FLOW";

    /** flux创作换脸新instantId流程参数配置 */
    String FLUX_CREATIVE_NEW_INSTANT_ID_FLOW = "FLUX_CREATIVE_NEW_INSTANT_ID_FLOW";

    /** flux创作换脸instantIdV2流程参数配置 */
    String FLUX_CREATIVE_INSTANT_ID_V2_FLOW = "FLUX_CREATIVE_INSTANT_ID_V2_FLOW";

    /** flux创作风格模型实验流程参数配置 */
    String FLUX_CREATIVE_STYLE_EXP_FLOW = "FLUX_CREATIVE_STYLE_EXP_FLOW";

    /** 手部修复流程参数配置 */
    String REPAIR_HANDS_FLOW_PARAMS = "REPAIR_HANDS_FLOW_PARAMS";

    /** （自研）try-on工作流配置 */
    String TRYON_COMFYUI_WORKFLOW = "TRYON_COMFYUI_WORKFLOW";

    /** 图片抠图工作流配置 */
    String PICTURE_MATTING_COMFYUI_WORKFLOW = "PICTURE_MATTING_COMFYUI_WORKFLOW";

    /** 服装自动分割 */
    String CLOTH_AUTO_SEGMENT_WORKFLOW = "CLOTH_AUTO_SEGMENT_WORKFLOW";

    /** 服装换色 */
    String CLOTH_RECOLOR_WORKFLOW = "CLOTH_RECOLOR_WORKFLOW";

    /** 印花上身流程参数配置 */
    String LOGO_COMBINE_FLOW_PARAMS = "LOGO_COMBINE_FLOW_PARAMS";

    /** 印花上身for背面流程参数配置 */
    String LOGO_COMBINE_BACK_FLOW_PARAMS = "LOGO_COMBINE_BACK_FLOW_PARAMS";

    /** 图片放大流程参数配置 */
    String IMAGE_UPSCALE_FLOW_PARAMS = "IMAGE_UPSCALE_FLOW_PARAMS";

    /** 视频修脸流程参数配置 */
    String FIX_VIDEO_FACE_FLOW_PARAMS = "FIX_VIDEO_FACE_FLOW_PARAMS";

    /** 换头换背景 流程参数配置 */
    String FACE_SCENE_SWITCH_FLOW_PARAMS = "FACE_SCENE_SWITCH_FLOW_PARAMS";

    /** 模特换头流程参数配置 */
    String SWAP_FACE_FLOW_PARAMS = "SWAP_FACE_FLOW_PARAMS";

    /** 模特换头流程(INSTANT ID)参数配置 */
    String SWAP_FACE_INSTANT_ID_FLOW_PARAMS = "SWAP_FACE_INSTANT_ID_FLOW_PARAMS";

    /** 局部重绘流程参数配置 */
    String PARTIAL_REDRAW_FLOW_PARAMS = "PARTIAL_REDRAW_FLOW_PARAMS";

    /** 细节修补参数配置 */
    String REPAIR_DETAIL_FLOW_PARAMS = "REPAIR_DETAIL_FLOW_PARAMS";

    /** 基础款换衣参数配置 */
    String BASIC_CHANGING_CLOTHES_FLOW_PARAMS = "BASIC_CHANGING_CLOTHES_FLOW_PARAMS";

    /** CR Upscale 参数配置 */
    String CR_UPSCALE_FLOW_PARAMS = "CR_UPSCALE_FLOW_PARAMS";

    /** 消除笔工作流配置 */
    String ERASE_BRUSH_FLOW_PARAMS = "ERASE_BRUSH_FLOW_PARAMS";

    /** 消除笔V2工作流配置 */
    String ERASE_BRUSH_FLOW_PARAMS_V2 = "ERASE_BRUSH_FLOW_PARAMS_V2";

    /** 【基础款换衣参数配置】系统lora参考图配置 */
    String SYSTEM_LORA_REFERENCE_FLOW_PARAMS = "SYSTEM_LORA_REFERENCE_FLOW_PARAMS";

    /** 【基础款换衣参数配置】用户上传参考图页面示例相关配置 */
    String USER_UPLOAD_REFERENCE_FLOW_PARAMS = "USER_UPLOAD_REFERENCE_FLOW_PARAMS";

    /** 【固定姿势创作】flux创作换脸新instantId流程参数配置 */
    String FLUX_FIXED_POSTURE_CREATIVE_NEW_INSTANT_ID_FLOW = "FLUX_FIXED_POSTURE_CREATIVE_NEW_INSTANT_ID_FLOW";

    /** 【固定姿势创作】flux创作换脸新流程参数配置 */
    String FLUX_FIXED_POSTURE_CREATIVE_NEW_FACE_FLOW = "FLUX_FIXED_POSTURE_CREATIVE_NEW_FACE_FLOW";

    /** 【固定姿势创作】flux创作新流程参数配置 */
    String FLUX_FIXED_POSTURE_CREATIVE_NEW_FLOW = "FLUX_FIXED_POSTURE_CREATIVE_NEW_FLOW";

    /** 【固定姿势创作】flux创作风格模型实验流程参数配置 */
    String FLUX_FIXED_POSTURE_CREATIVE_STYLE_EXP_FLOW = "FLUX_FIXED_POSTURE_CREATIVE_STYLE_EXP_FLOW";

    /**
     * 首页登录注册配置内容
     */
    String HOME_LOGIN_REGISTER_CONFIG = "HOME_LOGIN_REGISTER_CONFIG";

    /** 姿势示例图流程配置 */
    String POSE_SAMPLE_DIAGRAM_FLOW_PARAMS = "POSE_SAMPLE_DIAGRAM_FLOW_PARAMS";

    /** 模特捏脸流程配置 */
    String FACE_PINCHING_FLOW = "FACE_PINCHING_FLOW";

    /** 固定姿势出图流程配置 */
    String FIXED_POSTURE_CREATION_FLOW = "FIXED_POSTURE_CREATION_FLOW";

    /** 前端上传素材页面示例相关配置 */
    String CLOTH_EXAM_CFG = "CLOTH_EXAM_CFG";

    /** 默认的价格方案配置 */
    String PRICE_PLAN_CFG = "PRICE_PLAN_CFG";

    /** 渠道商价格方案配置，key为PRICE_PLAN_DISTRIBUTOR_CFG.$corpOrgId，value为json array，指定渠道商和它的会员客户看到的价格方案 */
    String DISTRIBUTOR_PRICE_PLAN_CFG_PREFIX = "distributor.price.plan.";

    /** 前端后台选择场景分类 */
    String SCENE_TYPE_CFG = "SCENE_TYPE_CFG";

    /** 前端后台选择模特分类 */
    String FACE_TYPE_CFG = "FACE_TYPE_CFG";

    /** 前端后台选择服装款式分类 */
    String CLOTH_STYLE_TYPE_CFG = "CLOTH_STYLE_TYPE_CFG";

    /** 前端后台选择参考图分类 */
    String REFER_TYPE_CFG = "REFER_TYPE_CFG";

    /** 后台选择颜色分类 */
    String CLOTH_COLOR_TYPE_CFG = "CLOTH_COLOR_TYPE_CFG";

    /** 印花位置 */
    String LOGO_LOCATION_TYPE_CFG = "LOGO_LOCATION_TYPE_CFG";

    //场景适合的服装类型配置
    String SCENE_CLOTH_SCOPE_CFG = "SCENE_CLOTH_SCOPE_CFG";

    /** COMFYUI端口映射配置 */
    String COMFYUI_PORT_CFG = "COMFYUI_PORT_CFG";

    /**
     * lora训练任务用的端口映射配置
     */
    String COMFYUI_PORT_CFG_LORA = "COMFYUI_PORT_CFG_LORA";

    //根据服装素材id确定训练端口
    String COMFYUI_PORT_CFG_LORA_MATERIAL_ID = "COMFYUI_PORT_CFG_LORA_MATERIAL_ID";

    /**
     * 文本安全检测禁止标签列表
     */
    String TEXT_MODERATION_FORBID_LABEL = "TEXT_MODERATION_FORBID_LABEL";

    /** 图片安全检测禁止标签列表 */
    String MODERATION_FORBID_LABEL = "MODERATION_FORBID_LABEL";

    /** 情趣内衣的图片安全检测禁止标签列表 */
    String SEXY_LINGERIE_FORBID_LABEL = "SEXY_LINGERIE_FORBID_LABEL";

    /** 安全规则忽略的批次id列表 */
    String MODERATE_IGNORE_IDS = "MODERATE_IGNORE_IDS";

    /** 情趣内衣的品类列表 */
    String KEY_SEXY_LINGERIE_GARMENT = "KEY_SEXY_LINGERIE_GARMENT";

    /** 需要替换的模特关键字 */
    String NEED_REPLACE_MODEL_KEYS = "NEED_REPLACE_MODEL_KEYS";

    /** 渠道商结算费率配置前缀 */
    String DISTRIBUTOR_SETTLE_RATE_PREFIX = "distributor.settle.rate.";

    /** 后台运营配置 */
    String OPERATOR_CONFIG = "OPERATOR_CONFIG";

    /** 临时，10张精选图打标用户 */
    String TEN_EXAMPLE_IMAGES_USERS = "TEN_EXAMPLE_IMAGES_USERS";

    //lora vip models id list
    String LORA_VIP = "LORA_VIP";

    /** 鞋子后缀字典 */
    String SHOE_SUFFIX_DICT = "SHOE_SUFFIX_DICT";

    /** 服装颜色数量白名单，json string，{"100010":3} */
    String COLOR_NUMBER_CFG = "COLOR_NUMBER_CFG";

    /** 自动训练商家白名单 */
    String AUTO_TRAIN_MERCHANT = "AUTO_TRAIN_MERCHANT";

    /** 自动训练时间白名单 */
    String AUTO_TRAIN_TIME = "AUTO_TRAIN_TIME";

    /** 自动交付商家白名单 */
    String AUTO_DELIVERY_MERCHANT = "AUTO_DELIVERY_MERCHANT";

    /** 自动交付时间白名单 */
    String AUTO_DELIVERY_TIME = "AUTO_DELIVERY_TIME";

    /** 不使用系统搭配白名单 */
    String EXCLUDE_SYSTEM_COLLOCATION = "EXCLUDE_SYSTEM_COLLOCATION";

    /** 可以查看所有服装和历史记录的白名单 */
    String SEE_ALL_MODELS_AND_HISTORY = "SEE_ALL_MODELS_AND_HISTORY";

    String ADMIN_MENUS_CFG = "ADMIN_MENUS_CFG";

    /** 体验模型的开放配置 */
    String EXPERIENCE_MODEL_OPEN_CFG = "EXPERIENCE_MODEL_OPEN_CFG";

    /** 图片案例同步配置 */
    String IMAGE_CASE_SYNC_CONFIG = "IMAGE_CASE_SYNC_CONFIG";

    /** 第三方应用服务配置 */
    String THIRD_PART_SERVER_CFG = "THIRD_PART_SERVER_CFG";

    /** 系统lora排序配置 */
    String SYSTEM_LORA_ORDER_CFG = "SYSTEM_LORA_ORDER_CFG";

    /** 纯色背景场景id列表 */
    String PURE_BG_SCENE_IDS = "PURE_BG_SCENE_IDS";

    /** 夜间自动训练开关，20:00-09:00，包括none、all、vip */
    String NIGHTTIME_AUTO_TRAIN_SWITCH = "NIGHTTIME_AUTO_TRAIN_SWITCH";

    /** 训练打标服装细节prompt */
    String TRAIN_LABEL_CLOTH_DETAILS_PROMPT = "TRAIN_LABEL_CLOTH_DETAILS_PROMPT";

    /** 训练打标场景prompt */
    String TRAIN_LABEL_SCENE_PROMPT = "TRAIN_LABEL_SCENE_PROMPT";

    /** 训练打标模特prompt */
    String TRAIN_LABEL_FACE_PROMPT = "TRAIN_LABEL_FACE_PROMPT";

    /** 训练打标模特prompt */
    String TRAIN_LABEL_FACE_MINI_PROMPT = "TRAIN_LABEL_FACE_MINI_PROMPT";

    /** 训练打标服装prompt */
    String TRAIN_LABEL_CLOTH_PROMPT = "TRAIN_LABEL_CLOTH_PROMPT";

    /** 训练打标服装极简prompt */
    String TRAIN_LABEL_CLOTH_MINI_PROMPT = "TRAIN_LABEL_CLOTH_MINI_PROMPT";

    /** Lora训练抠图：只缩放 */
    String TRAIN_FLOW_CUTOUT_ONLY_UPSCALE = "TRAIN_FLOW_CUTOUT_ONLY_UPSCALE";

    /** Lora训练抠图：带人台 */
    String TRAIN_FLOW_CUTOUT_WITH_MANNEQUIN = "TRAIN_FLOW_CUTOUT_WITH_MANNEQUIN";

    /** 服装训练不抠图商家白名单列表 */
    String NO_CUTOUT_MERCHANT_LIST = "NO_CUTOUT_MERCHANT_LIST";

    /** 图片质量检测忽略标签 */
    String IMAGE_QUALITY_CHECK_IGNORE_LABEL = "IMAGE_QUALITY_CHECK_IGNORE_LABEL";

    /** lora打标开关 */
    String LORA_LABEL_SWITCH = "LORA_LABEL_SWITCH";

    /** lora新打标商家白名单 */
    String LORA_NEW_LABEL_MERCHANT_LIST = "LORA_NEW_LABEL_MERCHANT_LIST";

    /** lora极简打标商家白名单 */
    String LORA_MINI_LABEL_MERCHANT_LIST = "LORA_MINI_LABEL_MERCHANT_LIST";

    /** lora训练的扩展信息 */
    String LORA_TRAIN_EXT_INFO = "LORA_TRAIN_EXT_INFO";

    /** lora极简打标激活词配置 */
    String LORA_MINI_ACTIVATE_KEYS = "LORA_MINI_ACTIVATE_KEYS";

    /** 训练同步素材开关 */
    String TRAIN_FOLDER_SYNC_SWITCH = "TRAIN_FOLDER_SYNC_SWITCH";

    // kling api名称, "piapi" / "302api"，缺省为piapi
    String KLING_API_NAME = "KLING_API_NAME";

    /** 设备信息配置 */
    String DEVICE_INFO_CONFIG = "DEVICE_INFO_CONFIG";

    String TEMP_SWITCH = "TEMP_SWITCH";

    /** 先换脸再修脸开关 */
    String FACE_NEW_FLOW_SWITCH = "FACE_NEW_FLOW_SWITCH";

    /** 模特人种prompt */
    String FACE_RACE_PROMPT = "FACE_RACE_PROMPT";

    // 充值配置,控制用户是否可以看见充值入口
    /*
    {
  "hideCustomerByDistributor": {
    "distributor": [
      222
    ],
    "exceptCustomer": [
      333
    ]
  }
}
     */ String SHOW_TOPUP_CONFIG = "SHOW_TOPUP_CONFIG";

    // 高清分辨率白名单 {"P_1620_2100":[100023]}
    String SHOW_HQ_RESOLUTION_WHILTELIST = "SHOW_HQ_RESOLUTION_WHILTELIST";

    /** 决策服装复杂度prompt */
    String DECIDE_CLOTH_COMPLEX_PROMPT = "DECIDE_CLOTH_COMPLEX_PROMPT";

    /** 激活环境配置key */
    String ACTIVE_ENV = "system.active.env";

    /** 展示训练参数白名单 */
    String SHOW_TRAIN_PARAMS_WHITELIST = "SHOW_TRAIN_PARAMS_WHITELIST";

    // 展示换图按钮白名单
    String SHOW_CHANGE_IMG_BTN_WHITELIST = "SHOW_CHANGE_IMG_BTN_WHITELIST";

    /** 服装款式prompt */
    String CLOTH_CATEGORY_PROMPT = "CLOTH_CATEGORY_PROMPT";

    /** 服装类型（上半身/下半身/全身）款式prompt */
    String CLOTH_TYPE_CATEGORY_PROMPT = "CLOTH_TYPE_CATEGORY_PROMPT";

    /** 获取参考图中模特描述信息 */
    String GET_REFERENCE_FACE_PROMPT = "GET_REFERENCE_FACE_PROMPT";

    /** 创作的高优先级用户列表 */
    String CREATIVE_VIP_USERS = "CREATIVE_VIP_USERS";

    /** 服装款式分类列表 */
    String CLOTH_CATEGORY_CFG = "CLOTH_CATEGORY_CFG";

    /** 自动创建图片配置 */
    String AUTO_CREATE_IMAGE_CFG = "AUTO_CREATE_IMAGE_CFG";

    /** 闲时每个客户最大并发数 */
    String IDLE_MAX_PER_CUSTOMER = "IDLE_MAX_PER_CUSTOMER";

    /** 忙时每个客户最大并发数 */
    String BUSY_MAX_PER_CUSTOMER = "BUSY_MAX_PER_CUSTOMER";

    /** 使用细节修补流程单图换衣 */
    String BRAND_TRY_ON_FLOW = "BRAND_TRY_ON_FLOW";

    /** 抠图高清晰度服装类别列表 */
    String CUTOUT_HD_GARMENT_CATEGORIES = "CUTOUT_HD_GARMENT_CATEGORIES";

    /** 测试模型版本列表 */
    String TEST_MODEL_VERSION_LIST = "TEST_MODEL_VERSION_LIST";

    /** 底模测试seed库 */
    String BASE_MODEL_TEST_SEED_STORE = "BASE_MODEL_TEST_SEED_STORE";

    // =================== 订单相关配置 ===================

    /** 默认结算配置 */
    String DEFAULT_SETTLEMENT_CONFIGS = "DEFAULT_SETTLEMENT_CONFIGS";

    /** 订单“新续”界定期限 key */
    String ORDER_NEW_RENEWED_PERIOD = "ORDER_NEW_RENEWED_PERIOD";

    /** 订单“新续”界定金额 key */
    String ORDER_NEW_RENEWED_STANDARD = "ORDER_NEW_RENEWED_STANDARD";

    // =================== 考核相关配置 =====================
    /** 默认考核配置 */
    String DEFAULT_ASSESSMENT_CONFIGS = "DEFAULT_ASSESSMENT_CONFIGS";

    /** 考核业绩订单金额下限 */
    String ASSESSMENT_ORDER_LOWER_LIMIT = "ASSESSMENT_ORDER_LOWER_LIMIT";

    /** 考核默认提醒时长 */
    String ASSESSMENT_REMIND_DURATION = "ASSESSMENT_REMIND_DURATION";

    // =================== 用户额外配置 =====================
    String ADDITIONAL_CUSTOMER_REQUIREMENTS = "ADDITIONAL_CUSTOMER_REQUIREMENTS";
}
