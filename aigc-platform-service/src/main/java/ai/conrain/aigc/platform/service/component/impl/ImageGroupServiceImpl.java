package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.vo.ClothInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO;
import ai.conrain.aigc.platform.dal.example.ImageGroupExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageGroupQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupVO;
import ai.conrain.aigc.platform.service.model.converter.ImageGroupConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.service.component.ImageGroupService;

/**   
 * ImageGroupService实现
 *
 * <AUTHOR>
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
@Slf4j
@Service
public class ImageGroupServiceImpl implements ImageGroupService {

	/** DAO */
	@Autowired
	private ImageGroupDAO imageGroupDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private MaterialModelService materialModelService;

	@Override
	public ImageGroupVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ImageGroupDO data = imageGroupDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return ImageGroupConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = imageGroupDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageGroup失败");
	}

	@Override
	public ImageGroupVO insert(ImageGroupVO imageGroup) {
		AssertUtil.assertNotNull(imageGroup, ResultCode.PARAM_INVALID, "imageGroup is null");
		AssertUtil.assertTrue(imageGroup.getId() == null, ResultCode.PARAM_INVALID, "imageGroup.id is present");

		//创建时间、修改时间兜底
		if (imageGroup.getCreateTime() == null) {
			imageGroup.setCreateTime(new Date());
		}

		if (imageGroup.getModifyTime() == null) {
			imageGroup.setModifyTime(new Date());
		}

		ImageGroupDO data = ImageGroupConverter.vo2DO(imageGroup);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = imageGroupDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageGroup失败");
		AssertUtil.assertNotNull(data.getId(), "新建ImageGroup返回id为空");
		imageGroup.setId(data.getId());
		return imageGroup;
	}


	@Override
	public void updateByIdSelective(ImageGroupVO imageGroup) {
		AssertUtil.assertNotNull(imageGroup, ResultCode.PARAM_INVALID, "imageGroup is null");
    	AssertUtil.assertTrue(imageGroup.getId() != null, ResultCode.PARAM_INVALID, "imageGroup.id is null");

		//修改时间必须更新
		imageGroup.setModifyTime(new Date());
		ImageGroupDO data = ImageGroupConverter.vo2DO(imageGroup);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = imageGroupDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageGroup失败，影响行数:" + n);
	}

	@Override
	public List<ImageGroupVO> queryImageGroupList(ImageGroupQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupExample example = ImageGroupConverter.query2Example(query);

		List<ImageGroupDO> list = imageGroupDAO.selectByExample(example);
		return ImageGroupConverter.doList2VOList(list);
	}

	@Override
	public Long queryImageGroupCount(ImageGroupQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupExample example = ImageGroupConverter.query2Example(query);
		return imageGroupDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询图像组，由多种图组成的pair对
	 */
	@Override
	public PageInfo<ImageGroupVO> queryImageGroupByPage(ImageGroupQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<ImageGroupVO> page = new PageInfo<>();

		ImageGroupExample example = ImageGroupConverter.query2Example(query);
		long totalCount = imageGroupDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<ImageGroupDO> list = imageGroupDAO.selectByExample(example);
		page.setList(ImageGroupConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    @Override
    public void updateByClothId(Integer imageGroupId, Integer clothId) {
        ImageGroupVO imageGroupVO = selectById(imageGroupId);
        AssertUtil.assertNotNull(imageGroupVO, ResultCode.PARAM_INVALID, "imageGroup is null");
        ClothInfoVO clothInfo = getClothInfoById(clothId);
        Integer imageId = imageService.saveClothInfo(clothInfo);
        List<Integer> imageIds = imageGroupVO.getImageIds();
        if (imageIds.size() > 1) {
            imageIds.set(1, imageId);
            ImageGroupQuery imageGroupQuery = new ImageGroupQuery();
            imageGroupQuery.setImageIds(imageIds);
            List<ImageGroupDO> existingImageGroups = imageGroupDAO.selectByExample(
                ImageGroupConverter.query2Example(imageGroupQuery));
            if (!existingImageGroups.isEmpty()) {
                throw new BizException(ResultCode.DUPLICATE_CLOTH_CONFIGURATION);
            }
        }
        imageIds.add(imageId);
        imageGroupVO.setModifyTime(new Date());
        updateByIdSelective(imageGroupVO);
    }

    @Override
    public ClothInfoVO getClothInfoById(@JsonArg @NotNull Integer id) {
        MaterialModelVO materialModelVO = materialModelService.selectById(id);
        AssertUtil.assertNotNull(materialModelVO, ResultCode.MATERIAL_NOT_FOUND, "未找到素材");

        if (materialModelVO.getClothLoraTrainDetail() == null
            || materialModelVO.getClothLoraTrainDetail().getLabel() == null
            || materialModelVO.getClothLoraTrainDetail().getLabel().getStatus() != QueueResult.QueueCodeEnum.COMPLETED) {
            throw new BizException(ResultCode.MATERIAL_NOT_TAGGED, "素材没有完成打标");
        }

        ClothInfoVO c = new ClothInfoVO();
        c.setId(materialModelVO.getId());
        c.setName(materialModelVO.getName());
        c.setFullBodyFrontViewImageUrl(materialModelService.queryDetailShowImage(id));

        return c;
    }

}