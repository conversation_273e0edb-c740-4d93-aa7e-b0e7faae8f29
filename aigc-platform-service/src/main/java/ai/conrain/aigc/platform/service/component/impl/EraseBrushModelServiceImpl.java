package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.EraseBrushModelService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.ImageFormatEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.EraseBrushV2Request;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;


@Slf4j
@Service
public class EraseBrushModelServiceImpl implements EraseBrushModelService {

    @Value("${comfyui.output.path}")
    private String outputPath;

    @Autowired
    private CreativeBatchService creativeBatchService;

    @Autowired
    private CreativeTaskService creativeTaskService;

    @Autowired
    private OssService ossService;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private ServerHelper serverHelper;

    @Autowired
    private FileDispatch fileDispatch;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreativeBatchVO eraseBrushOperate(EraseBrushV2Request request) {

        long startTime = System.currentTimeMillis();

        // 1. 参数校验
        AssertUtil.assertNotNull(request.getBatchId(), ResultCode.PARAM_INVALID, "批次ID不能为空");
        AssertUtil.assertNotNull(request.getMaskFile(), ResultCode.PARAM_INVALID, "蒙版文件不能为空");

        // 2. 获取当前任务
        CreativeBatchVO batch = creativeBatchService.selectById(request.getBatchId());
        AssertUtil.assertNotNull(batch, ResultCode.BIZ_FAIL, "当前任务数据异常");

        // 3. 处理任务栈
        Deque<Integer> taskStack = getTaskStack(batch);
        if (!taskStack.isEmpty()) {
            // 任务栈为空时, 不需要传 taskId, 反之则需要
            AssertUtil.assertNotNull(request.getTaskId(), ResultCode.PARAM_INVALID, "任务ID不能为空");
        }
        // 3.1 执行出栈动作, 并删除出栈的 task
        creativeTaskService.batchDeleteByIds(handleTaskStack(taskStack, request.getTaskId()));

        // 4. 查询前一个 task
        CreativeTaskVO previousTask = null;
        if (!taskStack.isEmpty() && !ObjectUtils.isEmpty(taskStack.peek())) {
            previousTask = creativeTaskService.selectById(taskStack.peek());
        }

        // 5. 创建任务
        CreativeTaskVO task = creativeTaskService.insert(buildTask(batch, previousTask));
        // 6. 服务分发
        dispatch(task);
        try {
            // 7. 处理消除笔操作
            processEraseBrush(request.getMaskFile().getBytes(), task);

            // 8. 更新任务状态
            task.setStatus(CreativeStatusEnum.FINISHED);
            creativeTaskService.updateByIdSelective(task);

            taskStack.push(task.getId());
            batch.addExtInfo(KEY_CREATIVE_TASK_STACK, taskStack);
            batch.addExtInfo("customStatus", "PROCESSING");
            batch.setShowImage(task.getResultImages().get(0));
            batch.setResultImages(task.getResultImages());
            batch.setBatchCnt(1);
            creativeBatchService.updateByIdSelective(batch);

            log.info("[消除笔V2]消除笔任务执行成功 - batchId: {}, taskId: {}", batch.getId(), task.getId());
        } catch (IOException e) {
            log.error("[消除笔V2]消除笔任务执行失败 - batchId: {}, taskId: {}, 错误: {}", batch.getId(), task.getId(), e.getMessage(), e);
            // 调用接口失败时删除当前任务
            creativeTaskService.deleteById(task.getId());
            throw new BizException(ResultCode.BIZ_FAIL, "消除失败请重试");
        }

        log.info("[消除笔V2]taskId = {}, 任务总耗时: {}", task.getId(), System.currentTimeMillis() - startTime);
        return creativeBatchService.getCreativeBatchByIdWithTask(batch.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeBatch(Integer batchId) {
        CreativeBatchVO batch = creativeBatchService.selectById(batchId);
        AssertUtil.assertNotNull(ResultCode.BIZ_FAIL, "[消除笔V2] 数据异常");
        if (batch.getBatchCnt() <= 0 || StringUtils.equals("INIT", batch.getStringFromExtInfo("customStatus"))) {
            creativeBatchService.deleteById(batch.getId());
        } else {
            batch.addExtInfo("customStatus", "FINISHED");
            creativeBatchService.updateByIdSelective(batch);
        }
    }

    /**
     * 处理消除笔操作，包括调用API和保存结果
     *
     * @param maskBytes 蒙版文件字节数组
     * @param task      创作任务
     * @throws IOException 如果处理过程中出现错误
     */
    private void processEraseBrush(byte[] maskBytes, CreativeTaskVO task) throws IOException {
        long startTime = System.currentTimeMillis();
        // 1. 准备参数
        String originImagePath = task.getExtInfo(KEY_ORIGIN_IMAGE_PATH);
        String originImagePrefix = task.getExtInfo(KEY_ORIGIN_IMAGE_PREFIX);
        String resultImagePath = outputPath + ComfyUIUtils.buildOutputPath(task.getBatchId());
        String resultImagePrefix = ComfyUIUtils.buildFileNameWithTimestamp(task.getId());
        String resultFormat = task.getExtInfo(KEY_RESULT_IMAGE_FORMAT);
        task.addExtInfo(KEY_RESULT_IMAGE_PATH, resultImagePath);
        task.addExtInfo(KEY_RESULT_IMAGE_PREFIX, resultImagePrefix);

        String serverUrl = task.getExtInfo(KEY_SERVER_URL);
        AssertUtil.assertNotNull(serverUrl, ResultCode.SYS_ERROR, "无去除背景服务");

        log.info("[消除笔V2]开始处理消除笔任务 - taskId: {}, serverUrl: {}, originImagePath: {}, resultFormat: {}", task.getId(), serverUrl, originImagePath,  resultFormat);

        try {
            // 2. 调用 lama_remover API
            byte[] resultImageBytes = comfyUIService.callLamaRemoverApi(
                    originImagePath, originImagePrefix, maskBytes, resultImagePath, resultImagePrefix, resultFormat, serverUrl);

            log.info("[消除笔V2]taskId = {}, 调用接口耗时: {}", task.getId(), System.currentTimeMillis() - startTime);

            // 3. 验证结果
            if (resultImageBytes == null || resultImageBytes.length == 0) {
                log.warn("[消除笔V2]消除笔API返回空结果 - taskId: {}", task.getId());
                throw new BizException(ResultCode.BIZ_FAIL, "消除笔API返回空结果");
            }

            log.info("[消除笔V2]消除笔API处理完成 - taskId: {}, 结果图片大小: {} KB", task.getId(), resultImageBytes.length / 1024);
            ImageFormatEnum imageFormat = ImageFormatEnum.getByFormatName(resultFormat);
            AssertUtil.assertNotNull(imageFormat, ResultCode.BIZ_FAIL, "获取图片格式失败");

            // 4. 上传结果到OSS
            startTime = System.currentTimeMillis();
            String imageUrl = uploadResultToOss(resultImageBytes, task, resultImagePrefix, imageFormat.getDottedSuffix());

            log.info("[消除笔V2]taskId = {}, 上传到oss耗时: {}", task.getId(), System.currentTimeMillis() - startTime);

            // 5. 保存结果到任务
            task.addResultImage(imageUrl);
            log.info("[消除笔V2]消除笔任务处理完成 - taskId: {}, imageUrl: {}", task.getId(), imageUrl);

            // 6. 发起异步文件同步
            fileDispatch.notifyFileSync(serverUrl, resultImagePath + "/" + resultImagePrefix + imageFormat.getDottedSuffix(), false);


        } catch (IOException e) {
            log.error("[消除笔V2]消除笔任务处理失败 - taskId: {}, 错误: {}", task.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 将结果图片上传到OSS
     *
     * @param imageBytes     图片字节数组
     * @param task           创意任务
     * @param filenamePrefix 文件名前缀
     * @param imageExt       图片扩展名
     * @return OSS图片URL
     * @throws IOException 如果上传过程中出现错误
     */
    private String uploadResultToOss(byte[] imageBytes, CreativeTaskVO task, String filenamePrefix, String imageExt) throws IOException {
        try {
            InputStream inputStream = new ByteArrayInputStream(imageBytes);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
            String fullName = sdf.format(task.getCreateTime()) + task.getUserId() + "/" + filenamePrefix + imageExt;
            return ossService.upload(fullName, inputStream);
        } catch (Exception e) {
            log.error("[消除笔V2]上传结果到OSS失败 - taskId: {}, 错误: {}", task.getId(), e.getMessage(), e);
            throw new IOException("上传结果到OSS失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理任务栈
     * taskId 不为空时, 表示退回到指定的 task, 此时执行出栈
     * taskId = 0 时, 表示清空所有任务, 回退到原图, 此时清空栈
     *
     * @param taskStack 任务栈
     * @param taskId    任务ID
     */
    private List<Integer> handleTaskStack(Deque<Integer> taskStack, Integer taskId) {
        if (taskStack.isEmpty() || taskId == null) {
            return new  ArrayList<>();
        }
        List<Integer> taskToDelete = new ArrayList<>();
        // 弹出栈中直到找到指定的任务ID
        // taskId 为 0 时, 所有任务都会出栈
        while (!taskStack.isEmpty() && !(taskStack.peek().equals(taskId))) {
            taskToDelete.add(taskStack.pop());
        }
        return taskToDelete;
    }

    /**
     * 创建 任务 task, 并补全必要的扩展字段
     * 如果有前一个任务, 从前一个任务中获取
     * 否则直接从 batch 获取
     * @param batch
     * @param previousTask
     * @return
     */
    private CreativeTaskVO buildTask(CreativeBatchVO batch, CreativeTaskVO previousTask) {
        CreativeTaskVO task = CreativeTaskConverter.request2VO(batch);
        // 原图 oss
        String originImage = batch.getExtInfo(KEY_ORIGIN_IMAGE);
        // 原图 comfyUI 绝对路径
        String originImagePath = batch.getExtInfo(KEY_ORIGIN_IMAGE_PATH);
        // 原图 comfyUI 文件名前缀
        String originImagePrefix = batch.getExtInfo(KEY_ORIGIN_IMAGE_PREFIX);
        // 结果图片格式
        String resultImageFormat = ImageFormatEnum.JPEG.getFormatName();
        // 服务 id
        String serverId = batch.getExtInfo(KEY_SERVER_ID);
        // 服务 url
        String serverUrl = batch.getExtInfo(KEY_SERVER_URL);

        if (!ObjectUtils.isEmpty(previousTask)) {
            // 上一个任务的结果图 oss
            originImage = previousTask.getResultImages().get(0);
            // 上一个任务的结果图 comfyUI 路径
            originImagePath = previousTask.getExtInfo(KEY_RESULT_IMAGE_PATH);
            // 上一个任务的结果图 文件名前缀
            originImagePrefix = previousTask.getExtInfo(KEY_RESULT_IMAGE_PREFIX);
            // 上一个任务的服务 id
            serverId =  previousTask.getExtInfo(KEY_SERVER_ID);
            // 上一任务的服务 url
            serverUrl = previousTask.getExtInfo(KEY_SERVER_URL);
        }
        task.addExtInfo(KEY_ORIGIN_IMAGE, originImage);
        task.addExtInfo(KEY_ORIGIN_IMAGE_PATH, originImagePath);
        task.addExtInfo(KEY_ORIGIN_IMAGE_PREFIX, originImagePrefix);
        task.addExtInfo(KEY_RESULT_IMAGE_FORMAT, resultImageFormat);
        task.addExtInfo(KEY_SERVER_ID, serverId);
        task.addExtInfo(KEY_SERVER_URL, serverUrl);

        return task;
    }

    /**
     * 解析扩展字段中的 任务栈
     * @param data batch
     * @return 任务栈
     */
    private Deque<Integer> getTaskStack(CreativeBatchVO data) {
        List<Integer> list = JSON.parseArray(data.getExtInfo(KEY_CREATIVE_TASK_STACK), Integer.class);
        if (list == null || list.isEmpty()) {
            return new ArrayDeque<>();
        }
        return new ArrayDeque<>(list);
    }

    /**
     * 判断前一个任务的服务是否可用,
     * 如果可用优先使用之前的服务
     * 不可用再重新分发服务
     *
     * @param task 任务
     */
    private void dispatch(CreativeTaskVO task) {
        String serverUrl = task.getStringFromExtInfo(KEY_SERVER_URL);

        if (StringUtils.isNotBlank(serverUrl)) {
            if (serverHelper.isEnable(serverUrl)) {
                return;
            }
            log.warn("【消除笔V2】从任务扩展中获取到服务，但服务已停用，重新计算可用服务,taskId={},serverUrl={}", task.getId(), serverUrl);
        }

        log.warn("【消除笔V2】无法从任务扩展中获取到服务, 重新计算可用服务,taskId={}", task.getId());
        // 获取新的服务
        ServerVO newServer = serverHelper.getModelServerByUser(task.getUserId());
        AssertUtil.assertNotNull(newServer, ResultCode.BIZ_FAIL, "获取服务失败");
        task.addExtInfo(KEY_SERVER_ID, newServer.getId());
        task.addExtInfo(KEY_SERVER_URL, serverHelper.getServerUrl(newServer));
    }
}
