package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.TestItemDAO;
import ai.conrain.aigc.platform.dal.entity.TestItemDO;
import ai.conrain.aigc.platform.dal.example.TestItemExample;
import ai.conrain.aigc.platform.service.component.TestCaseService;
import ai.conrain.aigc.platform.service.component.TestItemGroupService;
import ai.conrain.aigc.platform.service.component.TestItemService;
import ai.conrain.aigc.platform.service.enums.TestGroupTypeEnum;
import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TestItemConverter;
import ai.conrain.aigc.platform.service.model.query.TestItemGroupQuery;
import ai.conrain.aigc.platform.service.model.query.TestItemQuery;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import ai.conrain.aigc.platform.service.model.vo.TestCaseVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_CASE_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_PARAMS;

/**
 * TestItemService实现
 *
 * <AUTHOR>
 * @version TestItemService.java v 0.1 2024-12-19 01:24:06
 */
@Slf4j
@Service
public class TestItemServiceImpl implements TestItemService {
    @Autowired
    private TestItemDAO testItemDAO;
    @Autowired
    private TestItemGroupService testItemGroupService;
    @Autowired
    private TestCaseService testCaseService;

    @Override
    public TestItemVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TestItemDO data = testItemDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return TestItemConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = testItemDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TestItem失败");
    }

    @Override
    public TestItemVO insert(TestItemVO testItem) {
        AssertUtil.assertNotNull(testItem, ResultCode.PARAM_INVALID, "testItem is null");
        AssertUtil.assertTrue(testItem.getId() == null, ResultCode.PARAM_INVALID, "testItem.id is present");

        //创建时间、修改时间兜底
        if (testItem.getCreateTime() == null) {
            testItem.setCreateTime(new Date());
        }

        if (testItem.getModifyTime() == null) {
            testItem.setModifyTime(new Date());
        }

        TestItemDO data = TestItemConverter.vo2DO(testItem);
        int n = testItemDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TestItem失败");
        AssertUtil.assertNotNull(data.getId(), "新建TestItem返回id为空");
        testItem.setId(data.getId());
        return testItem;
    }

    @Override
    public void updateByIdSelective(TestItemVO testItem) {
        AssertUtil.assertNotNull(testItem, ResultCode.PARAM_INVALID, "testItem is null");
        AssertUtil.assertTrue(testItem.getId() != null, ResultCode.PARAM_INVALID, "testItem.id is null");

        testItem.setRoundsNum(calcRoundsNum(testItem));

        //修改时间必须更新
        testItem.setModifyTime(new Date());
        TestItemDO data = TestItemConverter.vo2DO(testItem);
        int n = testItemDAO.updateByPrimaryKeySelective(data);

        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TestItem失败，影响行数:" + n);
    }

    @Override
    public List<TestItemVO> queryTestItemList(TestItemQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestItemExample example = TestItemConverter.query2Example(query);

        List<TestItemDO> list = testItemDAO.selectByExampleWithBLOBs(example);

        List<TestItemVO> result = TestItemConverter.doList2VOList(list);
        if (CollectionUtils.isNotEmpty(result)) {
            TestItemGroupQuery groupQuery = new TestItemGroupQuery();
            groupQuery.setItemIds(result.stream().map(TestItemVO::getId).collect(Collectors.toList()));
            List<TestItemGroupVO> groups = testItemGroupService.queryTestItemGroupList(groupQuery);

            if (CollectionUtils.isNotEmpty(groups)) {
                result.forEach(e -> e.setGroups(
                    groups.stream().filter(i -> e.getId().equals(i.getItemId())).collect(Collectors.toList())));
            }
        }
        return result;
    }

    @Override
    public Long queryTestItemCount(TestItemQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestItemExample example = TestItemConverter.query2Example(query);
        long c = testItemDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询AB测试项目
     */
    @Override
    public PageInfo<TestItemVO> queryTestItemByPage(TestItemQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<TestItemVO> page = new PageInfo<>();

        TestItemExample example = TestItemConverter.query2Example(query);
        long totalCount = testItemDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<TestItemDO> list = testItemDAO.selectByExample(example);
        page.setList(TestItemConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Integer planId, TestTypeEnum type, List<TestItemVO> items, List<CreateTestParams> testParams) {
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(items), ResultCode.PARAM_INVALID, "参数非法");

        for (TestItemVO vo : items) {
            vo.setPlanId(planId);
            vo.setType(vo.getType());
            vo.setOperatorId(OperationContextHolder.getOperatorUserId());
            vo.setRoundsNum(calcRoundsNum(vo));
            vo.setStatus(TestStatusEnum.ENABLED);

            insert(vo);

            int idx = 0;
            for (JSONObject comparison : vo.getComparisonParams()) {
                TestItemGroupVO group = new TestItemGroupVO();
                group.setPlanId(planId);
                group.setItemId(vo.getId());
                group.setType(type);
                group.setComparisonParams(comparison);
                group.setRoundsNum(vo.getRoundsNum());
                group.setStatus(TestStatusEnum.ENABLED);
                group.setNegativeNum(0);
                group.setPositiveNum(0);
                if (idx++ == 0) {
                    group.setGroupType(TestGroupTypeEnum.EXPERIMENTAL);
                } else {
                    group.setGroupType(TestGroupTypeEnum.CONTROL);
                }
                if (testParams != null) {
                    group.addExtInfo(KEY_TEST_PARAMS,
                        testParams.subList(0, Math.min(vo.getRoundsNum(), testParams.size())));
                }
                testItemGroupService.insert(group);
            }

        }

    }

    @Override
    public List<TestItemVO> queryByPlanId(Integer planId) {
        TestItemQuery query = new TestItemQuery();
        query.setPlanId(planId);
        return queryTestItemList(query);
    }

    /**
     * 计算轮数
     *
     * @param testItem 测试项
     * @return 轮数
     */
    private Integer calcRoundsNum(TestItemVO testItem) {
        Integer roundsNum = testItem.getSharedParams().getInteger("imageNum");

        Integer testCaseId = testItem.getSharedParams().getInteger(KEY_TEST_CASE_ID);
        if (testCaseId != null) {
            TestCaseVO testCase = testCaseService.selectById(testCaseId);
            roundsNum = roundsNum * testCase.getCaseNum();
        }
        return roundsNum;
    }
}