/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.DictTagsEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.StyleScene.OutfitDetail;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.util.ChineseCharacterUtils;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_OUTFIT;

/**
 * 服装搭配帮助类
 *
 * <AUTHOR>
 * @version : ClothCollocationHelper.java, v 0.1 2024/11/18 10:14 renxiao.wu Exp $
 */
@Slf4j
@Component
public class ClothCollocationHelper {
    @Autowired
    private PromptDictService promptDictService;

    /**
     * 翻译已知的提示词
     *
     * @param originModel 原始服装搭配模型
     */
    public void transKnownPrompt(ClothCollocationModel originModel) {
        if (isEmpty(originModel)) {
            return;
        }

        originModel.setTops(transPrompt(originModel.getTops()));
        originModel.setBottoms(transPrompt(originModel.getBottoms()));
        originModel.setOthers(transPrompt(originModel.getOthers()));
        originModel.setProps(transPrompt(originModel.getProps()));
        originModel.setShoe(transPrompt(originModel.getShoe()));
    }

    /**
     * 存储翻译后的prompt
     *
     * @param origin 原始服装搭配
     * @param trans  翻译后服装搭配
     */
    public void storeNewPrompt(ClothCollocationModel origin, ClothCollocationModel trans) {
        if (isEmpty(origin) || isEmpty(trans)) {
            return;
        }

        List<PromptDictVO> target = new ArrayList<>();
        fillNewPrompts(origin.getShoe(), trans.getShoe(), target, DictTagsEnum.SHOE);
        fillNewPrompts(origin.getTops(), trans.getTops(), target, DictTagsEnum.TOPS);
        fillNewPrompts(origin.getBottoms(), trans.getBottoms(), target, DictTagsEnum.BOTTOMS);
        fillNewPrompts(origin.getOthers(), trans.getOthers(), target, DictTagsEnum.OTHERS);
        fillNewPrompts(origin.getProps(), trans.getProps(), target, DictTagsEnum.PROPS);

        if (CollectionUtils.isEmpty(target)) {
            return;
        }
        promptDictService.batchInsert(target);
    }

    /**
     * 通过判断model中所有字段是否包含中文，来确定是否需要翻译
     *
     * @param model 服装搭配模型
     * @return true，需要翻译
     */
    public static boolean isNeedTrans(ClothCollocationModel model) {
        if (isEmpty(model)) {
            return false;
        }

        return ChineseCharacterUtils.containsChinese(model.getShoe()) || ChineseCharacterUtils.containsChinese(
            model.getTops()) || ChineseCharacterUtils.containsChinese(model.getBottoms())
               || ChineseCharacterUtils.containsChinese(model.getOthers()) || ChineseCharacterUtils.containsChinese(
            model.getProps());
    }

    /**
     * 格式化服装搭配，去除冲突的关键词
     *
     * @param clothCollocation 目标搭配
     * @return 格式化后搭配词
     */
    public static ClothCollocationModel formatClothCollocation(ClothCollocationModel clothCollocation) {
        ClothCollocationModel target = new ClothCollocationModel();

        String tops = ComfyUIUtils.removeConflictingKey(clothCollocation.getTops());
        if (!CommonUtil.isNoneString(tops)) {
            target.setTops(tops);
        }
        String bottoms = ComfyUIUtils.removeConflictingKey(clothCollocation.getBottoms());
        if (!CommonUtil.isNoneString(bottoms)) {
            target.setBottoms(bottoms);
        }
        String others = ComfyUIUtils.removeConflictingKey(clothCollocation.getOthers());
        if (!CommonUtil.isNoneString(others)) {
            target.setOthers(others);
        }
        String props = ComfyUIUtils.removeConflictingKey(clothCollocation.getProps());
        if (!CommonUtil.isNoneString(props)) {
            target.setProps(props);
        }
        String shoe = ComfyUIUtils.removeConflictingKey(clothCollocation.getShoe());
        if (!CommonUtil.isNoneString(shoe)) {
            target.setShoe(shoe);
        }

        return target;
    }

    /**
     * 是否为空
     *
     * @return true，为空
     */
    public static boolean isEmpty(ClothCollocationModel clothCollocation) {
        if (clothCollocation == null) {
            return true;
        }
        return StringUtils.isBlank(clothCollocation.getShoe()) && StringUtils.isBlank(clothCollocation.getTops())
               && StringUtils.isBlank(clothCollocation.getBottoms()) && StringUtils.isBlank(
            clothCollocation.getOthers()) && StringUtils.isBlank(clothCollocation.getProps());
    }

    /**
     * 提取风格的服装搭配
     *
     * @param sceneElement       场景元素
     * @param aiClothCollocation ai推荐的服装搭配
     * @return 服装搭配模型
     */
    public static ClothCollocationModel fetchStyleOutfit(CreativeElementVO sceneElement, MaterialModelVO modelVO,
                                                         ClothCollocationModel aiClothCollocation) {
        if (!ElementUtils.isStyleScene(sceneElement)) {
            return null;
        }
        String outfitStr = sceneElement.getExtInfo(KEY_STYLE_OUTFIT, String.class);
        OutfitDetail outfit = JSONObject.parseObject(outfitStr, OutfitDetail.class);
        if (outfit == null || modelVO == null || modelVO.getClothLoraTrainDetail() == null) {
            return null;
        }
        String clothType = modelVO.getClothLoraTrainDetail().getClothType();
        ClothCollocationModel result = new ClothCollocationModel();

        //设置上装：
        String tops = null;
        //如果服装款式是下装，则将上装设置为外套和内搭
        if (StringUtils.equals(clothType, CommonConstants.lowerGarment)) {
            tops = CommonUtil.formatTextWithComma(outfit.getOuterwear()) + CommonUtil.formatTextWithComma(
                outfit.getInnerwear());
        } else if (aiClothCollocation != null && StringUtils.isNotBlank(aiClothCollocation.getTops())) { //另外的条件是套装或上装
            //如果是外套，那么ai会推荐内搭；如果是内搭，ai不会推荐外套
            //我们只设置外套的内搭
            tops = CommonUtil.formatTextWithComma(outfit.getInnerwear());
            if (StringUtils.isBlank(tops)) {
                tops = formatAIGenPrompt(aiClothCollocation.getTops());
            }
        }

        result.setTops(tops);

        //设置下装：
        //只有当服装款式是上装时才设置下身服装
        if (StringUtils.equals(clothType, CommonConstants.upperGarment)) {
            String bottoms = !CommonUtil.isNoneString(outfit.getBottoms()) ? outfit.getBottoms()
                : (aiClothCollocation != null ? formatAIGenPrompt(aiClothCollocation.getBottoms()) : null);
            result.setBottoms(CommonUtil.formatTextWithComma(bottoms));
        }

        result.setShoe(CommonUtil.formatTextWithComma(outfit.getShoes()));
        result.setProps(CommonUtil.formatTextWithComma(outfit.getProps()));
        if (CollectionUtils.isNotEmpty(outfit.getAccessories())) {
            StringBuilder others = new StringBuilder();
            outfit.getAccessories().forEach(a -> others.append(CommonUtil.formatTextWithComma(a)));
            result.setOthers(others.toString());
        }
        return result;
    }

    /**
     * 格式化AI生成的服装搭配
     * </br>
     * 将原有,替换成|，做分割随机
     *
     * @param prompt prompt
     * @return 格式化后的prompt
     */
    private static String formatAIGenPrompt(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return prompt;
        }
        return prompt.replaceAll(",", "|");
    }

    /**
     * 将服装搭配prompt进行翻译
     *
     * @param prompt prompt值
     * @return 翻译后的prompt
     */
    private String transPrompt(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return prompt;
        }
        //1 先将prompt中带中文逗号的统一改成英文逗号
        String target = StringUtils.replace(prompt, "，", ",");
        //2 基于,将词组进行拆分
        String[] words = StringUtils.split(target, ",");

        StringBuilder sb = new StringBuilder();
        for (String word : words) {
            //3 去掉首尾空格
            String trimWord = StringUtils.trim(word);
            if (StringUtils.isBlank(trimWord)) {
                continue;
            }

            //4 获取字典中的翻译
            String transWord = promptDictService.queryValueByKey(trimWord);
            //5 如果有翻译，则替换，否则不替换
            if (StringUtils.isNotBlank(transWord)) {
                sb.append(",").append(transWord);
            } else {
                sb.append(",").append(trimWord);
            }
        }

        return sb.length() <= 0 ? prompt : sb.substring(1);
    }

    /**
     * 填充新的prompt
     *
     * @param origin 原始搭配
     * @param trans  翻译后搭配
     * @param target 填充目标
     */
    private void fillNewPrompts(String origin, String trans, List<PromptDictVO> target, DictTagsEnum tags) {
        if (StringUtils.isBlank(origin) || StringUtils.isBlank(trans)) {
            return;
        }

        String[] originWords = StringUtils.split(origin, ",");
        String[] transWords = StringUtils.split(trans, ",");
        for (int i = 0; i < originWords.length; i++) {
            String originWord = StringUtils.trim(originWords[i]);
            String transWord = i >= transWords.length ? null : StringUtils.trim(transWords[i]);
            //如果翻译后与翻译前一致，则不存储
            if (StringUtils.equals(originWord, transWord) || !ChineseCharacterUtils.containsChinese(originWord)
                || StringUtils.isBlank(transWord)) {
                continue;
            }

            if (StringUtils.isNotBlank(promptDictService.queryValueByKey(originWord))) {
                continue;
            }

            //存储翻译后的prompt
            PromptDictVO dict = new PromptDictVO();
            dict.setWord(originWord);
            dict.setPrompt(transWord);
            dict.setType(DictTypeEnum.CLOTH_COLLOCATION);
            dict.setTags(Arrays.asList(DictTagsEnum.CUSTOM.getCode(), tags.getCode()));
            dict.setUserId(OperationContextHolder.getMasterUserId());
            dict.setOperatorId(OperationContextHolder.getOperatorUserId());
            target.add(dict);
        }
    }

}
