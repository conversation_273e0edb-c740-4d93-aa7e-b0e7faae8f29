package ai.conrain.aigc.platform.service.helper;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;

import javax.imageio.ImageIO;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;

/**
 * ComfyUi帮助类
 */
@Slf4j
@Component
public class ComfyUIHelper {


    @Autowired
    private OssService ossService;
    @Autowired
    protected FileDispatch fileDispatch;

    /**
     * 上传文件到 comfyUI input下
     * @param image 图片oss地址
     * @return 图片comfyUI地址
     */
    public String upLoadImage(String image) throws IOException {
        // 获取全路径
        String url = CommonUtil.getFilePathAndNameFromURL(image);
        // 获取原始文件名称
        String originImageName = CommonUtil.getFileNameWithoutExtension(url);
        // 创建临时目录
        String tmpUrl = ossService.downloadFile(url, "/tmp/", originImageName);
        // 读取图片
        BufferedImage originalImage = ImageIO.read(new File(tmpUrl));

        //2.上传到comfyui的input下
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(originalImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = originImageName + ".png";

        // 获取当前用户ID，若 ID 为空则默认为系统操作用户
        Integer userId = OperationContextHolder.getMasterUserId() == null ? CommonUtil.mockSystemContext().getCurrentUserId() : OperationContextHolder.getMasterUserId();

        // 拼装 comfyUI 地址
        String comfyUIPath = ComfyUIUtils.buildInputPath(userId) + imageName;

        // 上传到comfyUI的input下
        fileDispatch.uploadFile(comfyUIPath, new ByteArrayInputStream(imageBytes),
                userId);


        // 返回结果
        return comfyUIPath;
    }

    /**
     * 上传文件到 comfyUI input下, 加 md5 校验
     * @param image 图片oss地址
     * @return 图片comfyUI地址
     */
    public String upLoadImage(String image, CreativeTaskVO task) throws IOException {
        // 获取全路径
        String url = CommonUtil.getFilePathAndNameFromURL(image);
        // 获取原始文件名称
        String originImageName = CommonUtil.getFileNameWithoutExtension(url);
        // 创建临时目录
        String tmpUrl = ossService.downloadFile(url, "/tmp/", originImageName);
        // 读取图片
        BufferedImage originalImage = ImageIO.read(new File(tmpUrl));

        //2.上传到comfyui的input下
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(originalImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = originImageName + ".png";

        // 拼装 comfyUI 地址
        String comfyUIPath = ComfyUIUtils.buildInputPath(task.getUserId()) + imageName;

        // 计算 md5
        String md5 = CommonUtil.calculateMD5(imageBytes);

        // 上传到comfyUI的input下
        fileDispatch.uploadFile(comfyUIPath, new ByteArrayInputStream(imageBytes), image, task.getUserId(), false, null, md5);


        // 返回结果
        return comfyUIPath;
    }

    /**
     * 从网络url上传文件到 comfyUI input下
     * @param imageUrl 图片url
     * @return 图片comfyUI地址
     */
    public String upLoadImageFromNetworkUrl(String imageUrl) throws IOException {
        // 1. 从 url 下载图片
        URL url = new URL(imageUrl);
        BufferedImage originalImage = ImageIO.read(url);

        // 获取原始文件名称
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);

        //2.上传到comfyui的input下
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(originalImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = originImageName + ".png";

        // 获取当前用户ID，若 ID 为空则默认为系统操作用户
        Integer userId = OperationContextHolder.getMasterUserId() == null ? CommonUtil.mockSystemContext().getCurrentUserId() : OperationContextHolder.getMasterUserId();

        // 拼装 comfyUI 地址
        String comfyUIPath = ComfyUIUtils.buildInputPath(userId) + imageName;

        // 上传到comfyUI的input下
        fileDispatch.uploadFile(comfyUIPath, new ByteArrayInputStream(imageBytes),
                userId);

        // 返回结果
        return comfyUIPath;
    }
}
