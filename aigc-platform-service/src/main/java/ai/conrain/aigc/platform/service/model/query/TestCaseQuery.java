package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TestCaseQuery
 *
 * @version TestCaseService.java v 0.1 2025-08-12 07:10:16
 */
@Data
public class TestCaseQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试用例名称 */
    private String name;

    /** 类型 */
    private String type;

    /** 用例数量 */
    private Integer caseNum;

    /** 上传用户id */
    private Integer userId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 扩展 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}