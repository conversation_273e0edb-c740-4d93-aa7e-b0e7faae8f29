//package ai.conrain.aigc.platform.service.component.creative;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Optional;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//
//import ai.conrain.aigc.platform.integration.ai.ComfyUIServiceImpl;
//import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
//import ai.conrain.aigc.platform.service.constants.CommonConstants;
//import ai.conrain.aigc.platform.service.constants.SystemConstants;
//import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
//import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
//import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
//import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
//import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
//import ai.conrain.aigc.platform.service.model.common.BizException;
//import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
//import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
//import ai.conrain.aigc.platform.service.model.request.BasicChangingClothesRequest;
//import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
//import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
//import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
//import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
//import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
//import ai.conrain.aigc.platform.service.util.ElementUtils;
//import ai.conrain.aigc.platform.service.util.FlowUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//@Slf4j
//@Service
//public class BasicChangingClothesCreativeService extends AbstractCreativeService<BasicChangingClothesRequest> {
//
//    @Autowired
//    private ComfyUIHelper comfyUIHelper;
//
//    @Autowired
//    private ComfyUIServiceImpl comfyUIServiceImpl;
//
//    @Autowired
//    private ServerHelper serverHelper;
//
//    @Value("${comfyui.output.path}")
//    private String comfyuiOutputPath;
//
//    @Override
//    public CreativeTypeEnum getCreativeType() {
//        return CreativeTypeEnum.BASIC_CHANGING_CLOTHES;
//    }
//
//    @Override
//    protected CreativeBatchVO buildData(BasicChangingClothesRequest request, MaterialModelVO modelVO)
//            throws IOException {
//        log.info(
//                "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildData::开始构建CreativeBatchVO 批次数据...");
//
//        // 构建批次对象
//        CreativeBatchVO batch = CreativeBatchConverter.request2VO(request);
//
//        // 修正图片比例
//        String imageProportion = batch.getImageProportion();
//        if (StringUtils.isNotBlank(imageProportion)) {
//            ProportionTypeEnum proportion = ProportionTypeEnum.getByCode(imageProportion);
//            if (proportion != null && proportion.isLagerProportion() && modelVO != null
//                    && ModelVersionEnum.SDXL == modelVO.getVersion()) {
//                log.info("sdxl生成图片的尺寸有误{}，自动修正为{}", proportion, proportion.getSmallerProportion());
//                batch.setImageProportion(proportion.getSmallerProportion().getCode());
//            }
//        }
//
//        if (request.getReferenceInfoList().get(0).getReferenceConfig() == null) {
//            batch.setImageProportion("NONE");
//        }
//
//        // 用户服装图片 上传图片至 ComfyUI
//        String clotheImageUpLoadImageUrl = comfyUIHelper.upLoadImage(request.getClotheImage());
//        // 用户服装蒙层图片 上传图片至 ComfyUI
//        String maskImageUpLoadImageUrl = comfyUIHelper.upLoadImage(request.getMaskImage());
//
//        // 添加扩展字段
//        batch.addExtInfo(CommonConstants.CLOTHE_IMAGE, clotheImageUpLoadImageUrl);
//        batch.addExtInfo(CommonConstants.MASK_IMAGE, maskImageUpLoadImageUrl);
//
//        // 添加抠图 prompt
//        batch.addExtInfo(CommonConstants.PICTURE_MATTING_PROMPT, getPictureMattingPrompt(request));
//
//        // 添加是否需要替换人脸
//        batch.addExtInfo(CommonConstants.IS_NEED_REPLACE_FACE,
//                request.getConfigs() != null && !request.getConfigs().isEmpty());
//
//        log.info(
//                "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildData::CreativeBatchVO 批次数据构建完成:{}",
//                batch);
//
//        // 返回结果
//        return batch;
//    }
//
//    @Override
//    protected CreativeElementVO fetchElementById(Integer id, BasicChangingClothesRequest request,
//                                                 MaterialModelVO modelVO) {
//        CreativeElementVO element = super.fetchElementById(id, request, modelVO);
//
//        //0.风格lora走随机流程,所以这里返回父节点
//        if (ElementUtils.isStyleScene(element)) {
//            log.info("命中风格lora，直接返回当前二级节点");
//            return element;
//        }
//
//        return element.getChildren().get(0);
//    }
//
//    @Override
//    protected List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements,
//                                              BasicChangingClothesRequest request) {
//        log.info(
//                "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据...");
//        // 构建任务数据
//        List<CreativeTaskVO> result = new ArrayList<>();
//
//        request.getReferenceInfoList().forEach(referenceInfo -> {
//            // 构建任务信息
//            CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);
//
//            String referenceImageUrl = StringUtils.EMPTY;
//
//            try {
//                referenceImageUrl = comfyUIHelper.upLoadImage(referenceInfo.getImageUrl());
//            } catch (IOException e) {
//                log.error(
//                        "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildTasks::图片上传至 ComfyUI 时出现错误::{}",
//                        e.getMessage());
//
//                throw new BizException(e.getMessage());
//            }
//
//            // 设置任务出图数量
//            target.setBatchCnt(4);
//            // 添加 衣服图片
//            target.addExtInfo(CommonConstants.CLOTHE_IMAGE, batch.getStringFromExtInfo(CommonConstants.CLOTHE_IMAGE));
//            // 添加 遮罩图片
//            target.addExtInfo(CommonConstants.MASK_IMAGE, batch.getStringFromExtInfo(CommonConstants.MASK_IMAGE));
//            // 添加 衣服类型
//            target.addExtInfo(CommonConstants.CLOTHE_TYPE, batch.getStringFromExtInfo(CommonConstants.CLOTHE_TYPE));
//            // 添加 参考图图片
//            target.addExtInfo(CommonConstants.REFERENCE_IMAGE, referenceImageUrl);
//            // 添加参考图原图片
//            target.addExtInfo(CommonConstants.REFERENCE_ORIGINAL_IMAGE, referenceInfo.getImageUrl());
//
//            // 生成随机 seed
//            target.addExtInfo(CommonConstants.KEY_SEED, String.valueOf(System.currentTimeMillis()));
//            // 是否需要替换人脸
//            target.addExtInfo(CommonConstants.IS_NEED_REPLACE_FACE,
//                    batch.getStringFromExtInfo(CommonConstants.IS_NEED_REPLACE_FACE));
//            // 是否是用户上传的参考图
//            target.addExtInfo(CommonConstants.IS_USER_UPLOAD_REFERENCE,
//                    !Objects.nonNull(referenceInfo.getReferenceConfig()));
//
//            // 添加风格lora配置
//            if (Objects.nonNull(referenceInfo.getReferenceConfig())) {
//                target.addExtInfo(CommonConstants.STYLE_LORA_CONFIG, referenceInfo.getReferenceConfig());
//
//                // 提取服装信息（若为clotheType为上装，则删除下装相关信息，若为下装，则删除上装相关信息）
//                JSONObject styleOutfit = referenceInfo.getReferenceConfig().getJSONObject("styleOutfit");
//                if (Objects.nonNull(styleOutfit)) {
//                    String clotheType = batch.getStringFromExtInfo(CommonConstants.CLOTHE_TYPE);
//                    JSONObject filteredStyleOutfit = new JSONObject();
//
//                    // 定义上装和下装的关键字
//                    String[] upperKeys = {"outerwear", "innerwear"};
//                    String[] lowerKeys = {"bottoms", "shoes"};
//
//                    // 遍历所有属性
//                    for (String key : styleOutfit.keySet()) {
//                        boolean isUpperKey = false;
//                        boolean isLowerKey = false;
//
//                        // 检查是否是上装相关属性
//                        for (String upperKey : upperKeys) {
//                            if (key.equals(upperKey)) {
//                                isUpperKey = true;
//                                break;
//                            }
//                        }
//
//                        // 检查是否是下装相关属性
//                        for (String lowerKey : lowerKeys) {
//                            if (key.equals(lowerKey)) {
//                                isLowerKey = true;
//                                break;
//                            }
//                        }
//
//                        // 根据clotheType决定是否保留该属性
//                        if (clotheType.contains("upper")) {
//                            // 上装：保留下装属性和其他属性，删除上装属性
//                            if (isLowerKey || !isUpperKey) {
//                                filteredStyleOutfit.put(key, styleOutfit.get(key));
//                            }
//                        } else if (clotheType.contains("lower")) {
//                            // 下装：保留上装属性和其他属性，删除下装属性
//                            if (isUpperKey || !isLowerKey) {
//                                filteredStyleOutfit.put(key, styleOutfit.get(key));
//                            }
//                        } else {
//                            // 其他情况保留所有属性
//                            filteredStyleOutfit.put(key, styleOutfit.get(key));
//                        }
//                    }
//
//                    // 将过滤后的服装信息添加到扩展信息中
//                    target.addExtInfo(CommonConstants.STYLE_OUTFIT_INFO, JSON.toJSONString(filteredStyleOutfit));
//                }
//            }
//
//            // 设置背景标签
//            target.addExtInfo(CommonConstants.BACK_TAGS, referenceInfo.getBackTags());
//            // 设置扩展标签
//            target.addExtInfo(CommonConstants.EXT_TAGS, referenceInfo.getBackExtTags());
//
//            // 设置抠图prompt
//            target.addExtInfo(CommonConstants.PICTURE_MATTING_PROMPT,
//                    batch.getStringFromExtInfo(CommonConstants.PICTURE_MATTING_PROMPT));
//
//            // 插入任务信息
//            CreativeTaskVO data = creativeTaskService.insert(target);
//
//            // 添加至集合中
//            result.add(data);
//        });
//
//        log.info(
//                "[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据构建完成,共：{}个任务",
//                result.size());
//
//        // 构建任务数据
//        return result;
//    }
//
//    @Override
//    protected Map<Integer, List<Integer>> getConfigs(BasicChangingClothesRequest request) {
//        return request.getConfigs();
//    }
//
//    @Override
//    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
//                            Map<String, Object> context) {
//
//        // 添加人脸节点
//        if (CollectionUtils.isNotEmpty(elements)) {
//            CreativeElementVO faceElement = elements.stream().filter(
//                    element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())).findFirst().orElse(null);
//
//            // 添加人脸节点
//            context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
//
//            // 安全获取 swapType
//            String swapType = Optional.ofNullable(faceElement)
//                    .map(element -> element.getExtInfo("swapType", String.class))
//                    .orElse("");
//
//            // 判断是否为 LORA 类型
//            boolean isLoraFace = StringUtils.equals("LORA", swapType);
//            context.put(CommonConstants.IS_USE_LORA_FACE, isLoraFace);
//
//            // 判断是否为 LORA 换脸（普通换脸情况下：isUseFacePic为true  lora 换脸：true为带照片，false 为不带照片）
//            boolean isLoraSwapFace;
//            if (isLoraFace) {
//                String loraSwapFace = Optional.ofNullable(faceElement)
//                        .map(element -> element.getExtInfo("loraSwapFace", String.class))
//                        .orElse("");
//                isLoraSwapFace = StringUtils.equals("Y", loraSwapFace);
//            } else {
//                isLoraSwapFace = true;
//            }
//            context.put(CommonConstants.IS_USE_FACE_PIC, isLoraSwapFace);
//
//        }
//
//        // 添加图片信息
//        context.put(CommonConstants.CLOTHE_IMAGE, task.getStringFromExtInfo(CommonConstants.CLOTHE_IMAGE));
//        // 添加遮罩图片
//        context.put(CommonConstants.MASK_IMAGE, task.getStringFromExtInfo(CommonConstants.MASK_IMAGE));
//        // 添加衣服类型
//        context.put(CommonConstants.CLOTHE_TYPE, task.getStringFromExtInfo(CommonConstants.CLOTHE_TYPE));
//        // 参考图
//        context.put(CommonConstants.REFERENCE_IMAGE, task.getStringFromExtInfo(CommonConstants.REFERENCE_IMAGE));
//        // 参考图描述信息
//        context.put(CommonConstants.STYLE_LORA_CONFIG,
//                JSONObject.parseObject(task.getStringFromExtInfo(CommonConstants.STYLE_LORA_CONFIG)));
//        // 添加背景标签
//        context.put(CommonConstants.BACK_TAGS, task.getStringFromExtInfo(CommonConstants.BACK_TAGS));
//        // 添加扩展标签
//        context.put(CommonConstants.EXT_TAGS, task.getStringFromExtInfo(CommonConstants.EXT_TAGS));
//        context.put(CommonConstants.PICTURE_MATTING_PROMPT,
//                task.getStringFromExtInfo(CommonConstants.PICTURE_MATTING_PROMPT));
//        // 是否需要替换人脸
//        context.put(CommonConstants.IS_NEED_REPLACE_FACE,
//                task.getExtInfo(CommonConstants.IS_NEED_REPLACE_FACE, Boolean.class));
//        // 是否是用户上传的参考图
//        context.put(CommonConstants.IS_USER_UPLOAD_REFERENCE,
//                task.getExtValue(CommonConstants.IS_USER_UPLOAD_REFERENCE, Boolean.class));
//        // 设置服装信息
//        context.put(CommonConstants.STYLE_OUTFIT_INFO, task.getStringFromExtInfo(CommonConstants.IS_USER_UPLOAD_REFERENCE));
//    }
//
//    @Override
//    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
//                                Map<String, Object> context) {
//        // 是否需要替换人脸
//        CreativeElementVO faceElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());
//        boolean isSwapFace = Objects.nonNull(faceElement);
//
//        // 是否是用户上传的参考图
//        boolean isUserUploadReference = Boolean.parseBoolean(
//                task.getStringFromExtInfo(CommonConstants.IS_USER_UPLOAD_REFERENCE));
//
//        // 若 用户上传参考图 或 不换头 均走以下流程
//        if (!isSwapFace || isUserUploadReference) {
//            log.info("[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::getFlowKey::执行用户上传参考图流程,taskId:{}", task.getId());
//            return SystemConstants.USER_UPLOAD_REFERENCE_FLOW_PARAMS;
//        }
//
//        // 若换头且为系统Lora参考图 走以下流程
//        log.info("[ComfyUI流程][基础款换衣]BasicChangingClothesCreativeService::getFlowKey::执行系统Lora参考图流程,taskId:{}", task.getId());
//        return SystemConstants.SYSTEM_LORA_REFERENCE_FLOW_PARAMS;
//    }
//
//    @Override
//    protected String postParse(String prompt) {
//        prompt = prompt.replaceAll("\r\n|\r|\n", "");
//        prompt = prompt.replaceAll("\\\\n", "\\\\n");
//        return prompt;
//    }
//
//    @Override
//    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
//        return FlowUtils.correctFlow(flow, context);
//    }
//
//    /**
//     * 获取图片抠图prompt
//     *
//     * @param request 请求体
//     * @return 抠图prompt
//     */
//    private String getPictureMattingPrompt(BasicChangingClothesRequest request) {
//
//        // 获取图片抠图prompt
//        if (Objects.nonNull(request.getMattingId())) {
//
//            // 查询抠图任务信息
//            List<CreativeTaskVO> creativeTaskVOS = creativeTaskService.queryTaskByBatchId(request.getMattingId());
//
//            if (CollectionUtils.isNotEmpty(creativeTaskVOS)) {
//                // 抠图任务只会有一个，此处取第一个
//                CreativeTaskVO creativeTaskVO = creativeTaskVOS.get(0);
//
//                // 获取文件服务器地址
//                String fileServerUrl = serverHelper.getFileServerUrlByTask(creativeTaskVO);
//
//                // 获取图片抠图prompt
//                return comfyUIServiceImpl.fetchFileContent(comfyuiOutputPath + creativeTaskVO.getResultPath(),
//                        ComfyUIUtils.buildFileNamePrefix(creativeTaskVO.getId()), "txt", fileServerUrl);
//            }
//        }
//
//        return null;
//    }
//
//}