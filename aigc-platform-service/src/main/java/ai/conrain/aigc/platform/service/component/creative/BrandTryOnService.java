package ai.conrain.aigc.platform.service.component.creative;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.request.BrandTryOnRequest;
import ai.conrain.aigc.platform.service.model.request.ImageMaskModel;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonFlowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class BrandTryOnService extends AbstractCreativeService<BrandTryOnRequest> {

    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    protected CreativeBatchVO buildData(BrandTryOnRequest request, MaterialModelVO modelVO) throws IOException {
        AssertUtil.assertNotEmpty(request.getReferenceImages(), "上传参考图为空");
        List<ImageMaskModel> referenceImages = request.getReferenceImages();
        CreativeBatchVO batch = CreativeBatchConverter.request2VO(request);
        List<String> images = new ArrayList<>();
        images.add(request.getClothImage());
        images.add(request.getClothMaskUrl());
        referenceImages.forEach(item -> {
            images.add(item.getImageUrl());
            images.add(item.getMaskUrl());
        });
        // 并行上传
        Map<String, String> uploadResult = batchFillHelper.uploadImagesParallel(images);
        // 尝试获取原始task, 并获取原始图片
        // 服装图原图
        batch.addExtInfo(KEY_ORIGIN_IMAGE, request.getClothImage());
        batch.addExtInfo(KEY_CLOTH_ORIGIN_IMAGE, request.getClothImage());
        batch.addExtInfo(KEY_CLOTH_IMAGE_COMFY_UI, uploadResult.get(request.getClothImage()));
        // 服装图蒙版
        batch.addExtInfo(KEY_CLOTH_IMAGE_MASK, request.getClothMaskUrl());
        batch.addExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI, uploadResult.get(request.getClothMaskUrl()));
        // 参考图以及蒙版
        referenceImages.forEach(item -> {
            item.setImageComfyUI(uploadResult.get(item.getImageUrl()));
            item.setMaskComfyUI(uploadResult.get(item.getMaskUrl()));
        });
        batch.addExtInfo(KEY_REFERENCE_IMAGES, referenceImages);
        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        List<?> referenceImages = batch.getExtInfo(KEY_REFERENCE_IMAGES, List.class);
        AssertUtil.assertNotEmpty(referenceImages, ResultCode.BIZ_FAIL, "参考图为空");
        //        target.addExtInfo(KEY_GROW_MASK, getGrowMask(idx));
        //        target.addExtInfo(KEY_CFG, getCFG(idx));
        ImageMaskModel imageMaskModel = null;
        // 根据索引获取对应的参考图片
        Object reference = referenceImages.get(idx);
        if (reference instanceof JSONObject) {
            imageMaskModel = JSONObject.toJavaObject((JSONObject)reference, ImageMaskModel.class);
        }else {
            imageMaskModel = (ImageMaskModel)reference;
        }
        AssertUtil.assertNotNull(imageMaskModel, ResultCode.BIZ_FAIL, "参考图为空");
        // 单独存一下参考图
        target.addExtInfo(REFERENCE_ORIGINAL_IMAGE, imageMaskModel.getImageUrl());
        // 参考图
        target.addExtInfo(KEY_ORIGIN_IMAGE, imageMaskModel.getImageUrl());
        target.addExtInfo(KEY_ORIGIN_IMAGE_COMFY_UI, imageMaskModel.getImageComfyUI());
        // 参考图蒙版
        target.addExtInfo(KEY_ORIGIN_IMAGE_MASK, imageMaskModel.getMaskUrl());
        target.addExtInfo(KEY_ORIGIN_IMAGE_MASK_COMFY_UI, imageMaskModel.getMaskComfyUI());
        // 服装图
        target.addExtInfo(KEY_CLOTH_ORIGIN_IMAGE, batch.getExtInfo(KEY_CLOTH_ORIGIN_IMAGE));
        target.addExtInfo(KEY_CLOTH_IMAGE_COMFY_UI, batch.getExtInfo(KEY_CLOTH_IMAGE_COMFY_UI));
        // 服装图蒙版
        target.addExtInfo(KEY_CLOTH_IMAGE_MASK, batch.getExtInfo(KEY_CLOTH_IMAGE_MASK));
        target.addExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI, batch.getExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI));
        target.setBatchCnt(4);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put(KEY_ORIGIN_IMAGE_COMFY_UI, task.getExtInfo(KEY_ORIGIN_IMAGE_COMFY_UI));
        context.put(KEY_ORIGIN_IMAGE_MASK_COMFY_UI, task.getExtInfo(KEY_ORIGIN_IMAGE_MASK_COMFY_UI));
        context.put(KEY_CLOTH_IMAGE_COMFY_UI, task.getExtInfo(KEY_CLOTH_IMAGE_COMFY_UI));
        context.put(KEY_CLOTH_IMAGE_MASK_COMFY_UI, task.getExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI));
        context.put(KEY_GROW_MASK, task.getStringFromExtInfo(KEY_GROW_MASK));
        context.put(KEY_CFG, task.getStringFromExtInfo(KEY_CFG));
    }

    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        return CommonFlowUtil.correctNumber(flow, context);
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.BRAND_TRY_ON;
    }

}
