package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.query.TestItemQuery;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import ai.conrain.aigc.platform.service.model.vo.TestItemVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * AB测试项目 Service定义
 *
 * <AUTHOR>
 * @version TestItemService.java v 0.1 2024-12-19 01:24:06
 */
public interface TestItemService {

    /**
     * 查询AB测试项目对象
     *
     * @param id 主键
     * @return 返回结果
     */
    TestItemVO selectById(Integer id);

    /**
     * 删除AB测试项目对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加AB测试项目对象
     *
     * @param testItem 对象参数
     * @return 返回结果
     */
    TestItemVO insert(TestItemVO testItem);

    /**
     * 修改AB测试项目对象
     *
     * @param testItem 对象参数
     */
    void updateByIdSelective(TestItemVO testItem);

    /**
     * 带条件批量查询AB测试项目列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<TestItemVO> queryTestItemList(TestItemQuery query);

    /**
     * 带条件查询AB测试项目数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryTestItemCount(TestItemQuery query);

    /**
     * 带条件分页查询AB测试项目
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<TestItemVO> queryTestItemByPage(TestItemQuery query);

    /**
     * 创建测试项目
     *
     * @param planId     计划id
     * @param type       类型
     * @param items      测试项目列表
     * @param testParams
     */
    void create(Integer planId, TestTypeEnum type, List<TestItemVO> items, List<CreateTestParams> testParams);

    /**
     * 根据计划id查询测试项目
     *
     * @param planId 计划id
     * @return 测试项目列表
     */
    List<TestItemVO> queryByPlanId(Integer planId);
}