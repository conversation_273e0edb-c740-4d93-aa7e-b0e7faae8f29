/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.train;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserFavorService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.LoraActivateKeys;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ElementStatusEnum;
import ai.conrain.aigc.platform.service.enums.FavorTypeEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.request.AddCommonMaterialRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserFavorVO;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NOSHOW_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCENE_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.MASK_LOSS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.SCENE_MASK_LOSS_EXT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.SwitchConstants.SCENE_MASK_LOSS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;

/**
 * 通用lora训练服务
 *
 * <AUTHOR>
 * @version : CommonLoraTrainService.java, v 0.1 2025/2/24 10:05 renxiao.wu Exp $
 */
@Slf4j
@Service
public class CommonLoraTrainService extends AbstractLoraTrainService<AddCommonMaterialRequest> {
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private UserFavorService userFavorService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    protected boolean isExclusive(AddCommonMaterialRequest request) {
        return request.isExclusive();
    }

    @Override
    protected boolean needConsumePoint(MaterialModelVO materialModel) {
        return RoleTypeEnum.MERCHANT.equals(OperationContextHolder.getRoleType());
    }

    @Override
    protected String getShowImage(AddCommonMaterialRequest request) {
        return request.getMaterialDetail().getImgUrls().get(0);
    }

    @Override
    protected void fillOtherTrainDetail(AddCommonMaterialRequest request, LoraTrainDetail trainDetail,
                                        MaterialModelVO model) {
        MaterialType materialType = MaterialType.valueOf(request.getMaterialType());

        String captionPrompt = request.getCaptionPrompt();

        LabelTypeEnum labelType = LabelTypeEnum.getByCode(model.getExtInfo(KEY_LABEL_TYPE, String.class));
        //极简打标清空激活词
        if (labelType == LabelTypeEnum.MINI && materialType == MaterialType.face) {
            trainDetail.setActivateKey("");
        } else {
            trainDetail.setActivateKey(LoraActivateKeys.getActivateKey(request.getMaterialType(), false));
            if (StringUtils.isBlank(captionPrompt)) {
                captionPrompt = systemConfigService.queryValueByKey(SystemConstants.TRAIN_LABEL_SCENE_PROMPT);
            }
        }

        if (materialType == MaterialType.scene) {
            JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
            float flag = switchJson.getFloatValue(SCENE_MASK_LOSS);
            if (GrayscaleTestUtils.isHit(flag, "场景训练-使用mask+loss方案")) {
                model.addExtInfo(MASK_LOSS, YES);
                trainDetail.setTrainExtInfo(SCENE_MASK_LOSS_EXT);
                trainDetail.setPreprocessCensoredFace(NO);
            }
        }

        captionPrompt = CommonUtil.unescapeLineBreak(captionPrompt);
        captionPrompt = ComfyUIUtils.parseParams(captionPrompt);

        trainDetail.setCaptionPrompt(captionPrompt);
        trainDetail.setWaterMarkDesc(CommonUtil.unescapeLineBreak(request.getWaterMarkDesc()));
        trainDetail.setNoshowFace(request.getNoshowFace());
        MaterialModelUtils.initAppendInfo(trainDetail);

        trainDetail.setImageSize("1024");

        //----------------------------------白头打标3要素 start----------------------------------
        trainDetail.setPreprocessCensoredFace(request.getPreprocessCensoredFace());

        //场景去水印（走的抠图流程）源目录
        MaterialModelUtils.fillCutoutSourceDir(trainDetail);

        //打标去水印（走的预处理流程）源目录
        MaterialModelUtils.fillLabelSourceDir(trainDetail);
        //----------------------------------白头打标3要素 end------------------------------------

        Integer repeatTimes = request.getTrainRepeatTimes();
        if (repeatTimes == null) {
            repeatTimes = materialType == MaterialType.face ? 15 : 5;
        }
        trainDetail.setRepeatTimes(repeatTimes);

        if (StringUtils.isNotBlank(request.getSceneType())) {
            model.addExtInfo(KEY_SCENE_TYPE, request.getSceneType());
        }
    }

    @Override
    protected void fillMaterialModelExt(JSONObject ext, AddCommonMaterialRequest request, Integer userId) {
        LabelTypeEnum labelType = LabelTypeEnum.getByCode(request.getLabelType());
        labelType = labelType == null ? (StringUtils.equals(request.getMaterialType(), MaterialType.face.name())
            ? LabelTypeEnum.MINI : LabelTypeEnum.DEFAULT) : labelType;
        ext.put(KEY_LABEL_TYPE, labelType.getCode());
    }

    /**
     * 填充素材信息扩展信息
     *
     * @param extInfo 扩展信息
     * @param request 请求
     */
    @Override
    protected void fillMaterialInfoExt(JSONObject extInfo, AddCommonMaterialRequest request) {
        extInfo.put(CommonConstants.KEY_EXCLUSIVE, request.isExclusive());
        extInfo.put(CommonConstants.HAS_WATERMARK, StringUtils.isNotBlank(request.getWaterMarkDesc()));
    }

    @Override
    protected void postProcess(MaterialModelVO materialModel, AddCommonMaterialRequest request) {
        CreativeElementVO ele = createElementConfig(materialModel, request);

        materialModel.addExtInfo(CommonConstants.CREATIVE_ELEMENT_ID, ele.getId());
        materialModelService.updateByIdSelective(materialModel);

        //自动添加到用户收藏
        if (StringUtils.equals(request.getMaterialType(), MaterialType.scene.name())) {
            UserFavorVO f = new UserFavorVO();
            f.setType(FavorTypeEnum.ELEMENT);
            f.setItemId(ele.getId());
            f.setModelId(materialModel.getId());
            f.setUserId(OperationContextHolder.getMasterUserId());
            f.setOperatorId(OperationContextHolder.getOperatorUserId());

            userFavorService.insert(f);
        }
    }

    /**
     * 创建创作元素
     *
     * @param model lora模型
     * @param req   请求参数
     * @return 创作元素
     */
    private CreativeElementVO createElementConfig(MaterialModelVO model, AddCommonMaterialRequest req) {
        CreativeElementVO e = new CreativeElementVO();
        e.setName(model.getName());
        e.addExtInfo(CommonConstants.KEY_OPEN_SCOPE,
            req.isExclusive() ? OperationContextHolder.getMasterUserId().toString() : CommonConstants.ALL);

        if (StringUtils.equals(req.getMaterialType(), MaterialType.face.name())) {
            e.setConfigKey(ElementConfigKeyEnum.FACE.name());

            //男女款式
            if (StringUtils.equals(req.getMaterialSubType(), "male")) {
                e.setType(new ArrayList<>(Collections.singletonList("male-model")));
            } else if (StringUtils.equals(req.getMaterialSubType(), "female")) {
                e.setType(new ArrayList<>(Collections.singletonList("female-model")));
            }

            e.addExtInfo(CommonConstants.KEY_SWAP_TYPE, "LORA");

        } else if (StringUtils.equals(req.getMaterialType(), MaterialType.scene.name())) {
            e.setConfigKey(ElementConfigKeyEnum.SCENE.name());

            //男女款式
            if (StringUtils.equals(req.getMaterialSubType(), "male")) {
                e.setType(new ArrayList<>(Collections.singletonList("Male")));
            } else if (StringUtils.equals(req.getMaterialSubType(), "female")) {
                e.setType(new ArrayList<>(Collections.singletonList("Female")));
            } else if (StringUtils.equals(req.getMaterialSubType(), "unisex")) {
                e.setType(new ArrayList<>(Arrays.asList("Male", "Female")));
            }
            e.addExtInfo(CommonConstants.KEY_IS_LORA, "Y");

            if (StringUtils.isNotBlank(req.getNoshowFace())) {
                e.addExtInfo(KEY_NOSHOW_FACE, req.getNoshowFace());
            }
            if (CollectionUtils.isNotEmpty(req.getClothCategory())) {
                e.addExtInfo(KEY_CLOTH_CATEGORY, req.getClothCategory());
            }
        } else {
            throw new IllegalArgumentException("invalid material type value:" + req.getMaterialType());
        }

        // 若有年龄范围，则拼接在type后面
        if (StringUtils.isNotBlank(req.getAgeRange())) {
            List<String> types = e.getType();
            if (types == null) {
                types = new ArrayList<>();
            }
            types.add(req.getAgeRange());
            e.setType(types);
        }

        e.setLevel(2);
        e.setStatus(ElementStatusEnum.TEST);
        e.setBelong(OperationContextHolder.isBackRole() ? ModelTypeEnum.SYSTEM : ModelTypeEnum.CUSTOM);
        e.setShowImage(req.getMaterialDetail().getImgUrls().get(0));
        e.setUserId(OperationContextHolder.getMasterUserId());
        e.setOperatorId(OperationContextHolder.getOperatorUserId());
        e.setOperatorNick(OperationContextHolder.getOperatorNick());
        e.setLoraModelId(model.getId());
        if (StringUtils.equals(model.getExtInfo(MASK_LOSS, String.class), YES)) {
            e.addExtInfo(MASK_LOSS, YES);
        }

        return creativeElementService.insert(e);
    }
}
