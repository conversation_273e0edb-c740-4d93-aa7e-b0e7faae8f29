package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 自动工程师分配服务
 */
@Slf4j
@Service
public class AutoEngineerAssignServiceImpl {

    @Autowired
    private UserPointService userPointService;
    
    @Autowired
    private UserProfileService userProfileService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private DistributorCustomerService distributorCustomerService;

    /**
     * 异步尝试自动为客户指派工程师
     * 逻辑：充值成功后，如果用户缪斯点达到阈值且未指派工程师，则根据其销售/渠道商的工程师进行自动指派
     * 
     * @param customerUserId 客户用户ID
     */
    @Async
    public void tryAutoAssignEngineerToCustomerAsync(Integer customerUserId) {
        try {
            log.info("开始异步自动指派工程师，客户ID: {}", customerUserId);
            
            // 1. 配置缪斯点阈值 (后续可配置化)
            BigDecimal threshold = new BigDecimal("3999.00");
            
            // 2. 检查客户当前缪斯点余额
            UserPointVO userPoint = userPointService.queryImagePoint(customerUserId);
            if (userPoint == null || userPoint.getImagePoint() == null) {
                log.info("客户{}缪斯点查询失败或为空，跳过自动指派工程师", customerUserId);
                return;
            }
            
            BigDecimal currentMusePoints = userPoint.getImagePoint();
            log.info("客户{}当前缪斯点: {}, 阈值: {}", customerUserId, currentMusePoints, threshold);
            
            // 3. 检查是否达到阈值
            if (currentMusePoints.compareTo(threshold) < 0) {
                log.info("客户{}缪斯点{}未达到阈值{}，跳过自动指派工程师", 
                    customerUserId, currentMusePoints, threshold);
                return;
            }
            
            // 4. 检查客户是否已经指派了工程师
            UserProfileVO existingEngineer = userProfileService.selectByUidAndProfileKey(
                customerUserId, CommonConstants.KEY_PROMPT_USER_ID);
            if (existingEngineer != null && StringUtils.isNotBlank(existingEngineer.getProfileVal())) {
                log.info("客户{}已指派工程师ID: {}，跳过自动指派", 
                    customerUserId, existingEngineer.getProfileVal());
                return;
            }
            
            // 5. 查询客户关联的销售/渠道商 - 直接查询distributor_customer表
            UserVO customer = userService.selectById(customerUserId);
            if (customer == null) {
                log.warn("客户{}信息查询失败，跳过自动指派工程师", customerUserId);
                return;
            }
            
            // 直接查询distributor_customer表获取销售ID
            DistributorCustomerQuery query = new DistributorCustomerQuery();
            query.setCustomerMasterUserId(customerUserId);
            List<DistributorCustomerVO> relations = distributorCustomerService.queryDistributorCustomerList(query);
            
            if (CollectionUtils.isEmpty(relations)) {
                log.info("客户{}未关联销售/渠道商，跳过自动指派工程师", customerUserId);
                return;
            }
            
            Integer distributorUserId = relations.get(0).getDistributorSalesUserId();
            if (distributorUserId == null) {
                log.info("客户{}关联的销售用户ID为空，跳过自动指派工程师", customerUserId);
                return;
            }
            
            log.info("客户{}关联的销售/渠道商ID: {}", customerUserId, distributorUserId);
            
            // 6. 查询销售/渠道商指派的工程师
            UserProfileVO distributorEngineer = userProfileService.selectByUidAndProfileKey(
                distributorUserId, CommonConstants.KEY_PROMPT_USER_ID);
            if (distributorEngineer == null || StringUtils.isBlank(distributorEngineer.getProfileVal())) {
                log.info("销售/渠道商{}未指派工程师，跳过为客户{}自动指派工程师", 
                    distributorUserId, customerUserId);
                return;
            }
            
            String engineerId = distributorEngineer.getProfileVal();
            log.info("销售/渠道商{}的工程师ID: {}", distributorUserId, engineerId);
            
            // 7. 自动为客户指派工程师
            UserProfileVO customerProfile = new UserProfileVO();
            customerProfile.setUid(customerUserId);
            customerProfile.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
            customerProfile.setProfileVal(engineerId);
            
            UserProfileVO result = userProfileService.insertOrUpdate(customerProfile);
            boolean success = result != null;
            
            if (success) {
                log.info("异步自动指派工程师成功: 客户ID={}, 工程师ID={}", customerUserId, engineerId);
            } else {
                log.error("异步自动指派工程师失败: 客户ID={}, 工程师ID={}", customerUserId, engineerId);
            }
            
        } catch (Exception e) {
            log.error("异步自动指派工程师处理异常，客户ID: " + customerUserId, e);
        }
    }
}