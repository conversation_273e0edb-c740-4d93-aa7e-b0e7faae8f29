package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.example.ImageRecordExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageDAO;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageRecordDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO;
import ai.conrain.aigc.platform.service.component.ImageRecordService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageRecordAggVO;
import ai.conrain.aigc.platform.service.model.query.ImageRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageNavigationVO;
import ai.conrain.aigc.platform.service.model.vo.ImageRecordVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.alibaba.fastjson2.JSON;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ImageRecordServiceImpl implements ImageRecordService {
    private static final String STYLE_TYPE = "style";

    @Autowired
    private ImageRecordDAO imageRecordDAO;
    @Autowired
    private ImageDAO imageDAO;
    @Autowired
    private ImageGroupDAO imageGroupDAO;

    /**
     * 带条件分页查询图像基本信息
     */
    @Override
    public PageInfo<ImageRecordVO> queryImageByPage(ImageRecordQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:"
            + query.getPageNum() + ",pageSize:" + query.getPageSize());

        ImageRecordExample example = convertToImageRecordQuery(query);
        long total;
        List<ImageRecordDO> listDO;

        if (STYLE_TYPE.equals(query.getType())) {
            total = imageRecordDAO.countImageGroupPageable(example);
            listDO = imageRecordDAO.findImageGroupPageable(example);
        } else {
            total = imageRecordDAO.countPageable(example);
            listDO = imageRecordDAO.findPageable(example);
        }

        List<ImageRecordVO> list = listDO.stream().map(imageDO -> convertToImageRecord(imageDO, query)).collect(Collectors.toList());
        PageInfo<ImageRecordVO> page = new PageInfo<>();
        page.setList(list);
        page.setSize(list.size());
        page.setTotalCount(total);
        return page;
    }

    /**
     * 查询图像的导航信息
     */
    @Override
    public ImageNavigationVO queryNavigation(ImageRecordQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        AssertUtil.assertNotNull(query.getCursor(), ResultCode.PARAM_INVALID, "query.cursor is null");

        ImageRecordExample example = convertToImageRecordQuery(query);
        example.setPageNum(1);
        example.setPageSize(1);

        List<ImageRecordDO> prevList;
        List<ImageRecordDO> nextList;

        if (STYLE_TYPE.equals(query.getType())) {
            example.setDirection("prev");
            prevList = imageRecordDAO.findImageGroupPageable(example);
            example.setDirection("next");
            nextList = imageRecordDAO.findImageGroupPageable(example);
        } else {
            example.setDirection("prev");
            prevList = imageRecordDAO.findPageable(example);
            example.setDirection("next");
            nextList = imageRecordDAO.findPageable(example);
        }

        ImageNavigationVO navigation = new ImageNavigationVO();
        navigation.setType(query.getType());
        navigation.setPrevId(prevList.isEmpty() ? null : prevList.get(0).getId());
        navigation.setNextId(nextList.isEmpty() ? null : nextList.get(0).getId());
        return navigation;
    }

    @Override
    public List<String> queryImageTags(ImageRecordQuery query) {
        AssertUtil.assertNotBlank(query.getType(), "type is blank");
        if (STYLE_TYPE.equals(query.getType())) {
            return imageGroupDAO.selectAllTags();
        }
        ImageDO imageDO = new ImageDO();
        imageDO.setType(query.getType());
        return imageDAO.selectAllTags(imageDO);
    }

    private ImageRecordExample convertToImageRecordQuery(ImageRecordQuery query) {
        if (query == null) {
            return null;
        }
        ImageRecordExample example = new ImageRecordExample();
        example.setId(query.getId());
        example.setCursor(query.getCursor());
        example.setUserId(query.getUserId());
        example.setType(query.getType());
        example.setMetadata(query.getMetadata());
        example.setPaired(query.getPaired());
        example.setResult(query.getResult());
        example.setAgg(query.getAgg());
        example.setTags(query.getTags());
        example.setCreateTime(query.getCreateTime());
        example.setModifyTime(query.getModifyTime());
        example.setPageSize(query.getPageSize());
        example.setPageNum(query.getPageNum());
        example.setOrderBy(query.getOrderBy());
        return example;
    }

    private ImageRecordVO convertToImageRecord(ImageRecordDO image, ImageRecordQuery query) {
        ImageRecordVO imageRecordVO = new ImageRecordVO();
        imageRecordVO.setId(image.getId());
        imageRecordVO.setUrl(image.getUrl());
        imageRecordVO.setPairUrl(image.getPairUrl());
        imageRecordVO.setShowImgUrl(image.getShowImgUrl());
        imageRecordVO.setType(image.getType());
        imageRecordVO.setMetadata(JSON.parseObject(image.getMetadata()));
        imageRecordVO.setCreateTime(image.getCreateTime());
        imageRecordVO.setModifyTime(image.getModifyTime());
        imageRecordVO.setResult(JSON.parseArray(image.getResult()));
        imageRecordVO.setClothTypeDesc(image.getClothTypeDesc());
        imageRecordVO.setIntendedUse(image.getIntendedUse());

        // 处理 agg 字段，根据查询条件补齐缺失的 userId
        List<ImageRecordAggVO> aggList = new ArrayList<>();
        if (image.getAgg() != null) {
            aggList = JSON.parseArray(image.getAgg(), ImageRecordAggVO.class);
        }

        // 如果查询条件中有 agg.userId，检查是否需要补齐
        if (query != null && query.getAgg() != null && !query.getAgg().isEmpty()) {
            Set<Integer> existingUserIds = aggList.stream()
                .map(ImageRecordAggVO::getUserId)
                .collect(Collectors.toSet());

            for (ai.conrain.aigc.platform.dal.example.ImageRecordAggQuery aggQuery : query.getAgg()) {
                if (aggQuery.getUserId() != null && !existingUserIds.contains(aggQuery.getUserId())) {
                    // 补齐缺失的 userId，editTime 设为 null
                    ImageRecordAggVO missingAgg = new ImageRecordAggVO();
                    missingAgg.setUserId(aggQuery.getUserId());
                    missingAgg.setEditTime(null);
                    aggList.add(missingAgg);
                }
            }
        }
        aggList.sort(Comparator.comparing(ImageRecordAggVO::getUserId));
        imageRecordVO.setAgg(aggList);
        return imageRecordVO;
    }
}
