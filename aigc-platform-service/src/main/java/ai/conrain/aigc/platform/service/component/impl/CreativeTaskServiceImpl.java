package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.CreativeTaskDAO;
import ai.conrain.aigc.platform.dal.dao.ImageCaseDAO;
import ai.conrain.aigc.platform.dal.entity.CreativeTaskDO;
import ai.conrain.aigc.platform.dal.entity.ImageCaseTagDO;
import ai.conrain.aigc.platform.dal.example.CreativeTaskExample;
import ai.conrain.aigc.platform.dal.example.CreativeTaskExample.Criteria;
import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.model.PromptResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.integration.aliyun.ImageModerationService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.taobao.huiwa.HuiWaService;
import ai.conrain.aigc.platform.integration.wechat.model.ImageQualityVO;
import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.component.creative.CreativeServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.FileHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper.WeakType;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static ai.conrain.aigc.platform.service.constants.BizConstants.*;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.*;
import static ai.conrain.aigc.platform.service.util.ComfyUIUtils.buildClientId;

/**
 * CreativeTaskService实现
 */
@Slf4j
@Service
public class CreativeTaskServiceImpl implements CreativeTaskService {
    private static final String LOCK_KEY_PREFIX = "_sync_task_lock_";
    private static final int LOCK_EXPIRE_TIME = 60 * 1000;
    @Value("${comfyui.output.path}")
    private String outputPath;
    @Autowired
    private CreativeTaskDAO creativeTaskDAO;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private OssService ossService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Lazy
    @Autowired
    private PromptWebSocketService promptWebSocketService;
    @Autowired
    private TairService tairService;
    @Autowired
    private WeakLockHelper weakLockHelper;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private CreativeBatchElementsService creativeBatchElementsService;
    @Autowired
    private ImageModerationService imageModerationService;
    @Lazy
    @Autowired
    private CreativeServiceFactory creativeService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private PromptDictService promptDictService;
    @Autowired
    private ImageCaseService imageCaseService;
    @Autowired
    private ImageCaseDAO imageCaseDAO;
    @Autowired
    private FileHelper fileHelper;
    @Autowired
    private CommonTaskService commonTaskService;
    @Autowired
    private ComfyUIHelper comfyUIHelper;
    @Autowired
    private HuiWaService huiWaService;

    @Override
    public CreativeTaskVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeTaskDO data = creativeTaskDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return CreativeTaskConverter.do2VO(data);
    }

    @Override
    public CreativeTaskVO selectFullById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeTaskDO data = creativeTaskDAO.selectFullByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return CreativeTaskConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = creativeTaskDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeTask失败");
    }

    @Override
    public void batchDeleteByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        int n = creativeTaskDAO.batchLogicalDeleteByPrimaryKeys(ids);
        AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "批量删除CreativeTask失败");
    }

    @Override
    public CreativeTaskVO insert(CreativeTaskVO creativeTask) {
        AssertUtil.assertNotNull(creativeTask, ResultCode.PARAM_INVALID, "creativeTask is null");
        AssertUtil.assertTrue(creativeTask.getId() == null, ResultCode.PARAM_INVALID, "creativeTask.id is present");

        // 创建时间、修改时间兜底
        if (creativeTask.getCreateTime() == null) {
            creativeTask.setCreateTime(new Date());
        }

        if (creativeTask.getModifyTime() == null) {
            creativeTask.setModifyTime(new Date());
        }

        CreativeTaskDO data = CreativeTaskConverter.vo2DO(creativeTask);
        // 逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        int n = creativeTaskDAO.insertSelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CreativeTask失败");
        AssertUtil.assertNotNull(data.getId(), "新建CreativeTask返回id为空");
        creativeTask.setId(data.getId());
        return creativeTask;
    }

    @Override
    public void updateByIdSelective(CreativeTaskVO creativeTask) {
        AssertUtil.assertNotNull(creativeTask, ResultCode.PARAM_INVALID, "creativeTask is null");
        AssertUtil.assertTrue(creativeTask.getId() != null, ResultCode.PARAM_INVALID, "creativeTask.id is null");

        // 修改时间必须更新
        creativeTask.setModifyTime(new Date());
        CreativeTaskDO data = CreativeTaskConverter.vo2DO(creativeTask);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = creativeTaskDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CreativeTask失败，影响行数:" + n);
    }

    @Override
    public List<CreativeTaskVO> queryCreativeTaskList(CreativeTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CreativeTaskExample example = CreativeTaskConverter.query2Example(query);

        List<CreativeTaskDO> list = creativeTaskDAO.selectByExampleWithBLOBs(example);
        return CreativeTaskConverter.doList2VOList(list);
    }

    /**
     * 带条件分页查询创作任务
     */
    @Override
    public PageInfo<CreativeTaskVO> queryCreativeTaskByPage(CreativeTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<CreativeTaskVO> page = new PageInfo<>();

        CreativeTaskExample example = CreativeTaskConverter.query2Example(query);
        long totalCount = creativeTaskDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CreativeTaskDO> list = creativeTaskDAO.selectByExampleWithBLOBs(example);
        page.setList(CreativeTaskConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CreativeTaskVO> initTask(CreativeBatchVO batch) {
        List<CreativeElementVO> elements = creativeBatchElementsService.queryBatchElements(batch.getId(), true);

        return creativeService.buildTasks(batch, elements);
    }

    @Override
    public boolean isExistsTaskByBatchId(Integer batchId) {
        AssertUtil.assertNotNull(batchId, ResultCode.PARAM_INVALID, "batchId is null");

        CreativeTaskExample example = new CreativeTaskExample();

        example.createCriteria().andBatchIdEqualTo(batchId).andDeletedEqualTo(false);

        long cnt = creativeTaskDAO.countByExample(example);
        return cnt > 0;
    }

    @Override
    public List<CreativeTaskVO> queryTaskByBatchId(Integer batchId) {
        AssertUtil.assertNotNull(batchId, ResultCode.PARAM_INVALID, "batchId is null");

        CreativeTaskExample example = new CreativeTaskExample();

        example.createCriteria().andBatchIdEqualTo(batchId).andDeletedEqualTo(false);

        List<CreativeTaskDO> list = creativeTaskDAO.selectByExampleWithBLOBs(example);
        return CreativeTaskConverter.doList2VOList(list);
    }

    @Override
    public void batchSyncStatus(List<CreativeTaskVO> taskList, String serverUrl, Integer serverId) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        List<String> promptIds = new ArrayList<>();
        for (CreativeTaskVO task : taskList) {
            if (StringUtils.isNotBlank(task.getPromptId())) {
                promptIds.add(task.getPromptId());
            }
        }

        // 从queue中同步状态
        Map<String, QueueResult> queueMap = CollectionUtils.isNotEmpty(promptIds) ? comfyUIService.queryStatusByQueue(
            promptIds, serverUrl) : new HashMap<>();

        taskList.forEach(task -> {
            QueueResult queue = queueMap.get(task.getPromptId());
            if (queue == null) {
                log.warn("查询queue结果为空，taskId={},promptId={}", task.getId(), task.getPromptId());
                queue = new QueueResult();
            }

            task.addExtInfo(KEY_SERVER_URL, serverUrl);
            task.addExtInfo(KEY_SERVER_ID, serverId);

            syncStatusEach(task, queue);
            // updateByIdSelective(task);
        });
    }

    @Override
    public void syncStatusByWS(String promptId, QueueCodeEnum status, List<String> doneNodes, Integer nodeMax,
                               Integer nodeSchadule) {
        CreativeTaskVO data = fetchByPromptId(promptId);

        if (data == null) {
            log.warn("查询不到当前promptId对应的任务，可能是其他任务流程，忽略,promptId={}", promptId);
            return;
        }

        if (data.getStatus().isEnd()) {
            log.warn("当前promptId对应的任务状态已完结，不需要更新,promptId={},targetStatus={}", promptId, status);
            return;
        }

        // 如果是已经完结，则更新完结状态
        if (status == QueueCodeEnum.COMPLETED) {
            log.info("当前任务已完成，开始同步图片");

            if (!weakLockHelper.lock(WeakType.SYNC_IMAGE, data.getId())) {
                log.warn("2秒内同步图片任务已执行过，直接返回当前数据，不需要同步状态");
                return;
            }

            syncImages(data).subscribe();

            // 评估并重试
            if (!moderateAndRetry(data)) {
                return;
            }
            data.setStatus(CreativeStatusEnum.FINISHED);
            updateByWs(data);
            return;
        }

        // 以下只处理进行中的数据
        if (status != QueueCodeEnum.RUNNING) {
            log.info("当前节点状态{}非running，直接返回", status);
            return;
        }
        BigDecimal origin = data.getExtInfo() != null ? data.getExtInfo().getBigDecimal(KEY_SCHEDULE) : null;
        origin = origin != null ? origin : BigDecimal.ONE;
        int totalNode = data.getExtInfo() != null ? data.getExtInfo().getInteger(KEY_TOTAL_NODE) : 1;
        int doneNods = doneNodes.size() - 1; // 要减掉当前执行中的node
        if (nodeMax == null || nodeMax == 0) {
            nodeMax = 1;
        }
        BigDecimal subNodeSchedule = new BigDecimal(nodeSchadule).divide(new BigDecimal(nodeMax), 2,
            RoundingMode.HALF_UP);
        BigDecimal schedule = (new BigDecimal(doneNods).add(subNodeSchedule)).divide(new BigDecimal(totalNode), 2,
            RoundingMode.HALF_UP).multiply(new BigDecimal(100));

        if (!BigDecimalUtils.equalsZero(origin) && !BigDecimalUtils.greaterThan(schedule, origin)) {
            log.info("当前进度没有变化，无需更新,promptId={},origin={},schedule={}", promptId, origin, schedule);
            return;
        }

        log.info("更新task进度，promptId={},origin={},schedule={}", promptId, origin, schedule);

        data.setStatus(CreativeStatusEnum.PROCESSING);
        data.addExtInfo(KEY_SCHEDULE, CommonConstants.CATTY_DECIMAL_FORMAT.format(schedule));

        updateByWs(data);
    }

    @Override
    public Map<Integer, List<CreativeTaskVO>> batchQueryCreativeTask(List<Integer> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }

        CreativeTaskExample example = new CreativeTaskExample();
        example.createCriteria().andBatchIdIn(batchIds).andDeletedEqualTo(false);

        // 查询数据库
        List<CreativeTaskDO> taskList = creativeTaskDAO.selectByExampleWithBLOBs(example);

        // 转换为VO
        List<CreativeTaskVO> voList = CreativeTaskConverter.doList2VOList(taskList);

        // 按批次ID分组
        Map<Integer, List<CreativeTaskVO>> result = voList.stream().collect(
            Collectors.groupingBy(CreativeTaskVO::getBatchId));

        // 为没有查到数据的batchId创建空列表
        batchIds.forEach(batchId -> {
            result.putIfAbsent(batchId, new ArrayList<>());
        });

        return result;
    }

    @Override
    public PageInfo<String> queryImagesByElementWithPage(CreativeTaskQuery query) {
        // 获取元素及其子元素的ID列表
        List<Integer> elementIds = creativeElementService.getElementIdsWithChildren(query.getElementId());
        query.setElementIds(elementIds);

        PageInfo<CreativeTaskVO> data = queryCreativeTaskByPage(query);

        PageInfo<String> result = new PageInfo<>();
        result.setTotalCount(data.getTotalCount());
        result.setSize(data.getSize());
        result.setHasNextPage(data.isHasNextPage());
        result.setExtInfo(data.getExtInfo());

        if (CollectionUtils.isNotEmpty(data.getList())) {
            List<String> collect = data.getList().stream().map(CreativeTaskVO::getResultImages).filter(Objects::nonNull)
                .flatMap(List::stream).collect(Collectors.toList());
            result.setList(collect);
        }

        return result;
    }

    @Override
    public void batchInsert(List<CreativeTaskVO> tasks) {
        creativeTaskDAO.batchInsert(CreativeTaskConverter.voList2DOList(tasks));
    }

    /**
     * 同步任务状态
     *
     * @param task  当前任务
     * @param queue 任务查询结果
     */
    private void syncStatusEach(CreativeTaskVO task, QueueResult queue) {
        // 对于三方任务进行单独处理
        String isThirdPartTask = task.getStringFromExtInfo(KEY_IS_THIRD_PART_TASK);
        // 执行三方应用处理
        if (StringUtils.isNotBlank(isThirdPartTask) && isThirdPartTask.equals(YES)) {
            commonTaskService.syncThirdTaskStatus(task);
            return;
        }

        if (task.getStatus() == CreativeStatusEnum.INIT) {
            log.info("当前任务还在初始化中，不需要调用ComfyUI查询，直接开始调用重试接口");

            retry(task);
            return;
        }

        String serverUrl = task.getStringFromExtInfo(KEY_SERVER_URL);
        String promptId = task.getPromptId();
        log.info("调用comfyUI查询taskId={},promptId={}任务状态结果：{}", task.getId(), promptId, queue);
        boolean isNone = queue.getCode() == QueueCodeEnum.NONE;

        // 1.如果是在队列中或进行中的，已经有队列位置了，则直接返回
        if (queue.getCode() == QueueCodeEnum.QUEUED) {
            log.info("【任务状态查询】，1查询队列服务后，{}状态为队列中，队列长度{}", promptId, queue.getQueueSize());
            task.addExtInfo(CommonConstants.KEY_QUEUE_SIZE, queue.getQueueSize());

            // 2.如果是进行中，缺少进度数据，则通过ws服务查询进度
        } else if (queue.getCode() == QueueCodeEnum.RUNNING) {
            log.info("【任务状态查询】，2查询队列服务后，{}状态为运行中，等待ws同步返回数据", promptId);
            if (needWSSync(task)) {
                promptWebSocketService.startListening(buildClientId(task.getOperatorId()), serverUrl);
            }
            CreativeTaskVO cache = fetchPromptCache(task.getPromptId());
            if (cache != null && cache.getExtInfo() != null) {
                task.addExtInfo(KEY_SCHEDULE, cache.getExtInfo().getBigDecimal(KEY_SCHEDULE));
            }

        } else {
            // 3.剩下未知情况，需要查询历史记录(历史记录中只有完成了的数据)
            queue = comfyUIService.queryStatusByHistory(promptId, serverUrl);
            isNone = isNone & queue.getCode() == QueueCodeEnum.NONE;
        }

        // 3.在queue中取不到，在history中状态也是不成功或者未知时，查询图片是否存在
        if (isNone || queue.getCode() == QueueCodeEnum.UNKNOWN || queue.getCode() == QueueCodeEnum.FAILED) {
            // 2.3.2.在调用一次获取一下目标目录下是否已经生成图片
            // 如果此时还是没有查到数据，则需要查询outputPath下是否已经存在图片
            Integer cnt = comfyUIService.queryOutputImageCnt(task.getResultPath(),
                ComfyUIUtils.buildFileNamePrefix(task.getId()), getFileServerUrl(task));

            // 2.3.2.2.如果已经生成图片，则直接返回已完成
            if (cnt != null && cnt >= task.getBatchCnt()) {
                log.info("【任务状态查询】，5查询图片数量后，{}图片数量{}大于{}，直接返回成功", promptId, cnt,
                    task.getBatchCnt());
                queue.setCode(QueueCodeEnum.COMPLETED);
            }
        }

        // 4.如果结果是失败，则重新调用prompt服务
        if ((isNone && queue.getCode() != QueueCodeEnum.COMPLETED) || queue.getCode() == QueueCodeEnum.FAILED) {
            if (task.getType() == CreativeTypeEnum.REPAIR_HANDS && comfyUIService.checkFileExists(
                outputPath + task.getResultPath() + "/error.txt", getFileServerUrl(task))) {
                log.warn("手部修复涂抹区域未检测到手，直接设置为失败,taskId={}", task.getId());
                task.setStatus(CreativeStatusEnum.FAILED);
            } else {
                // 如果执行失败，重新开始执行
                log.warn("原请求id={}执行失败，开始失败重试,isNone={},status={}", promptId, isNone, queue.getCode());
                retry(task);
                return;
            }
        }

        // 5.完结状态时更新图片信息
        if (queue.getCode() == QueueCodeEnum.COMPLETED) {
            log.info("【任务状态查询】，3查询历史记录后，{}状态为完成，同步图片后返回", promptId);

            if (!weakLockHelper.lock(WeakType.SYNC_IMAGE, task.getId())) {
                log.warn("2秒内同步图片任务已执行过，直接返回当前数据，不需要同步状态");
                return;
            }

            boolean hasError = false;
            try {
                syncImages(task).subscribe();
            } catch (Exception e) {
                hasError = true;
            }

            // 如果有异常，或者是文件读不到，这里做一个补偿，再check下文件是否存在，不存在时进行重试
            if (hasError || CollectionUtils.isEmpty(task.getResultImages())) {
                Integer cnt = comfyUIService.queryOutputImageCnt(task.getResultPath(),
                    ComfyUIUtils.buildFileNamePrefix(task.getId()), getFileServerUrl(task));
                if (cnt != null && cnt < task.getBatchCnt()) {
                    // 重新调用prompt生成图片
                    log.error("【任务状态查询】，查询历史记录成功，且查询文件不存在，直接开始重试,batchId={},taskId={}",
                        task.getBatchId(), task.getId());
                    retry(task);
                    return;
                }

                log.error("【任务状态查询】，查询历史记录成功，且查询文件存在，可能是文件服务抖动导致，本次跳过");
                return;
            }

            // 评估并重试
            if (!moderateAndRetry(task)) {
                return;
            }

            if (StringUtils.isBlank(task.getExtValue(KEY_END_TIME, String.class))) {
                task.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
            }
        }

        // 6.更新订单状态
        // 未知时不变更原来状态码
        if (queue.getCode() != QueueCodeEnum.UNKNOWN) {
            CreativeStatusEnum queueCode = CreativeStatusEnum.getByQueueCode(queue.getCode());
            if (queueCode.getOrder() > task.getStatus().getOrder()) {
                task.setStatus(queueCode);
            }
        }

        updateByIdSelective(task);
    }

    /**
     * 失败重试
     *
     * @param task 创作任务
     */
    private void retry(CreativeTaskVO task) {
        if (!weakLockHelper.lock(WeakType.TASK, task.getId())) {
            log.info("3秒内任务已经进行过重试，直接跳过，{}", task.getId());
            return;
        }

        // 参数准备
        CreativeTaskVO target = selectById(task.getId());
        MaterialModelVO modelVO = null;
        List<CreativeElementVO> elements = null;
        CreativeTypeEnum type = target.getType();

        if (type == CreativeTypeEnum.CREATE_IMAGE || type == CreativeTypeEnum.FIXED_POSTURE_CREATION) {
            modelVO = materialModelService.selectById(target.getModelId());
        }

        if (type == CreativeTypeEnum.POSE_SAMPLE_DIAGRAM) {
            modelVO = new MaterialModelVO();
            modelVO.setVersion(ModelVersionEnum.FLUX);
        }

        if (type == CreativeTypeEnum.BASIC_CHANGING_CLOTHES || type == CreativeTypeEnum.FACE_SCENE_SWITCH) {
            elements = creativeBatchElementsService.queryBatchElementsNotEmpty(task.getBatchId(), true);
        } else if (ArrayUtils.isNotEmpty(type.getConfigKeys())) {
            elements = creativeBatchElementsService.queryBatchElements(task.getBatchId(), true);
        }

        // 场景/模特/服装 非空检测，如果为空，则直接终止
        if (checkElementsEmpty(task)) {
            // 状态设置为失败
            task.setStatus(CreativeStatusEnum.FAILED);
            // 更新task信息
            updateByIdSelective(task);
            return;
        }

        // 兼容处理
        String serverUrl = task.getStringFromExtInfo(KEY_SERVER_URL);
        target.addExtInfo(KEY_SERVER_URL, serverUrl);

        callPrompt(target, modelVO, elements);
    }

    /**
     * 调用ComfyUI任务
     *
     * @param task     创作任务
     * @param modelVO  模型信息
     * @param elements 元素信息
     */
    private void callPrompt(CreativeTaskVO task, MaterialModelVO modelVO, List<CreativeElementVO> elements) {

        // 0.如果该task刚初始化，则先查询是否队列满了
        if (task.getStatus() == CreativeStatusEnum.INIT) {
            CreativeTaskExample example = new CreativeTaskExample();
            Criteria criteria = example.createCriteria();
            criteria.andBatchIdEqualTo(task.getBatchId()).andStatusIn(CreativeStatusEnum.getProcessingStatusList())
                .andDeletedEqualTo(false);

            long processCnt = creativeTaskDAO.countByExample(example);

            if (processCnt >= CommonConstants.MAX_PROCESS_CNT) {
                log.info("当前任务数量超过最大限制，等待调度中{},cnt={}", task.getId(), processCnt);
                return;
            }
        }

        // 1.参数准备
        String clientId = buildClientId(task.getOperatorId());
        String serverUrl = task.getStringFromExtInfo(KEY_SERVER_URL);

        // 2.开始监听ws信息，再调用prompt接口
        if (needWSSync(task)) {
            promptWebSocketService.startListening(clientId, serverUrl);
        }

        String lockKey = LOCK_KEY_PREFIX + task.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.info("task任务正在处理中，直接返回,id={},uid={}", task.getId(), task.getOperatorId());
            return;
        }

        try {
            // 3.调用prompt接口
            String aigcRequest = creativeService.buildFlow(task, modelVO, elements);
            PromptResult prompt = comfyUIService.prompt(aigcRequest, serverUrl);
            fileHelper.parseAndSyncFile(prompt);
            AssertUtil.assertTrue(prompt.isSuccess(), ResultCode.BIZ_FAIL,
                "aigc服务调用失败" + prompt.getError() + ", taskId=" + task.getId() + ", 请求参数为：" + aigcRequest);

            // 4.清理相关信息并更新
            task.setPromptId(prompt.getPromptId());
            task.setStatus(CreativeStatusEnum.QUEUE);
            task.clearExtInfo();
            task.clearResultImage();
            int totalNode = JSONObject.parseObject(aigcRequest).getJSONObject("prompt").size();
            task.addExtInfo(KEY_TOTAL_NODE, totalNode);

            //  tpl_version 不为空，说明是模板，不保存所提交的prompt
            if (task.getTplInfo() != null) {
                task.setAigcRequest(null);
            } else {
                task.setAigcRequest(aigcRequest);
            }

        } catch (Exception e) {
            log.error("调用prompt接口异常,taskId=" + task.getId() + ",serverUrl=" + serverUrl, e);
        } finally {
            // 释放锁
            tairService.releaseLock(lockKey);

            // 设置尝试次数
            Integer originTryTimes = task.getExtInfo() != null ? task.getExtInfo().getInteger(KEY_TRY_TIMES) : null;
            int tryTimes = originTryTimes == null ? 1 : originTryTimes + 1;

            task.addExtInfo(KEY_TRY_TIMES, tryTimes);
            notifyRetry(task, tryTimes, serverUrl);

            if (StringUtils.isBlank(task.getExtValue(KEY_START_TIME, String.class))) {
                task.addExtInfo(KEY_START_TIME, DateUtils.formatFullTime(new Date()));
            }

            updateByIdSelective(task);
        }

    }

    /**
     * 对重试进行通知
     *
     * @param task      任务
     * @param tryTimes  重试次数
     * @param serverUrl 服务地址
     */
    private void notifyRetry(CreativeTaskVO task, int tryTimes, String serverUrl) {
        if (tryTimes % 20 != 3) {
            return;
        }
        log.warn("单张出图重试次数超过3次,taskId={},uid={},tryTimes={}", task.getId(), task.getUserId(), tryTimes);

        // 控制钉钉报警频率
        String tairKey = "_retry_url_" + serverUrl;
        Boolean exists = tairService.getObject(tairKey, Boolean.class);
        if ((exists != null && exists) || tryTimes < 10) {
            return;
        }

        Calendar calendar = Calendar.getInstance();
        int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
        // 8点前不发送钉钉消息
        if (hourOfDay < 9) {
            return;
        }

        DingTalkNoticeHelper.sendMsg2DevGroup(String.format(
            "单张出图重试次数超过3次，\ntraceId：%s，\ntaskId：%s / batchId: %s，\nuserId：%s / modelId: "
            + "%s，\nserverUrl：%s\n当前重试次数：%s次,\n任务创建时间：%s,\n任务类型：%s", MDC.get("traceId"), task.getId(),
            task.getBatchId(), task.getUserId(), task.getModelId(), serverUrl, tryTimes,
            DateUtils.formatTime(task.getCreateTime()), task.getType()));
        tairService.setObject(tairKey, true, 3 * 60);
    }

    /**
     * 同步图片到OSS存储
     *
     * @param target 创作任务
     * @return Mono<Void> 异步操作结果
     */
    private Mono<Void> syncImages(CreativeTaskVO target) {
        return Mono.fromRunnable(() -> {
            // 1. 下载基础图片
            Map<String, byte[]> baseImages = downloadImage(target);
            if (MapUtils.isEmpty(baseImages)) {
                log.error("调用ComfyUI服务器端服务扫描并传输图片失败，返回结果为空,id={},uid={}", target.getId(),
                    target.getOperatorId());
                return;
            }

            // 2. 下载带前缀的图片
            Map<String, Map<String, byte[]>> prefixImageMap = null;

            switch (target.getType()) {
                // 基础款换衣需要进行处理
                case BASIC_CHANGING_CLOTHES:
                    prefixImageMap = downloadImageWithMultiplePrefixes(target, Arrays.asList("face_", "mask_"));
                    break;
                case PICTURE_MATTING:
                    // 后续扩展
                    break;
            }

            // 3. 处理基础图片
            processBaseImages(target, baseImages);

            // 4. 处理带前缀的图片
            if (MapUtils.isNotEmpty(prefixImageMap)) {
                processPrefixImages(target, prefixImageMap);
            }

        });
    }

    /**
     * 处理基础图片
     *
     * @param target     创作任务
     * @param baseImages 基础图片映射
     */
    private void processBaseImages(CreativeTaskVO target, Map<String, byte[]> baseImages) {
        baseImages.forEach((fileName, bytes) -> {
            validateAndUploadBaseImage(target, fileName, bytes);
        });
    }

    /**
     * 处理带前缀的图片
     *
     * @param target         创作任务
     * @param prefixImageMap 带前缀的图片映射
     */
    private void processPrefixImages(CreativeTaskVO target, Map<String, Map<String, byte[]>> prefixImageMap) {
        // 处理脸部图片
        Map<String, byte[]> faceImages = prefixImageMap.get("face_");
        if (MapUtils.isNotEmpty(faceImages)) {
            // 正常情况下，只会有一张图片，若含有多张则只处理最后一张
            String fileName = faceImages.keySet().iterator().next();
            byte[] bytes = faceImages.get(fileName);
            validateAndUploadPrefixImage(target, KEY_FACE_IMAGE_NAME, fileName, bytes);
        }

        // 处理蒙版图片
        Map<String, byte[]> maskImages = prefixImageMap.get("mask_");
        if (MapUtils.isNotEmpty(maskImages)) {
            // 正常情况下，只会有一张图片，若含有多张则只处理最后一张
            String fileName = maskImages.keySet().iterator().next();
            byte[] bytes = maskImages.get(fileName);
            validateAndUploadPrefixImage(target, KEY_MASK_IMAGE_NAME, fileName, bytes);
        }
    }

    /**
     * 验证并上传基础图片到OSS
     *
     * @param target   创作任务
     * @param fileName 文件名
     * @param bytes    图片字节数组
     */
    private void validateAndUploadBaseImage(CreativeTaskVO target, String fileName, byte[] bytes) {
        // 验证文件名不为空
        if (StringUtils.isBlank(fileName) || bytes.length == 0) {
            return;
        }

        // 上传图片到OSS
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            // 构建OSS存储路径
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
            String tag = target.getType().getImageTag();
            String imageName = FileUtils.confoundFileName(fileName, tag);
            String fullName = sdf.format(target.getCreateTime()) + target.getUserId() + "/" + imageName;

            // 上传图片并添加盲水印
            String imageUrl = ossService.upload(fullName, inputStream);

            // 记录图片信息到结果列表
            target.addResultImage(imageUrl);
            target.addExtInfo(KEY_ORIGIN_REMOTE_IMAGE_NAME, fileName);

            log.info("上传基础文件{}到oss上，返回url:{}", fullName, imageUrl);
        } catch (IOException e) {
            log.error("处理基础图片文件流异常", e);
        }
    }

    /**
     * 验证并上传带前缀的图片到OSS
     *
     * @param target   创作任务
     * @param prefix   前缀类型
     * @param fileName 文件名
     * @param bytes    图片字节数组
     */
    private void validateAndUploadPrefixImage(CreativeTaskVO target, String prefix, String fileName, byte[] bytes) {
        // 验证文件名不为空
        if (StringUtils.isBlank(fileName) || bytes.length == 0) {
            return;
        }

        // 上传图片到OSS
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            // 构建OSS存储路径
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
            String tag = target.getType().getImageTag();
            String imageName = FileUtils.confoundFileName(fileName, tag);
            String fullName = sdf.format(target.getCreateTime()) + target.getUserId() + "/" + imageName;

            // 上传图片并添加盲水印
            String imageUrl = ossService.upload(fullName, inputStream);

            // 记录图片信息到扩展信息
            target.addExtInfo(prefix, imageUrl);

            log.info("上传{}前缀文件{}到oss上，返回url:{}", prefix, fullName, imageUrl);
        } catch (IOException e) {
            log.error("处理{}前缀图片文件流异常", prefix, e);
        }
    }

    /**
     * 使用多个前缀下载图片 (拉取带有前缀的文件时，如果拉取过程中出现异常则会直接跳过)
     *
     * @param target   创作任务
     * @param prefixes 多个前缀
     * @return 包含不同前缀分类的图片映射，key是前缀名称，value是该前缀下的图片映射
     */
    private Map<String, Map<String, byte[]>> downloadImageWithMultiplePrefixes(CreativeTaskVO target,
                                                                               List<String> prefixes) {
        String fileServerUrl = getFileServerUrl(target);
        String basePrefix = ComfyUIUtils.buildFileNamePrefix(target.getId());

        Map<String, Map<String, byte[]>> result = new HashMap<>();

        // 处理每个前缀
        for (String prefix : prefixes) {
            // 将基础前缀与指定前缀拼接
            String fullPrefix = prefix + basePrefix;

            // 下载对应前缀的图片
            Map<String, byte[]> prefixImages = null;
            try {
                prefixImages = comfyUIService.downloadImage(target.getResultPath(), fullPrefix, fileServerUrl);
            } catch (Exception e) {
                log.info("【前缀文件】【{}】下载图片异常，跳过本次下载。", prefix);
                continue;
            }

            // 将该前缀的图片映射加入结果集
            if (MapUtils.isNotEmpty(prefixImages)) {
                result.put(prefix, prefixImages);
            }
        }

        return result;
    }

    private Map<String, byte[]> downloadImage(CreativeTaskVO target) {
        String fileServerUrl = getFileServerUrl(target);

        // 0.如果批次数量为0时，直接从ComfyUI下载单个文件
        if (target.getBatchCnt() == 1) {
            if (target.getType().isVideoCreative()) {
                return comfyUIService.downloadVideo(target.getResultPath(),
                    ComfyUIUtils.buildFileNamePrefix(target.getId()), fileServerUrl);
            } else {
                return comfyUIService.downloadImage(target.getResultPath(),
                    ComfyUIUtils.buildFileNamePrefix(target.getId()), fileServerUrl);
            }
        }

        log.info("开始批量下载图片,id={},uid={},path={}", target.getId(), target.getUserId(), target.getResultPath());

        Map<String, byte[]> result = new HashMap<>();
        // 1.调用ComfyUI服务器端服务扫描并传输所有图片
        byte[] bytes = comfyUIService.batchFetchImageFile(target.getResultPath(),
            ComfyUIUtils.buildFileNamePrefix(target.getId()), fileServerUrl);

        // 2.上传zip包到oss，将地址添加到extInfo
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);

        // 3.将所有图片保存到oss，并回填resultImages
        // 需要将流重置，返回到开始位置
        ZipInputStream zis = new ZipInputStream(inputStream);
        ZipEntry zipEntry = null;

        try {
            // 读取 ZIP 中的每个条目
            while ((zipEntry = zis.getNextEntry()) != null) {
                // 读取图片内容
                ByteArrayOutputStream imageOutputStream = new ByteArrayOutputStream();

                byte[] buffer = new byte[BUFFER_SIZE];
                int len;
                while ((len = zis.read(buffer, 0, BUFFER_SIZE)) > 0) {
                    imageOutputStream.write(buffer, 0, len);
                }

                result.put(zipEntry.getName(), imageOutputStream.toByteArray());
                log.info("获取到文件内容，fileName={}", zipEntry.getName());

                imageOutputStream.close();
                zis.closeEntry();
            }
        } catch (IOException e) {
            log.error("读取zip文件异常", e);
        } finally {
            try {
                zis.close();
            } catch (IOException e) {
                log.error("close zis error", e);
            }
        }

        return result;
    }

    /**
     * 图片质量及内容安全评估，不通过的进行重试
     *
     * @param task 创作任务
     * @return 返回结果，true，评估通过
     */
    private boolean moderateAndRetry(CreativeTaskVO task) {
        // 1.先自身做一次图片质量评估
        boolean isQualityOk = assessmentImageQuality(task);

        // 1.1.如果图片质量通过，则继续进行内容安全评估，否则直接删除图片
        if (isQualityOk) {
            String imageUrl = CollectionUtils.isEmpty(task.getResultImages()) ? null : task.getResultImages().get(0);
            if (StringUtils.isBlank(imageUrl)) {
                log.warn("图片URL为空，跳过内容安全评估，taskId={}", task.getId());
                return false;
            }

            if (!task.getType().isNeedModerate()) {
                log.info("当前创作类型跳过图片安全评估, taskId={}, type={}", task.getId(), task.getType());
                return true;
            }

            // 2.再进行内容安全评估
            if (moderate(imageUrl, task.getPromptId(), task.getId(), task.getBatchId(),
                task.getExtValue(KEY_CLOTH_TYPE, String.class), task.getExtValue(KEY_GARMENT_TYPE, String.class))) {
                return true;
            } else if (task.getExtValue(KEY_TEST_GROUP_ID, Integer.class) != null) {
                Integer retryTimes = task.getExtValue(KEY_TRY_TIMES, Integer.class);
                if (retryTimes != null && retryTimes > 3) {
                    log.warn("实验数据，图片安全评估不通过，且重试次数超过3次，跳过，id={},retryTimes={}", task.getId(),
                        retryTimes);
                    return true;
                }
            }
        }

        // 评估不通过时：
        // 3.删除comfyui上的图片文件
        String fileNamePrefix = ComfyUIUtils.buildFileNamePrefix(task.getId());
        boolean remove = comfyUIService.removeImage(task.getResultPath(), fileNamePrefix, getFileServerUrl(task));
        AssertUtil.assertTrue(remove, ResultCode.SYS_ERROR, "调用ComfyUI删除接口失败");

        // 4.重新调用prompt生成图片
        retry(task);

        // 最后返回
        return false;
    }

    /**
     * 执行一次图片质量评估
     *
     * @param task 任务
     * @return true，通过
     */
    private boolean assessmentImageQuality(CreativeTaskVO task) {
        if (task.getExtValue(KEY_TEST_GROUP_ID, Integer.class) != null) {
            log.warn("图片质量检测跳过，发现任务为测试任务，id={},batchId={}", task.getId(), task.getBatchId());
            return true;
        }

        // 获取图片地址
        String imageUrl = CollectionUtils.isNotEmpty(task.getResultImages()) ? task.getResultImages().get(0) : null;
        if (StringUtils.isBlank(imageUrl)) {
            log.warn("图片结果URL为空，跳过图片质量检测，id={},batchId={}", task.getId(), task.getBatchId());
            return true;
        }

        String originRemoteImageName = task.getStringFromExtInfo(KEY_ORIGIN_REMOTE_IMAGE_NAME);
        Boolean isPureBG = task.getExtInfo(KEY_IS_PURE_BG, Boolean.class);
        // 其他纯色场景id，需要也打上标记
        Integer sceneId = task.getExtInfo(KEY_SCENE_ID, Integer.class);
        if (sceneId != null && systemConfigService.isInJsonArray(PURE_BG_SCENE_IDS, sceneId)) {
            isPureBG = true;
        }
        // 兼容处理，发布过程中可能存在旧数据
        if (StringUtils.isBlank(originRemoteImageName) || isPureBG == null) {
            log.warn("图片质量检测跳过，原始图片文件名为空或是否纯色背景为空，id={},imageName={},isPureBG={},batchId={}",
                task.getId(), originRemoteImageName, isPureBG, task.getBatchId());
            return true;
        }

        // 更换地址
        String fileServerUrl = serverHelper.getModelServerUrlByUser(task.getUserId());
        if (StringUtils.isBlank(fileServerUrl)) {
            log.warn("图片质量检测跳过，获取文件服务器地址失败，id={},batchId={}", task.getId(), task.getBatchId());
            return true;
        }

        // 构建图片路径
        String path = task.getResultPath() + "/" + originRemoteImageName;

        ImageQualityVO imageQualityVO = comfyUIService.isBadImage(path, imageUrl, isPureBG, fileServerUrl);
        if (imageQualityVO == null) {
            log.warn("图片质量检测跳过，调用检测结果为空，id={},imageName={},isPureBG={},batchId={}", task.getId(),
                originRemoteImageName, isPureBG, task.getBatchId());
            return true;
        }

        // 图片质量检测通过
        if (!imageQualityVO.getBadImage()) {
            log.info("图片质量检测通过,id={},uid={},path={},imageUrl={},batchId={}", task.getId(), task.getUserId(),
                path, imageUrl, task.getBatchId());
            return true;
        }

        // 不通过场景，统一添加到图库
        List<String> tags = imageQualityVO.getTags();
        addToImageCase(imageUrl, path, tags, task);

        // 判断label白名单
        if (tags.stream().allMatch(e -> systemConfigService.isInJsonArray(IMAGE_QUALITY_CHECK_IGNORE_LABEL, e))) {
            log.warn("图片质量检测不通过，但标签为白名单，跳过，id={},batchId={},tags={},imageUrl={}", task.getId(),
                task.getBatchId(), tags, imageUrl);
            return true;
        }

        // 如果超过3次还没有出图，则跳过
        Integer badImageTimes = task.getExtValue(KEY_BAD_IMAGE_TIMES, Integer.class);
        // 若为空，则初始化为0
        if (badImageTimes == null) {
            badImageTimes = 0;
        } else if (badImageTimes >= 2) {
            // 大于3次，则跳过
            log.warn("图片质量检测不通过，且重试次数超过2次，跳过，id={},badImageTimes={},batchId={}", task.getId(),
                badImageTimes, task.getBatchId());
            return true;
        }

        log.warn("图片质量检测不通过,id={},uid={},path={},imageUrl={},batchId={},times={},tags={}", task.getId(),
            task.getUserId(), path, imageUrl, task.getBatchId(), badImageTimes, JSONObject.toJSONString(tags));
        task.addExtInfo(KEY_BAD_IMAGE_TIMES, badImageTimes + 1);
        updateByIdSelective(task);

        return false;
    }

    /**
     * 将低质量的图片保存到图片案例中
     *
     * @param imageUrl 图片地址
     * @param path     图片路径
     * @param tags     质量结果标签
     * @param task     创作任务
     */
    private void addToImageCase(String imageUrl, String path, List<String> tags, CreativeTaskVO task) {
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }
        // 构建图片案例
        ImageCaseVO imageCaseVO = buildImageCaseVO(imageUrl, path, task);
        ImageCaseVO imageCase = imageCaseService.insert(imageCaseVO);

        // 初始化
        List<Integer> tagList = tags.stream().map(
            tag -> promptDictService.queryByTypeAndPrompt(DictTypeEnum.IMAGE_TAGS, tag)).filter(Objects::nonNull).map(
            PromptDictVO::getId).collect(Collectors.toList());

        // 插入标签
        if (!tagList.isEmpty()) {
            insertImageCaseTags(tagList, imageCase.getId());
        }
    }

    /**
     * 获取文件服务地址
     *
     * @param task 任务
     * @return 文件服务地址
     */
    private String getFileServerUrl(CreativeTaskVO task) {
        return serverHelper.getFileServerUrlByTask(task);
    }

    /**
     * 内容安全评估
     *
     * @param imageUrl    图片地址
     * @param promptId    promptId
     * @param id          taskId
     * @param batchId     批次id
     * @param clothType   服装款式
     * @param garmentType 服装类别
     * @return 返回结果，true，评估通过
     */
    private boolean moderate(String imageUrl, String promptId, Integer id, Integer batchId, String clothType,
                             String garmentType) {
        if (StringUtils.isBlank(imageUrl)) {
            return true;
        }
        Matcher matcher = OSS_FILE_PATH_PATTERN.matcher(imageUrl);
        String fileName = matcher.find() ? matcher.group(1) : null;

        List<String> labels = imageModerationService.moderateByOSS(fileName, promptId);
        if (CollectionUtils.isEmpty(labels)) {
            log.info("内容安全审核通过，阿里云结果为空,id={},batchId={}", id, batchId);
            return true;
        }

        List<String> configLabels;
        JSONArray garments = systemConfigService.queryJsonArrValue(KEY_SEXY_LINGERIE_GARMENT);
        if (ClothTypeEnum.getByCode(clothType) == ClothTypeEnum.SexyLingerie || (StringUtils.isNotBlank(garmentType)
                                                                                 && garments.stream().anyMatch(
            e -> Pattern.compile("(?<=^|\\s)" + e + "(?=\\s|$)").matcher(garmentType).find()))) {

            log.info("内容安全审核，命中情趣内衣类目,batchId={}", batchId);
            JSONArray jsonarray = systemConfigService.queryJsonArrValue(SEXY_LINGERIE_FORBID_LABEL);
            configLabels = jsonarray.stream().map(Object::toString).collect(Collectors.toList());
        } else {
            configLabels = systemConfigService.queryModerationForbidLabel();
        }

        for (String each : labels) {
            if (configLabels.contains(each)) {
                if (systemConfigService.isInJsonArray(MODERATE_IGNORE_IDS, batchId)) {
                    log.info("内容安全审核忽略，命中规则{},id={},batchId={},image={}", each, id, batchId, imageUrl);
                    return true;
                }

                log.warn("内容安全审核不通过，命中规则{},id={},batchId={},image={}", each, id, batchId, imageUrl);
                return false;
            }
        }

        log.info("内容安全审核通过,id={},batchId={},阿里云返回={}", id, batchId, labels);
        return true;
    }

    /**
     * 根据promptId获取任务
     *
     * @param promptId promptId
     * @return 返回结果
     */
    private CreativeTaskVO fetchByPromptId(String promptId) {
        CreativeTaskVO data = fetchPromptCache(promptId);
        if (data != null) {
            return data;
        }

        CreativeTaskExample example = new CreativeTaskExample();
        example.createCriteria().andPromptIdEqualTo(promptId).andDeletedEqualTo(false);
        List<CreativeTaskDO> taskList = creativeTaskDAO.selectByExampleWithBLOBs(example);

        if (CollectionUtils.isEmpty(taskList)) {
            return null;
        }

        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(taskList) && taskList.size() == 1, ResultCode.BIZ_FAIL,
            "一条promptId对应多个任务," + promptId);

        return CreativeTaskConverter.do2VO(taskList.get(0));
    }

    private void updateByWs(CreativeTaskVO data) {
        if (!weakLockHelper.lock(WeakType.WS_SCHEDULE, data.getId())) {
            log.warn("2秒内同步任务进展已执行过，直接返回当前数据，不需要同步状态");
            return;
        }

        // 更新tair缓存
        savePromptCache(data);

        // 如果任务状态为完成，则更新
        if (data.getStatus() == CreativeStatusEnum.FINISHED) {
            updateByIdSelective(data);
        }
    }

    /**
     * 根据promptId获取缓存数据
     *
     * @param promptId promptId
     * @return 缓存数据
     */
    private CreativeTaskVO fetchPromptCache(String promptId) {
        return tairService.getObject(getPromptCacheKey(promptId), CreativeTaskVO.class);
    }

    /**
     * 保存promptId缓存
     *
     * @param task 任务
     */
    private void savePromptCache(CreativeTaskVO task) {
        task.setAigcRequest(null);
        tairService.setObject(getPromptCacheKey(task.getPromptId()), task, CommonConstants.TEN_MINUTES);
    }

    /**
     * 获取缓存key
     *
     * @param promptId promptId
     * @return 缓存key
     */
    private String getPromptCacheKey(String promptId) {
        return "_task_prompt_" + promptId;
    }

    /**
     * 构建ImageCaseVO对象
     *
     * @param url  请求参数
     * @param path 地址
     * @param task
     * @return ImageCaseVO对象
     */
    private ImageCaseVO buildImageCaseVO(String url, String path, CreativeTaskVO task) {
        ImageCaseVO imageCaseVO = new ImageCaseVO();
        imageCaseVO.setUrl(url);
        imageCaseVO.setStorePath(path);
        imageCaseVO.setStatus(MaterialModelStatusEnum.ENABLED);
        imageCaseVO.setCreateTime(new Date());
        imageCaseVO.setModifyTime(new Date());
        imageCaseVO.setSyncStatus(false);
        imageCaseVO.setReSyncCount(0);
        imageCaseVO.setMemo("安全检测图片...");

        // 构建扩展信息
        imageCaseVO.setBatchId(task.getBatchId());
        imageCaseVO.setTaskId(task.getId());

        // 返回构建完成的ImageCaseVO对象
        return imageCaseVO;
    }

    /**
     * 批量插入图片案例标签
     *
     * @param promptDictList 字典 id 列表
     * @param imageCaseId    图片案例ID
     */
    private void insertImageCaseTags(List<Integer> promptDictList, Integer imageCaseId) {
        // 批量构建标签对象
        List<ImageCaseTagDO> tagList = promptDictList.stream().map(dict -> new ImageCaseTagDO(imageCaseId, dict))
            .collect(Collectors.toList());

        // 批量插入标签
        tagList.forEach(imageCaseDAO::insertTag);
    }

    /**
     * 是否需要WS同步进展
     *
     * @param data 任务
     * @return true，需要
     */
    private static boolean needWSSync(CreativeTaskVO data) {
        if (data == null) {
            return false;
        }
        String bizTag = data.getExtInfo(BIZ_TAG, String.class);
        return !StringUtils.equals(bizTag, SYSTEM_IMAGES) && !StringUtils.equals(bizTag, TEST_CASE);
    }

    /**
     * 检查元素是否为空
     *
     * @param task 任务 ID
     * @return true，为空
     */
    private boolean checkElementsEmpty(CreativeTaskVO task) {
        CreativeTypeEnum type = task.getType();

        // 创建姿势示例图
        if (type == CreativeTypeEnum.POSE_SAMPLE_DIAGRAM) {
            log.info("【姿势示例图】检查元素是否为空");

            // 提取场景 id
            String sceneIdStr = task.getStringFromExtInfo(CommonConstants.KEY_POSE_ELEMENT_ID);
            if (StringUtils.isBlank(sceneIdStr)) {
                log.error("【姿势示例图】场景ID为空");
                return true;
            }
            int sceneId = Integer.parseInt(sceneIdStr);

            // 获取场景元素
            CreativeElementVO creativeElementVO = creativeElementService.selectById(sceneId);

            log.info("【姿势示例图】场景id: {}, 元素: {}", sceneId, creativeElementVO);
            // 检查元素是否为空
            return creativeElementVO == null;
        }

        return false;
    }

    /**
     * 二次过滤掉前置任务未完成的多阶段任务
     *
     * @param unfinishedTasks 未完成的任务列表
     * @param isNeedSyncData  是否需要同步数据
     */
    public void filterMultiProcess(List<CreativeTaskVO> unfinishedTasks, Boolean isNeedSyncData) {
        // 若为空则直接返回
        if (CollectionUtils.isEmpty(unfinishedTasks)) {
            return;
        }

        Iterator<CreativeTaskVO> iterator = unfinishedTasks.iterator();
        while (iterator.hasNext()) {
            CreativeTaskVO item = iterator.next();

            // 获取isMultiProcess标识
            String isMultiProcess = item.getStringFromExtInfo(KEY_IS_MULTI_PROCESS);

            // 1、判断任务中是否含有isMultiProcess标识,不含有则未正常任务，直接放行
            if (StringUtils.isEmpty(isMultiProcess) || !isMultiProcess.equals(YES)) {
                continue;
            }

            // 2、校验前置任务 id 是否配置，若未配置则也直接放行
            Integer preTaskId = item.getPreTaskId();
            if (preTaskId == null) {
                continue;
            }

            // 是否需要同步数据，若需要则需要如下逻辑判断
            if (isNeedSyncData) {
                // 3、判断数据是否已经同步，若已经同步则直接放行
                String previewIsSyncSuccess = item.getStringFromExtInfo(KEY_PREVIEW_IS_SYNC_SUCCESS);
                if (StringUtils.isNotBlank(previewIsSyncSuccess) && previewIsSyncSuccess.equals(YES)) {
                    continue;
                }

                // 4、查询前置任务是否已经完成，若已完成则进行放行
                CreativeTaskVO previewTask = selectById(preTaskId);
                if (previewTask != null && previewTask.getStatus().equals(CreativeStatusEnum.FINISHED)) {
                    fillPreviewResultToTask(previewTask, item);
                    continue;
                }
            }

            // 5、若含有isMultiProcess标识且为Y 并且 前置任务id配置则说明含有前置任务
            log.info("【多流程任务同步调度任务】多阶段任务的前置任务未完成，过滤当前任务, taskId={},依赖任务ID：{}",
                item.getId(), preTaskId);
            // 未完成的任务从列表中清处掉，继续等待前置任务完成
            iterator.remove();
        }
    }

    /**
     * 填充前置任务结果至二阶段任务
     *
     * @param previewTask 前置任务
     * @param item        当前任务
     */
    private void fillPreviewResultToTask(CreativeTaskVO previewTask, CreativeTaskVO item) {
        // 1、获取前置任务结果
        List<String> resultImages = previewTask.getResultImages();
        // 2、获取当前任务前置任务结果 Key
        String preTaskResultKey = item.getStringFromExtInfo(KEY_PRE_TASK_RESULT_KEY);
        // 3、获取当前任务前置任务结果 Size
        Integer preTaskResultSize = item.getIntegerFromExtInfo(KEY_PRE_TASK_RESULT_SIZE);

        if (CollectionUtils.isEmpty(resultImages) || StringUtils.isEmpty(preTaskResultKey)
            || preTaskResultSize == null) {
            log.error("【多阶段任务】【前置任务结果填充】CreativeBatchServiceImpl::fillPreviewResultToTask::前置任务resultImages"
                      + "或preTaskResultKey为空，resultImages：{} ,preTaskResultKey ：{},preTaskResultSize:{}数据同步终止...",
                resultImages, preTaskResultKey, preTaskResultSize);
            return;
        }

        // 4、设置二阶段任务结果
        fillPreTaskResultToItem(resultImages, preTaskResultKey, preTaskResultSize, item);
    }

    /**
     * 设置二阶段结果
     *
     * @param resultImages      结果图片
     * @param preTaskResultKey  前置任务结果Key
     * @param preTaskResultSize 前置任务结果 Size
     * @param item              当前任务
     */
    private void fillPreTaskResultToItem(List<String> resultImages, String preTaskResultKey, Integer preTaskResultSize,
                                         CreativeTaskVO item) {
        // 1、获取是否需要转换参数
        Boolean isNeedChangeToComfyUiPath = item.getBooleanFromExtInfo(KEY_IS_NEED_CHANGE_TO_COMFYUI_PATH);

        // 2、从结果集合中取出对应数量的图片
        if (CollectionUtils.isEmpty(resultImages) || resultImages.size() < preTaskResultSize) {
            log.error("【多阶段任务】【前置任务结果填充】fillPreTaskResultToItem::前置任务resultImages数量不足，resultImages.size()：{} ,"
                      + "preTaskResultSize ：{} 数据同步终止...", resultImages.size(), preTaskResultSize);
            return;
        }

        // 3、截取需要处理的图片列表
        List<String> originalImageList = resultImages.subList(0, preTaskResultSize);
        List<String> uploadedUrls = new ArrayList<>();
        if (isNeedChangeToComfyUiPath) {
            // 4、并行上传图片至 ComfyUI
            List<CompletableFuture<String>> uploadFutures = originalImageList.stream().map(
                imageUrl -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return comfyUIHelper.upLoadImageFromNetworkUrl(imageUrl);
                    } catch (IOException e) {
                        log.error("[多阶段任务][前置任务结果填充] 图片上传失败, imageUrl: {}. Error: {}", imageUrl,
                            e.getMessage());
                        return null; // 返回 null 表示失败
                    }
                })).collect(Collectors.toList());

            // 5、等待所有上传任务完成并收集结果
            CompletableFuture.allOf(uploadFutures.toArray(new CompletableFuture[0])).join();

            uploadedUrls = uploadFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());

            // 6、检查是否有上传失败的图片
            if (uploadedUrls.stream().anyMatch(Objects::isNull)) {
                log.error("[多阶段任务][前置任务结果填充] 部分或全部图片上传失败，数据同步终止, taskId: {}",
                    item.getId());
                return;
            }
        }

        // 7、填充任务结果
        if (preTaskResultSize == 1) {
            item.addExtInfo(preTaskResultKey,
                isNeedChangeToComfyUiPath ? uploadedUrls.get(0) : originalImageList.get(0));
        } else {
            item.addExtInfo(preTaskResultKey, isNeedChangeToComfyUiPath ? uploadedUrls : originalImageList);
        }
        // 统一使用列表存储原始图片
        item.addExtInfo(KEY_PRE_TASK_RESULT_LIST, originalImageList);

        // 7、打标标识该二级任务数据已经同步，后续不会再执行更新逻辑
        item.addExtInfo(KEY_PREVIEW_IS_SYNC_SUCCESS, YES);

        // 8、特殊情况做特殊处理
        specialOperator(item, preTaskResultKey);

        // 9、更新记录
        updateByIdSelective(item);
    }

    /**
     * 特殊情况进行特殊处理
     *
     * @param item             任务信息
     * @param preTaskResultKey 结果 Key
     */
    private void specialOperator(CreativeTaskVO item, String preTaskResultKey) {
        // 绘蛙需要进行特殊处理
        if (item.getType().equals(CreativeTypeEnum.HUIWA_BASIC_CHANGING_CLOTHES)) {
            huiwaSpecialOperator(item, preTaskResultKey);
        }

    }

    /**
     * 绘蛙基础款换衣任务则需要拿到结果之后立刻进行上传参考图并更新 CommonTask
     *
     * @param item             任务信息
     * @param preTaskResultKey 结果 Key
     */
    private void huiwaSpecialOperator(CreativeTaskVO item, String preTaskResultKey) {
        // 1、提取是否已经上传参考图
        Boolean isUploadHuiwaReference = item.getBooleanFromExtInfo(IS_UPLOAD_HUIWA_REFERENCE);
        // 若已经上传则直接终止二次上传操作
        if (isUploadHuiwaReference) {
            return;
        }

        // 2、参数提取
        Integer id = item.getId();
        String resultImage = item.getStringFromExtInfo(preTaskResultKey);

        // 3、获取 CommonTask 信息
        CommonTaskQuery query = new CommonTaskQuery();
        query.setRelatedBizId(String.valueOf(id));
        List<CommonTaskVO> commonTaskVOList = commonTaskService.queryCommonTaskList(query);
        if (CollectionUtils.isEmpty(commonTaskVOList) || commonTaskVOList.get(0) == null) {
            log.error("[多阶段任务][前置任务结果填充][绘蛙]taskId:{}关联CommonTask 任务不存在，结果同步终止...", id);
            return;
        }
        CommonTaskVO commonTaskVO = commonTaskVOList.get(0);

        // 4、绘蛙上传参考图获取 ModelId
        Long modelId = huiWaService.uploadPreferenceImage(resultImage);
        // 若 imageTaskId 为空则说明绘蛙创建任务失败
        if (modelId == null) {
            throw new BizException("绘蛙上传参考图任务创建失败！");
        }
        commonTaskVO.addExtInfo(KEY_PREFERENCE_MODEL_ID, modelId);
        commonTaskVO.setCreateTime(new Date());

        // 5、更新 CommonTask 创建任务请求入参：ModelId
        commonTaskService.updateByIdSelective(commonTaskVO);
    }

    /**
     * 绘蛙任务结果填充进入二阶段任务
     *
     * @param resultImages 结果图片集合
     * @param item         二阶段任务
     */
    private void huiwaTaskFillResultToItem(List<String> resultImages, CreativeTaskVO item) {
        // 绘蛙只取出一张图片
        String imageUrl = resultImages.get(0);

        // 1、图片处理 上传至 ComfyUI
        String uploadImageUrl = null;
        try {
            uploadImageUrl = comfyUIHelper.upLoadImageFromNetworkUrl(imageUrl);
        } catch (IOException e) {
            log.error(
                "[多阶段任务][前置任务结果填充]CreativeBatchServiceImpl::huiwaTaskFillResultToItem::结果图片上传失败，数据同步终止...");
            return;
        }

        // 2、更新换脸任务
        // 更新所需参数
        item.addExtInfo(KEY_TARGET_IMAGE, uploadImageUrl);
        // 存储原始图片
        item.addExtInfo(KEY_ORIGIN_IMAGE, imageUrl);
        // 打标标识该二级任务数据已经同步，后续不会再执行更新逻辑
        item.addExtInfo(KEY_PREVIEW_IS_SYNC_SUCCESS, YES);

        // 3、更新记录
        updateByIdSelective(item);
    }
}