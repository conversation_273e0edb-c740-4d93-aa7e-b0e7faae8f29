package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO;
import ai.conrain.aigc.platform.service.model.query.SearchResultImgQuery;
import ai.conrain.aigc.platform.dal.example.SearchResultImgExample;
import ai.conrain.aigc.platform.service.model.vo.SearchResultImgVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * SearchResultImgConverter
 *
 * @version SearchResultImgService.java v 0.1 2025-08-16 05:50:47
 */
public class SearchResultImgConverter {

    /**
     * DO -> VO
     */
    public static SearchResultImgVO do2VO(SearchResultImgDO from) {
        SearchResultImgVO to = new SearchResultImgVO();
        to.setId(from.getId());
        to.setSearchId(from.getSearchId());
        to.setSearchRetBatch(from.getSearchRetBatch());
        to.setImageId(from.getImageId());
        to.setImageUrl(from.getImageUrl());
        to.setImageShowUrl(from.getImageShowUrl());
        to.setImageCaptionId(from.getImageCaptionId());
        to.setGenre(from.getGenre());
        to.setBgClusterId(from.getBgClusterId());
        to.setStyleSimilarity(from.getStyleSimilarity());
        to.setMatchScore(from.getMatchScore());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static SearchResultImgDO vo2DO(SearchResultImgVO from) {
        SearchResultImgDO to = new SearchResultImgDO();
        to.setId(from.getId());
        to.setSearchId(from.getSearchId());
        to.setSearchRetBatch(from.getSearchRetBatch());
        to.setImageId(from.getImageId());
        to.setImageUrl(from.getImageUrl());
        to.setImageShowUrl(from.getImageShowUrl());
        to.setImageCaptionId(from.getImageCaptionId());
        to.setGenre(from.getGenre());
        to.setBgClusterId(from.getBgClusterId());
        to.setStyleSimilarity(from.getStyleSimilarity());
        to.setMatchScore(from.getMatchScore());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static SearchResultImgExample query2Example(SearchResultImgQuery from) {
        SearchResultImgExample to = new SearchResultImgExample();
        SearchResultImgExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getSearchId())) {
            c.andSearchIdEqualTo(from.getSearchId());
        }
        if (!ObjectUtils.isEmpty(from.getSearchRetBatch())) {
            c.andSearchRetBatchEqualTo(from.getSearchRetBatch());
        }
        if (!ObjectUtils.isEmpty(from.getImageId())) {
            c.andImageIdEqualTo(from.getImageId());
        }
        if (!ObjectUtils.isEmpty(from.getImageUrl())) {
            c.andImageUrlEqualTo(from.getImageUrl());
        }
        if (!ObjectUtils.isEmpty(from.getImageShowUrl())) {
            c.andImageShowUrlEqualTo(from.getImageShowUrl());
        }
        if (!ObjectUtils.isEmpty(from.getImageCaptionId())) {
            c.andImageCaptionIdEqualTo(from.getImageCaptionId());
        }
        if (!ObjectUtils.isEmpty(from.getGenre())) {
            c.andGenreEqualTo(from.getGenre());
        }
        if (!ObjectUtils.isEmpty(from.getBgClusterId())) {
            c.andBgClusterIdEqualTo(from.getBgClusterId());
        }
        if (!ObjectUtils.isEmpty(from.getStyleSimilarity())) {
            c.andStyleSimilarityEqualTo(from.getStyleSimilarity());
        }
        if (!ObjectUtils.isEmpty(from.getMatchScore())) {
            c.andMatchScoreEqualTo(from.getMatchScore());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<SearchResultImgVO> doList2VOList(List<SearchResultImgDO> list) {
        return CommonUtil.listConverter(list, SearchResultImgConverter::do2VO);
    }
}