package ai.conrain.aigc.platform.service.component.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.UserPointDAO;
import ai.conrain.aigc.platform.dal.entity.UserPointDO;
import ai.conrain.aigc.platform.dal.example.UserPointExample;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.ModelPointService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserPointLogService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.PricePlanHelper;
import ai.conrain.aigc.platform.service.model.biz.PredictVO;
import ai.conrain.aigc.platform.service.model.biz.PricePlanCode;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserPointConverter;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.PricePlan;
import ai.conrain.aigc.platform.service.model.vo.UserPointLogVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.component.impl.ModelPointServiceImpl.INIT_MODEL_POINT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_CREATIVE_UPLOAD;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_USER_POINT_DEDUCT_FLAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_USER_POINT_RETURN_FLAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;

/**
 * UserPointService实现
 *
 * <AUTHOR>
 * @version UserPointService.java v 0.1 2024-05-15 10:58:45
 */
@Slf4j
@Service
public class UserPointServiceImpl implements UserPointService {

    /** DAO */
    @Autowired
    private UserPointDAO userPointDAO;
    @Autowired
    private ModelPointService modelPointService;
    @Autowired
    private UserPointLogService userPointLogService;
    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private PricePlanHelper pricePlanHelper;
    @Autowired
    private UserService userService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public UserPointVO selectByUserId(Integer userId) {
        // 参数校验
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");

        // DO对象 转换为 VO对象
        return UserPointConverter.do2VO(selectDOByUserId(userId));
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = userPointDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除UserPoint失败");
    }

    @Override
    public UserPointVO insert(UserPointVO userPoint) {
        AssertUtil.assertNotNull(userPoint, ResultCode.PARAM_INVALID, "userPoint is null");
        AssertUtil.assertTrue(userPoint.getId() == null, ResultCode.PARAM_INVALID, "userPoint.id is present");

        //创建时间、修改时间兜底
        if (userPoint.getCreateTime() == null) {
            userPoint.setCreateTime(new Date());
        }

        if (userPoint.getModifyTime() == null) {
            userPoint.setModifyTime(new Date());
        }

        UserPointDO data = UserPointConverter.vo2DO(userPoint);
        int n = userPointDAO.insertSelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建UserPoint失败");
        AssertUtil.assertNotNull(data.getId(), "新建UserPoint返回id为空");
        userPoint.setId(data.getId());
        return userPoint;
    }

    @Override
    public void updateByIdSelective(UserPointVO userPoint) {
        AssertUtil.assertNotNull(userPoint, ResultCode.PARAM_INVALID, "userPoint is null");
        AssertUtil.assertTrue(userPoint.getId() != null, ResultCode.PARAM_INVALID, "userPoint.id is null");

        //修改时间必须更新
        userPoint.setModifyTime(new Date());
        UserPointDO data = UserPointConverter.vo2DO(userPoint);
        int n = userPointDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新UserPoint失败，影响行数:" + n);
    }

    @Override
    public List<UserPointVO> queryUserPointList(UserPointQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserPointExample example = UserPointConverter.query2Example(query);

        List<UserPointVO> list = UserPointConverter.doList2VOList(userPointDAO.selectByExample(example));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setImagePoint(CommonUtil.point2MusePoint(item.getPoint()));
            });
        }
        return list;
    }

    @Override
    public Long queryUserPointCount(UserPointQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserPointExample example = UserPointConverter.query2Example(query);
        return userPointDAO.countByExample(example);
    }

    /**
     * 带条件分页查询用户算力点
     */
    @Override
    public PageInfo<UserPointVO> queryUserPointByPage(UserPointQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<UserPointVO> page = new PageInfo<>();

        UserPointExample example = UserPointConverter.query2Example(query);
        long totalCount = userPointDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<UserPointDO> list = userPointDAO.selectByExample(example);
        page.setList(UserPointConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public UserPointVO queryImagePoint(Integer userId) {
        // 查询用户算力点
        UserPointVO data = selectByUserId(userId);

        // 用户不存在
        if (data == null) {
            log.info("用户{}算力点表不存在，直接返回0", userId);
            data = new UserPointVO();
            data.setExperiencePoint(0);
            data.setGivePoint(0);
            data.setImagePoint(BigDecimal.ZERO);
            return data;
        }

        // 图片算力点设置
        data.setImagePoint(CommonUtil.point2MusePoint(data.getPoint()));

        // 返回算力点记录
        return data;
    }

    @Override
    public PredictVO predict(Integer modelId, Integer imageNum, CreativeTypeEnum type, Integer timeSecs4Video,
                             boolean isUpload, ProportionTypeEnum proportionType) {
        return calcPredict(type, modelId, imageNum, false, timeSecs4Video, isUpload, proportionType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consumeByImage(CreativeBatchVO data) {

        //参数准备
        Integer userId = data.getUserId();
        Integer modelId = data.getModelId();
        Integer imageNum = data.getBatchCnt();
        Integer operatorId = data.getOperatorId();
        Integer bizId = data.getId();
        ProportionTypeEnum proportionType = ProportionTypeEnum.getByCode(data.getImageProportion());

        if (data.getModelType() == ModelTypeEnum.SYSTEM) {
            consumeBySystemImage(userId, imageNum, bizId, operatorId);
            return;
        }

        boolean isUpload = StringUtils.equals(YES, data.getExtValue(KEY_IS_CREATIVE_UPLOAD, String.class));

        PredictVO predict = calcPredict(data.getType(), modelId, imageNum, true, data.getTimeSecs4Video(), isUpload,
            proportionType);
        if (predict.isNeedTopup()) {
            log.warn("用户余额不足，userId={},modelId={},predict={}", userId, modelId, predict);
            throw new BizException(ResultCode.MUSE_POINT_OVER_LIMIT, "购买图片失败，请充值");
        }

        UserPointLogVO pointLog = new UserPointLogVO();

        //2.先扣除服装套餐内积分，服装套餐内积分1:1
        Integer pointM = predict.getModelPoint();
        if (pointM != null) {
            ModelPointVO modelPoint = predict.getModelPointVO();
            modelPoint.setPoint(modelPoint.getPoint() - pointM);
            modelPointService.updateByIdSelective(modelPoint);

            pointLog.setModelPoint(-pointM);
            pointLog.setOriginModelPoint(modelPoint.getPoint() + pointM);
            pointLog.setTargetModelPoint(modelPoint.getPoint());
        }

        //3.如果图片套餐内积分不够扣除，则扣除赠送积分
        Integer pointG = predict.getGivePoint();
        BigDecimal pointI = predict.getMusePoint();
        if (pointG != null || pointI != null) {
            UserPointDO userPoint = predict.getUserPointDO();
            if (pointG != null) {
                userPoint.setGivePoint(userPoint.getGivePoint() - pointG);

                pointLog.setGivePoint(-pointG);
                pointLog.setOriginGivePoint(userPoint.getGivePoint() + pointG);
                pointLog.setTargetGivePoint(userPoint.getGivePoint());
            }

            if (pointI != null) {
                userPoint.setPoint(userPoint.getPoint() - (CommonUtil.musePoint2Point(pointI)));

                pointLog.setPoint(-CommonUtil.musePoint2Point(pointI));
                pointLog.setOriginPoint(userPoint.getPoint() + pointLog.getPoint());
                pointLog.setTargetPoint(userPoint.getPoint());
            }

            int cnt = userPointDAO.updateByPrimaryKey(userPoint);
            AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "更新用户算力点失败");
        }

        log.info("生成图片，userId={},imageNum={}，消耗用户点数={}", userId, imageNum, predict);

        //记录流水
        pointLog.setUserId(userId);
        pointLog.setOperatorId(operatorId);
        pointLog.setType(data.getType().getLogType());
        pointLog.setRelatedId(bizId);
        pointLog.setModelName(data.getModelName());
        userPointLogService.insert(pointLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revertByImage(CreativeBatchVO data) {
        Integer userId = data.getUserId();
        ModelTypeEnum type = data.getModelType();
        Integer imageNum = data.getBatchCnt();
        Integer operatorId = data.getOperatorId();
        Integer bizId = data.getId();

        if (type == ModelTypeEnum.SYSTEM) {
            revertBySystemImage(userId, imageNum, bizId, operatorId);
            return;
        }

        UserPointLogVO originLog = userPointLogService.lockByBizId(bizId, userId, data.getType().getLogType());

        if (StringUtils.equals(YES, originLog.getExtInfo(KEY_USER_POINT_RETURN_FLAG))) {
            log.warn("图片退回失败，图片点数已退回，userId={},originLog={}", userId, originLog);
            throw new BizException(ResultCode.BIZ_FAIL, "图片退回失败，图片点数已退回");
        }

        UserPointLogVO pointLog = new UserPointLogVO();

        if (originLog.getModelPoint() != null) {
            //originLog.getModelPoint()是负数
            Integer revertModelPoint = -originLog.getModelPoint();
            ModelPointVO modelPoint = modelPointService.lockByModelId(data.getModelId(), userId);
            modelPoint.setPoint(modelPoint.getPoint() + revertModelPoint);
            modelPointService.updateByIdSelective(modelPoint);
            log.info("退回服装套餐内图片张数，userId={},bizId={},point={}", userId, data.getId(), revertModelPoint);

            pointLog.setModelPoint(revertModelPoint);
            pointLog.setOriginModelPoint(modelPoint.getPoint() - revertModelPoint);
            pointLog.setTargetModelPoint(modelPoint.getPoint());
        }

        int revertPoint = 0;
        if (originLog.getPoint() != null) {
            revertPoint = -originLog.getPoint();
        }

        int revertGivePoint = 0;
        if (originLog.getGivePoint() != null) {
            revertGivePoint = -originLog.getGivePoint();
        }

        if (revertPoint > 0 || revertGivePoint > 0) {
            UserPointDO userPoint = userPointDAO.lockByUserId(userId);
            userPoint.setPoint(userPoint.getPoint() + revertPoint);
            userPoint.setGivePoint(userPoint.getGivePoint() + revertGivePoint);
            int cnt = userPointDAO.updateByPrimaryKey(userPoint);
            AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "更新用户算力点失败");
            log.info("退回缪斯点/赠送点数，userId={},bizId={},revertPoint={},revertGivePoint={}", userId, data.getId(),
                revertPoint, revertGivePoint);

            pointLog.setPoint(revertPoint);
            pointLog.setOriginPoint(userPoint.getPoint() - revertPoint);
            pointLog.setTargetPoint(userPoint.getPoint());

            pointLog.setGivePoint(revertGivePoint);
            pointLog.setOriginGivePoint(userPoint.getGivePoint() - revertGivePoint);
            pointLog.setTargetGivePoint(userPoint.getGivePoint());
        }

        //增加标记
        originLog.addExtInfo(KEY_USER_POINT_RETURN_FLAG, YES);
        userPointLogService.updateByIdSelective(originLog);

        log.info("退回用户点数，userId={},type={},imageNum={}", userId, type, imageNum);

        //记录流水
        pointLog.setUserId(userId);
        pointLog.setOperatorId(operatorId);
        pointLog.setType(data.getType().getReturnLogType());
        pointLog.setRelatedId(bizId);
        userPointLogService.insert(pointLog);
    }

    /**
     * 衣服退点：恢复lora训练的muse点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revertByTrainModel(MaterialModelVO model) {
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "model不能为空");

        Integer modelId = model.getId();
        Integer userId = model.getUserId();
        UserPointLogVO originLog = userPointLogService.lockByBizId(modelId, userId, PointLogTypeEnum.LORA_TRAIN);

        UserPointLogVO pointLog = new UserPointLogVO();

        int revertPoint = 0;
        if (originLog.getPoint() != null) {
            revertPoint = -originLog.getPoint();
        }

        if (revertPoint > 0) {
            UserPointDO userPoint = userPointDAO.lockByUserId(userId);
            userPoint.setPoint(userPoint.getPoint() + revertPoint);
            int cnt = userPointDAO.updateByPrimaryKey(userPoint);
            AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "更新用户算力点失败");
            log.info("退回缪斯点数，userId={},modelId={},revertPoint={}", userId, modelId, revertPoint);

            pointLog.setPoint(revertPoint);
            pointLog.setOriginPoint(userPoint.getPoint() - revertPoint);
            pointLog.setTargetPoint(userPoint.getPoint());
        }else {
            log.warn("模型退点失败，无可退回点数。userId = {},modelId = {},revertPoint = {} ", userId, modelId, revertPoint);
            throw new BizException(ResultCode.POINT_CALCULATION_ERROR);
        }

        log.info("退回用户点数，userId={},type={},musePoint={}", userId, PointLogTypeEnum.LORA_TRAIN.getCode(),
            CommonUtil.point2MusePoint(revertPoint));

        //记录流水
        //流水日志中增加退点成功标记
        pointLog.addExtInfo(KEY_USER_POINT_RETURN_FLAG, YES);
        pointLog.setUserId(userId);
        pointLog.setOperatorId(OperationContextHolder.getOperatorUserId());
        pointLog.setType(PointLogTypeEnum.LORA_TRAIN_RETURN);
        pointLog.setRelatedId(modelId);
        userPointLogService.insert(pointLog);
    }

    /**
     * 衣服审核不通过后，重新审核通过，进行扣除训练的muse点
     * @param model 现在的衣服lora模型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductByTrainModel(MaterialModelVO model) {
        AssertUtil.assertNotNull(model, ResultCode.PARAM_INVALID, "model不能为空");

        Integer modelId = model.getId();
        Integer userId = model.getUserId();
        // 这个模型的lora训练流水记录
        UserPointLogVO originLog = userPointLogService.lockByBizId(modelId, userId, PointLogTypeEnum.LORA_TRAIN);

        UserPointLogVO pointLog = new UserPointLogVO();
        // 计算要扣除的点数
        int deDuctPoint = 0;
        if (originLog.getPoint() != null) {
            // 重新审核通过要扣除的muse点 = 训练lora时扣除的muse点（因为审核不通过的时候已经给他退点了）
            deDuctPoint = originLog.getPoint();
        }

        if (deDuctPoint < 0) {
            UserPointDO userPoint = userPointDAO.lockByUserId(userId);
            // 扣除muse点
            userPoint.setPoint(userPoint.getPoint() + deDuctPoint);
            int cnt = userPointDAO.updateByPrimaryKey(userPoint);
            AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "更新用户算力点失败");
            log.info("扣除缪斯点数，userId={},modelId={},revertPoint={}", userId, modelId, deDuctPoint);

            pointLog.setPoint(deDuctPoint);
            pointLog.setOriginPoint(userPoint.getPoint() - deDuctPoint);
            pointLog.setTargetPoint(userPoint.getPoint());
        }else {
            log.warn("模型退点失败，无可扣除点数。userId = {},modelId = {},revertPoint = {} ", userId, modelId, deDuctPoint);
            throw new BizException(ResultCode.POINT_CALCULATION_ERROR);
        }

        log.info("重新审核通过并扣除用户点数，userId={},type={},musePoint={}", userId, PointLogTypeEnum.LORA_TRAIN.getCode(),
            CommonUtil.point2MusePoint(deDuctPoint));

        //记录流水
        // 新加的流水记录添加额外信息，表示被扣点了
        pointLog.addExtInfo(KEY_USER_POINT_DEDUCT_FLAG, YES);
        pointLog.setUserId(userId);
        pointLog.setOperatorId(OperationContextHolder.getOperatorUserId());
        pointLog.setType(PointLogTypeEnum.LORA_TRAIN_DEDUCT);
        pointLog.setRelatedId(modelId);
        userPointLogService.insert(pointLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void topupByImage(Integer userId, Integer imageNum, Integer experiencePoint, Integer givePoint) {
        //  查询指定 id 的用户信息
        UserPointDO userPointDO = userPointDAO.lockByUserId(userId);
        //        boolean create = userPointDO == null;
        boolean create = Objects.isNull(userPointDO);

        // 构造用户积分对象
        UserPointVO userPointVO = null;
        if (create) {
            userPointVO = new UserPointVO();
            userPointVO.setUserId(userId);
            userPointVO.setPoint(0);
            userPointVO.setGivePoint(0);
            userPointVO.setExperiencePoint(0);
        } else {
            userPointVO = UserPointConverter.do2VO(userPointDO);
        }

        Integer originPoint = userPointVO.getPoint();
        Integer originExperiencePoint = userPointVO.getExperiencePoint();
        Integer originGivePoint = userPointVO.getGivePoint();

        userPointVO.setPoint(imageNum * CommonConstants.MUSE_POINT_COEFFICIENT);
        userPointVO.setExperiencePoint(experiencePoint);
        userPointVO.setGivePoint(givePoint);

        if (create) {
            insert(userPointVO);
        } else {
            updateByIdSelective(userPointVO);
        }

        //记录log表
        UserPointLogVO log = new UserPointLogVO();
        log.setUserId(userId);
        log.setType(PointLogTypeEnum.RECHARGE_MANUAL);

        log.setPoint(userPointVO.getPoint() - originPoint);
        log.setOriginPoint(originPoint);
        log.setTargetPoint(userPointVO.getPoint());

        log.setExperiencePoint(userPointVO.getExperiencePoint() - originExperiencePoint);
        log.setOriginExperiencePoint(originExperiencePoint);
        log.setTargetExperiencePoint(userPointVO.getExperiencePoint());

        log.setGivePoint(userPointVO.getGivePoint() - originGivePoint);
        log.setOriginGivePoint(originGivePoint);
        log.setTargetGivePoint(userPointVO.getGivePoint());

        // 插入积分记录
        userPointLogService.insert(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rechargeByPay(OrderInfoVO order) {
        AssertUtil.assertNotNull(order, ResultCode.PARAM_INVALID, "order is null");

        //查询充值计划详情
        Integer userId = order.getMasterUserId();
        Integer bizId = order.getId();
        String productCode = order.getProductCode();

        log.info("充值开始,userId={},productCode={},orderNo={}", userId, productCode, order.getOrderNo());

        Integer topupMusePoint = null;
        Integer creativeImgCountGave = null;

        //后台录入转账
        if (CommonUtil.isValidJson(order.getProductDetail())) {
            JSONObject productDetail = JSONObject.parseObject(order.getProductDetail());
            if (productDetail != null && productDetail.containsKey(CommonConstants.MUSE_POINT)) {
                topupMusePoint = productDetail.getInteger(CommonConstants.MUSE_POINT);
            }
            if (productDetail != null && productDetail.containsKey(CommonConstants.GAVE_IMG_COUNT)) {
                creativeImgCountGave = productDetail.getInteger(CommonConstants.GAVE_IMG_COUNT);
            }
        }

        //客户在前台充值
        if (topupMusePoint == null && creativeImgCountGave == null && PricePlanCode.getByCode(productCode) != null) {
            PricePlan pricePlan = pricePlanHelper.queryPricePlanByCode(productCode, userId);
            AssertUtil.assertNotNull(pricePlan, ResultCode.PARAM_INVALID, "productCode is invalid");
            topupMusePoint = pricePlan.getMusePoint();
            creativeImgCountGave = pricePlan.getCreativeImgCountGave();
        }

        if (topupMusePoint == null) {
            topupMusePoint = 0;
        }
        if (creativeImgCountGave == null) {
            creativeImgCountGave = 0;
        }

        if (topupMusePoint <= 0 && creativeImgCountGave <= 0) {
            log.info(
                "当前充值配置不需要充值点数和赠送图片数，直接返回，productCode:{}, musePoint:{}, creativeImgCountGave:{}",
                productCode, topupMusePoint, creativeImgCountGave);
            return;
        }

        UserPointDO userPointDO = userPointDAO.lockByUserId(userId);
        boolean create = userPointDO == null;

        UserPointVO userPointVO = null;
        if (create) {
            userPointVO = new UserPointVO();
            userPointVO.setUserId(userId);
            userPointVO.setPoint(0);
            userPointVO.setGivePoint(0);
        } else {
            userPointVO = UserPointConverter.do2VO(userPointDO);
            if (userPointVO.getPoint() == null) {
                userPointVO.setPoint(0);
            }
            if (userPointVO.getGivePoint() == null) {
                userPointVO.setGivePoint(0);
            }
        }

        int rechargePoint = topupMusePoint * CommonConstants.MUSE_POINT_COEFFICIENT;

        userPointVO.setPoint(userPointVO.getPoint() + rechargePoint);
        userPointVO.setGivePoint(userPointVO.getGivePoint() + creativeImgCountGave);

        if (create) {
            insert(userPointVO);
        } else {
            updateByIdSelective(userPointVO);
        }

        log.info("充值成功,userId={},productCode={},orderNo={}", userId, productCode, order.getOrderNo());
        DingTalkNoticeHelper.sendMsg2BizGroup(
            "点数充值成功\n用户：" + order.getMasterUserNick() + "\n充值缪斯点：" + topupMusePoint + " \n赠送图片："
            + creativeImgCountGave);

        //记录流水
        UserPointLogVO log = new UserPointLogVO();
        log.setUserId(userId);
        log.setType(PointLogTypeEnum.RECHARGE);
        log.setRelatedId(bizId);
        log.setOperatorId(order.getOperatorUserId());

        log.setPoint(rechargePoint);
        log.setOriginPoint(userPointVO.getPoint() - rechargePoint);
        log.setTargetPoint(userPointVO.getPoint());

        log.setGivePoint(creativeImgCountGave);
        log.setOriginGivePoint(userPointVO.getGivePoint() - creativeImgCountGave);
        log.setTargetGivePoint(userPointVO.getGivePoint());

        userPointLogService.insert(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consumeByTrainModel(Integer userId, MaterialModelVO model, boolean exclusiveElementModel) {
        UserPointDO data = userPointDAO.lockByUserId(userId);
        AssertUtil.assertNotNull(data, ResultCode.BIZ_FAIL, "用户积分点不存在");

        AssertUtil.assertNotNull(model, ResultCode.BIZ_FAIL, "衣服模型为空");
        Integer modelId = model.getId();

        int consumePoint = CommonUtil.getNeedPoint4Train(model, exclusiveElementModel);

        if (data.getPoint() < consumePoint) {
            log.warn("训练lora扣减缪斯点失败，点数不足，modelId={},userId={},current={},consume={}", modelId, modelId,
                data.getPoint(), consumePoint);
            throw new BizException(ResultCode.IMAGE_POINT_OVER_LIMIT);
        }

        int originPoint = data.getPoint();
        data.setPoint(data.getPoint() - consumePoint);

        int n = userPointDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新用户算力点失败");

        //初始化模型套餐内积分
        if (model.getMaterialType() == MaterialType.cloth) {
            modelPointService.init(modelId, userId);
        }

        log.info("训练lora扣减缪斯点成功，modelId={},userId={},current={},consume={}", modelId, userId, data.getPoint(),
            consumePoint);

        //记录log表
        UserPointLogVO log = new UserPointLogVO();
        log.setUserId(userId);
        log.setType(PointLogTypeEnum.LORA_TRAIN);
        log.setPoint(-consumePoint);
        log.addExtInfo(CommonConstants.multiColors, model.getClothLoraTrainDetail().getMultiColors());
        log.addExtInfo(CommonConstants.materialType, model.getMaterialType().name());

        //渠道商代拍上传服装训练
        if (model.isAgentUploadModel()) {
            log.addExtInfo(CommonConstants.uploadByAgentId,
                model.getExtInfo().getInteger(CommonConstants.uploadByAgentId));
            log.addExtInfo(CommonConstants.uploadByAgentName,
                model.getExtInfo().getString(CommonConstants.uploadByAgentName));
            log.addExtInfo(CommonConstants.uploadByAgentMasterId,
                model.getExtInfo().getInteger(CommonConstants.uploadByAgentMasterId));
            log.addExtInfo(CommonConstants.uploadByAgentMasterName,
                model.getExtInfo().getString(CommonConstants.uploadByAgentMasterName));
            log.addExtInfo(CommonConstants.uploadByAgentRole,
                model.getExtInfo().getString(CommonConstants.uploadByAgentRole));

            log.setOperatorId(userId);

            //默认情况
        } else {
            log.setOperatorId(OperationContextHolder.getOperatorUserId());
        }

        if (model.getExtInfo() != null && model.getExtInfo().containsKey(CommonConstants.KEY_IRONING_CLOTH)) {
            log.addExtInfo(CommonConstants.KEY_IRONING_CLOTH,
                model.getExtInfo().getString(CommonConstants.KEY_IRONING_CLOTH));
        }

        log.setModelPoint(INIT_MODEL_POINT);
        log.setOriginModelPoint(0);
        log.setTargetModelPoint(INIT_MODEL_POINT);
        log.setRelatedId(modelId);
        log.setOriginPoint(originPoint);
        log.setTargetPoint(data.getPoint());
        log.setModelName(model.getName());

        userPointLogService.insert(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustGivePoint(Integer userId, int experiencePoint, int givePoint) {
        AssertUtil.assertNotNull(userId, "userId is null");

        // 查询用户信息
        UserPointDO userPointDO = userPointDAO.lockByUserId(userId);
        if (userPointDO == null) {
            AssertUtil.assertTrue(experiencePoint >= 0 && givePoint >= 0, "用户积分点不存在，必须是非负数");
            this.setGivePointAndExperiencePoint(userId, experiencePoint, givePoint);
        } else {
            Integer targetExperiencePoint = ObjectUtils.defaultIfNull(userPointDO.getExperiencePoint(), 0)
                                            + experiencePoint;
            Integer targetGivePoint = ObjectUtils.defaultIfNull(userPointDO.getGivePoint(), 0) + givePoint;
            this.setGivePointAndExperiencePoint(userId, targetExperiencePoint, targetGivePoint);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setGivePointAndExperiencePoint(Integer userId, Integer experiencePoint, Integer givePoint) {
        // 查询用户信息
        UserPointDO userPointDO = userPointDAO.lockByUserId(userId);

        // 校验用户是否存在
        boolean create = Objects.isNull(userPointDO);

        UserPointVO userPointVO = null;
        if (create) {
            userPointVO = new UserPointVO();
            userPointVO.setUserId(userId);
            userPointVO.setPoint(0);
            userPointVO.setGivePoint(0);
            userPointVO.setExperiencePoint(0);
        } else {
            userPointVO = UserPointConverter.do2VO(userPointDO);
        }

        Integer originExperiencePoint = userPointVO.getExperiencePoint();
        Integer originGivePoint = userPointVO.getGivePoint();

        userPointVO.setExperiencePoint(experiencePoint != null ? experiencePoint : originExperiencePoint);
        userPointVO.setGivePoint(givePoint != null ? givePoint : originGivePoint);

        if (create) {
            insert(userPointVO);
        } else {
            updateByIdSelective(userPointVO);
        }

        //记录log表
        UserPointLogVO log = new UserPointLogVO();
        log.setUserId(userId);
        log.setType(PointLogTypeEnum.RECHARGE_MANUAL);

        log.setExperiencePoint(userPointVO.getExperiencePoint() - originExperiencePoint);
        log.setOriginExperiencePoint(originExperiencePoint);
        log.setTargetExperiencePoint(userPointVO.getExperiencePoint());

        log.setGivePoint(userPointVO.getGivePoint() - originGivePoint);
        log.setOriginGivePoint(originGivePoint);
        log.setTargetGivePoint(userPointVO.getGivePoint());

        // 执行插入
        userPointLogService.insert(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustMuse(Integer userId, BigDecimal musePoint, String memo) {
        UserPointDO userPointDO = userPointDAO.lockByUserId(userId);
        boolean create = userPointDO == null;

        UserPointVO userPointVO = null;
        if (create) {
            userPointVO = new UserPointVO();
            userPointVO.setUserId(userId);
            userPointVO.setPoint(0);
            userPointVO.setGivePoint(0);
            userPointVO.setExperiencePoint(0);
        } else {
            userPointVO = UserPointConverter.do2VO(userPointDO);
        }

        Integer originPoint = userPointVO.getPoint();

        int adjustPoint = musePoint.multiply(BigDecimal.valueOf(CommonConstants.MUSE_POINT_COEFFICIENT)).intValue();
        userPointVO.setPoint(originPoint + adjustPoint);

        if (create) {
            insert(userPointVO);
        } else {
            updateByIdSelective(userPointVO);
        }

        //记录log表
        UserPointLogVO log = new UserPointLogVO();
        log.setUserId(userId);
        log.setType(PointLogTypeEnum.ADJUST_MANUAL);
        log.setMemo(memo);

        log.setPoint(userPointVO.getPoint() - originPoint);
        log.setOriginPoint(originPoint);
        log.setTargetPoint(userPointVO.getPoint());
        log.setOperatorId(OperationContextHolder.getOperatorUserId());

        userPointLogService.insert(log);
    }

    @Override
    public Map<Integer, Integer> queryUserAccumulatedRechargeByUserIdList(List<Integer> userIdList) {
        return userPointDAO.queryUserAccumulatedRechargeByUserIdList(userIdList);
    }



    /**
     * 计算预估生成图片所需积分
     *
     * @param type           创作类型
     * @param modelId        模型id
     * @param imageNum       图片张数
     * @param needLock       是否需要锁记录
     * @param isUpload       是否上传
     * @param proportionType 图片尺寸
     * @return 预估生成图片所需要的积分
     */
    private PredictVO calcPredict(CreativeTypeEnum type, Integer modelId, Integer imageNum, boolean needLock,
                                  Integer timeSecs4Video, boolean isUpload, ProportionTypeEnum proportionType) {

        if (OperationContextHolder.isDistributorRole() || OperationContextHolder.isAdmin()
            || systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
            OperationContextHolder.getOperatorUserId())) {
            PredictVO predict = new PredictVO();
            predict.setMusePoint(BigDecimal.ZERO);
            return predict;
        }

        if (type == CreativeTypeEnum.REPAIR_DETAIL || type == CreativeTypeEnum.PARTIAL_REDRAW
            || type == CreativeTypeEnum.REPAIR_HANDS || type == CreativeTypeEnum.REMOVE_WRINKLE
            || type == CreativeTypeEnum.ERASE_BRUSH || type == CreativeTypeEnum.BRAND_TRY_ON) {
            return calcOtherPredict3(type, imageNum, needLock);
        }

        if (type == CreativeTypeEnum.FACE_SCENE_SWITCH) {
            return calcOtherPredict2(type, imageNum, needLock);
        }

        //视频创作
        if (type == CreativeTypeEnum.CREATE_VIDEO) {
            AssertUtil.assertTrue(timeSecs4Video != null && (timeSecs4Video == 5 || timeSecs4Video == 10),
                ResultCode.BIZ_FAIL, "视频时长参数错误：" + timeSecs4Video);
            return predict4Video(type, imageNum, needLock, timeSecs4Video);
        }

        // 固定姿势额外消耗两倍的图片
        if (type == CreativeTypeEnum.FIXED_POSTURE_CREATION) {
            imageNum = imageNum * 2;
        }

        //其余（非创作、固定姿势创作）
        if (type != CreativeTypeEnum.CREATE_IMAGE && type != CreativeTypeEnum.FIXED_POSTURE_CREATION) {
            return calcOtherPredict(type, imageNum, needLock, isUpload);
        }

        //去创作，出图
        return predict4CreateImage(type, modelId, imageNum, needLock, proportionType);
    }

    @NotNull
    private PredictVO predict4Video(CreativeTypeEnum type, Integer imageNum, boolean needLock, Integer timeSecs4Video) {
        //参数准备
        PredictVO predict = new PredictVO();
        Integer userId = OperationContextHolder.getMasterUserId();

        //扣款顺序：直接扣0.4 缪斯点
        //1.锁用户积分记录
        UserPointDO userPoint = needLock ? userPointDAO.lockByUserId(userId) : selectDOByUserId(userId);
        predict.setUserPointDO(userPoint);

        if (userPoint == null) {
            log.warn("未查询到用户积分记录,userId={},此时用户需要充值", userId);
            predict.setNeedTopup(true);
            return predict;
        }

        predict.setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));
        //2. 计算所需的缪斯点数量
        BigDecimal fac = BigDecimal.valueOf(timeSecs4Video == 10 ? imageNum * 2 : imageNum);
        int remain = CommonUtil.musePoint2Point(type.getConsumeMusePoints().multiply(fac));

        //3.1.如果剩余积分不足，则直接抛异常
        if (userPoint.getPoint() < remain) {
            log.warn("当前积分点数不足，需要充值，userId={},图片数量={}", userId, imageNum);
            predict.setMusePoint(CommonUtil.point2MusePoint(remain));
            predict.setNeedTopup(true);
            return predict;
        }
        predict.setMusePoint(CommonUtil.point2MusePoint(remain));
        return predict;
    }

    @NotNull
    private PredictVO predict4CreateImage(CreativeTypeEnum type, Integer modelId, Integer imageNum, boolean needLock,
                                          ProportionTypeEnum proportionType) {
        //参数准备
        PredictVO predict = new PredictVO();
        Integer userId = OperationContextHolder.getMasterUserId();

        imageNum = proportionType == ProportionTypeEnum.P_1620_2100 ? imageNum * 2 : imageNum;

        //扣款顺序：服装套餐内积分 > 赠送积分 > 缪斯点

        //1.锁服装套餐内积分记录
        ModelPointVO modelPoint = needLock ? modelPointService.lockByModelId(modelId, userId)
            : modelPointService.selectByModelId(modelId, userId);
        if (modelPoint == null) {
            MaterialModelVO modelVO = materialModelService.selectById(modelId);
            if (OperationContextHolder.isBackRole() || modelVO.getType() == ModelTypeEnum.SYSTEM) {
                modelPoint = new ModelPointVO();
            } else {
                log.warn("未查询到服装套餐内积分记录,userId={},modelId={}", userId, modelId);
                throw new BizException(ResultCode.BIZ_FAIL);
            }
        } else {
            AssertUtil.assertFrontPermission(modelPoint.getUserId(), "权限检查不通过，非当前模型所有者");
        }

        int remain = imageNum;
        int deduct = modelPoint.getPoint() > remain ? remain : modelPoint.getPoint();
        predict.setCurrentModelPoint(modelPoint.getPoint());

        //2.先扣除服装套餐内积分，服装套餐内积分1:1
        if (deduct > 0) {
            predict.setModelPoint(deduct);
            predict.setModelPointVO(modelPoint);
        }

        remain = remain - deduct;
        if (remain == 0) {
            return predict;
        }

        //3.如果图片套餐内积分不够扣除，则扣除赠送积分

        //3.1.锁用户积分记录
        UserPointDO userPoint = needLock ? userPointDAO.lockByUserId(userId) : selectDOByUserId(userId);
        predict.setUserPointDO(userPoint);

        if (userPoint == null) {
            log.warn("未查询到用户积分记录,userId={},此时用户需要充值", userId);
            predict.setNeedTopup(true);
            return predict;
        }

        predict.setCurrentGivePoint(userPoint.getGivePoint());
        predict.setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));

        deduct = userPoint.getGivePoint() > remain ? remain : userPoint.getGivePoint();
        if (deduct > 0) {
            predict.setGivePoint(deduct);
        }

        //4.如果还是不够扣除，则扣除缪斯点
        remain = remain - deduct;
        if (remain == 0) {
            return predict;
        }

        //4.1.如果剩余积分不足（0.4个缪斯点一张图），则直接抛异常
        int remainPoint = remain * CommonUtil.musePoint2Point(type.getConsumeMusePoints());
        if (userPoint.getPoint() < remainPoint) {
            log.warn("当前积分点数不足，需要充值，userId={},modelId={},图片数量={}", userId, modelId, imageNum);
            predict.setMusePoint(CommonUtil.point2MusePoint(remainPoint));
            predict.setNeedTopup(true);
            return predict;
        }

        predict.setMusePoint(
            CommonUtil.point2MusePoint(remain * CommonUtil.musePoint2Point(type.getConsumeMusePoints())));

        return predict;
    }

    /**
     * 计算其他的预算
     *
     * @param type     创作类型
     * @param imageNum 图片数
     * @param needLock 是否需要锁定
     * @param isUpload 是否上传
     * @return 预估生成图片所需要的积分
     */
    private PredictVO calcOtherPredict(CreativeTypeEnum type, Integer imageNum, boolean needLock, boolean isUpload) {
        //如果当前类型不消耗缪斯点，则直接返回
        BigDecimal consumeMusePoints = isUpload ? type.getConsumeMusePointsForUpload() : type.getConsumeMusePoints();
        if (!BigDecimalUtils.greaterThanZero(consumeMusePoints) || OperationContextHolder.isAdmin()
            || OperationContextHolder.isDistributorRole()) {
            PredictVO predict = new PredictVO();
            predict.setMusePoint(BigDecimal.ZERO);
            return predict;
        }

        PredictVO predict = new PredictVO();
        Integer userId = OperationContextHolder.getMasterUserId();

        UserPointDO userPoint = needLock ? userPointDAO.lockByUserId(userId) : selectDOByUserId(userId);
        predict.setUserPointDO(userPoint);

        if (userPoint == null) {
            log.warn("未查询到用户积分记录,userId={},此时用户需要充值", userId);
            predict.setNeedTopup(true);
            return predict;
        }

        predict.setCurrentGivePoint(userPoint.getGivePoint());
        predict.setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));

        BigDecimal musePoint = type.isConsumeByImageCnt() ? BigDecimal.valueOf(imageNum).multiply(consumeMusePoints)
            : consumeMusePoints;
        predict.setMusePoint(musePoint);
        predict.setNeedTopup(CommonUtil.musePoint2Point(musePoint) > userPoint.getPoint());

        return predict;
    }

    /**
     * 计算其他的预算, 扣费顺序是 赠送积分 > 缪斯点
     *
     * @param type     创作类型 (换头换背景, 局部重绘)
     * @param imageNum 图片数
     * @param needLock 是否需要锁定
     * @return 预估生成图片所需要的积分或者缪斯点
     */
    private PredictVO calcOtherPredict2(CreativeTypeEnum type, Integer imageNum, boolean needLock) {
        if (OperationContextHolder.isDistributorRole() || OperationContextHolder.isAdmin()
            || systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
            OperationContextHolder.getOperatorUserId())) {
            PredictVO predict = new PredictVO();
            predict.setMusePoint(BigDecimal.ZERO);
            return predict;
        }

        //参数准备
        PredictVO predict = new PredictVO();
        Integer userId = OperationContextHolder.getMasterUserId();

        //扣款顺序：赠送积分 > 缪斯点
        //1.锁用户积分记录
        UserPointDO userPoint = needLock ? userPointDAO.lockByUserId(userId) : selectDOByUserId(userId);
        predict.setUserPointDO(userPoint);

        if (userPoint == null) {
            log.warn("未查询到用户积分记录,userId={},此时用户需要充值", userId);
            predict.setNeedTopup(true);
            return predict;
        }

        predict.setCurrentGivePoint(userPoint.getGivePoint());
        predict.setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));
        //2.先扣除赠送积分
        int remain = imageNum;
        int deduct = userPoint.getGivePoint() > remain ? remain : userPoint.getGivePoint();
        if (deduct > 0) {
            predict.setGivePoint(deduct);
        }
        remain = remain - deduct;
        if (remain == 0) {
            return predict;
        }

        // 3. 如果还是不够扣除，则扣除缪斯点
        int remainPoint = remain * CommonUtil.musePoint2Point(type.getConsumeMusePoints());
        //3.1.如果剩余积分不足（0.4个缪斯点一张图），则直接抛异常
        if (userPoint.getPoint() < remainPoint) {
            log.warn("当前积分点数不足，需要充值，userId={},图片数量={}", userId, imageNum);
            predict.setMusePoint(CommonUtil.point2MusePoint(remainPoint));
            predict.setNeedTopup(true);
            return predict;
        }
        predict.setMusePoint(
            CommonUtil.point2MusePoint(remain * CommonUtil.musePoint2Point(type.getConsumeMusePoints())));
        return predict;
    }

    /**
     * 直接扣 缪斯点
     *
     * @param type     创作类型
     * @param needLock 是否需要锁定
     * @return 预估生成图片所需要的缪斯点
     */
    private PredictVO calcOtherPredict3(CreativeTypeEnum type, Integer imageNum, boolean needLock) {
        if (OperationContextHolder.isDistributorRole() || OperationContextHolder.isAdmin()
            || systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
            OperationContextHolder.getOperatorUserId())) {
            PredictVO predict = new PredictVO();
            predict.setMusePoint(BigDecimal.ZERO);
            return predict;
        }

        if (type == CreativeTypeEnum.REPAIR_DETAIL || type == CreativeTypeEnum.PARTIAL_REDRAW
                || type == CreativeTypeEnum.REPAIR_HANDS || type == CreativeTypeEnum.REMOVE_WRINKLE
                || type == CreativeTypeEnum.ERASE_BRUSH) {
            imageNum = 1;
        }

        //参数准备
        PredictVO predict = new PredictVO();
        Integer userId = OperationContextHolder.getMasterUserId();

        //扣款顺序：直接扣缪斯点
        //1.锁用户积分记录
        UserPointDO userPoint = needLock ? userPointDAO.lockByUserId(userId) : selectDOByUserId(userId);
        predict.setUserPointDO(userPoint);

        if (userPoint == null) {
            log.warn("未查询到用户积分记录,userId={},此时用户需要充值", userId);
            predict.setNeedTopup(true);
            return predict;
        }

        predict.setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));
        //2. 计算所需的缪斯点数量
        int remain = CommonUtil.musePoint2Point(type.getConsumeMusePoints()) * imageNum;
        //3.1.如果剩余积分不足，则直接抛异常
        if (userPoint.getPoint() < remain) {
            log.warn("当前积分点数不足，需要充值，userId={},图片数量={}", userId, imageNum);
            predict.setMusePoint(CommonUtil.point2MusePoint(remain));
            predict.setNeedTopup(true);
            return predict;
        }
        predict.setMusePoint(CommonUtil.point2MusePoint(remain));
        return predict;
    }

    /**
     * 根据用户id查询do信息
     *
     * @param userId 用户id
     * @return UserPointDO
     */
    private UserPointDO selectDOByUserId(Integer userId) {
        // 参数校验
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");

        // 请求参数封装
        UserPointExample example = new UserPointExample();
        example.createCriteria().andUserIdEqualTo(userId);
        // 执行查询操作
        List<UserPointDO> list = userPointDAO.selectByExample(example);

        //        if (CollectionUtils.isEmpty(list)) {
        //            return null;
        //        }
        //
        //        return list.get(0);

        // 若集合为空则返回 null，不为空则返回第一条记录
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    private void consumeBySystemImage(Integer userId, Integer imageNum, Integer bizId, Integer operatorId) {
        UserPointDO data = userPointDAO.lockByUserId(userId);
        AssertUtil.assertNotNull(data, ResultCode.BIZ_FAIL, "用户积分不存在" + userId);

        int imagePoint = data.getExperiencePoint();
        if (imagePoint < imageNum) {
            log.warn("用户{}当前消耗的体验点数{}超过目前拥有的图片算力点{}，执行失败", userId, imageNum, imagePoint);
            throw new BizException(ResultCode.IMAGE_POINT_OVER_LIMIT, imageNum);
        }

        data.setExperiencePoint(data.getExperiencePoint() - imageNum);

        int cnt = userPointDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(cnt > 0, ResultCode.BIZ_FAIL, "更新用户算力点失败");
        log.info("消耗用户体验点数，userId={},imageNum={}", userId, imageNum);

        //记录流水
        logExperience(userId, imageNum, bizId, operatorId, data, true);
    }

    private void revertBySystemImage(Integer userId, Integer imageNum, Integer bizId, Integer operatorId) {
        UserPointDO data = userPointDAO.lockByUserId(userId);
        AssertUtil.assertNotNull(data, ResultCode.BIZ_FAIL, "用户不存在" + userId);

        data.setExperiencePoint(data.getExperiencePoint() + imageNum);

        int cnt = userPointDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(cnt > 0, ResultCode.BIZ_FAIL, "更新用户算力点失败");
        log.info("恢复用户体验点数，userId={},imageNum={}", userId, imageNum);

        //记录流水
        logExperience(userId, imageNum, bizId, operatorId, data, false);
    }

    /**
     * 记录体验点流水
     *
     * @param userId     用户id
     * @param imageNum   图片张数
     * @param bizId      业务id
     * @param operatorId 操作员
     * @param data       数据
     * @param isConsume  是否消耗
     */
    private void logExperience(Integer userId, Integer imageNum, Integer bizId, Integer operatorId, UserPointDO data,
                               boolean isConsume) {
        UserPointLogVO pointLog = new UserPointLogVO();
        pointLog.setUserId(userId);
        pointLog.setOperatorId(operatorId);
        pointLog.setType(isConsume ? PointLogTypeEnum.EXPERIENCE_CREATE : PointLogTypeEnum.EXPERIENCE_RETURN);
        pointLog.setExperiencePoint(isConsume ? -imageNum : imageNum);
        pointLog.setOriginExperiencePoint(data.getExperiencePoint() - pointLog.getExperiencePoint());
        pointLog.setTargetExperiencePoint(data.getExperiencePoint());
        pointLog.setRelatedId(bizId);
        userPointLogService.insert(pointLog);
    }
}