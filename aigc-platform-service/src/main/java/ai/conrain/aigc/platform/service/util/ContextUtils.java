/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Field;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 上下文工具类
 *
 * <AUTHOR>
 * @version : ContextUtils.java, v 0.1 2025/1/3 14:21 renxiao.wu Exp $
 */
@Slf4j
public abstract class ContextUtils {
    /**
     * 基于点分割的key，填充上下文
     *
     * @param target 目标填充上下文
     * @param key    关键字
     * @param value  值
     */
    public static void fillWithContext(JSONObject target, String key, Object value) {
        if (value == null) {
            return;
        }

        if (StringUtils.indexOf(key, ".") < 0) {
            target.put(key, value);
            return;
        }

        String[] keys = StringUtils.split(key, ".");
        JSONObject leaf = null;
        JSONObject current = null;

        for (int i = keys.length - 1; i > 0; i--) {
            String keyPart = keys[i];
            if (leaf == null) {
                leaf = new JSONObject();
                leaf.put(keyPart, value);
                current = leaf;
            } else {
                JSONObject temp = current;
                current = new JSONObject();
                current.put(keyPart, temp);
            }
        }

        //如果target中已经存在相同的key，则进行合并
        if (target.containsKey(keys[0])) {
            current = mergeJson(target.getJSONObject(keys[0]), current);
        }
        target.put(keys[0], current);
    }

    /**
     * 变更上下文
     *
     * @param context       当前上下文
     * @param changeContext 变更上下文
     */
    public static void updateContext(Map<String, Object> context, JSONObject changeContext) {
        try {

            for (String key : changeContext.keySet()) {
                Object testValue = changeContext.get(key);
                Object contextValue = context.get(key);

                if (testValue == null) {
                    continue;
                }

                if (testValue instanceof JSONObject) {
                    updateNestedObject(contextValue, (JSONObject)testValue);
                } else {
                    context.put(key, testValue);
                }
            }

        } catch (Exception e) {
            log.error("update test context error", e);
        }
    }

    /**
     * 变更具体的类
     *
     * @param targetObject 目标对象
     * @param updates      变更内容
     * @throws Exception 异常
     */
    private static void updateNestedObject(Object targetObject, JSONObject updates) throws Exception {
        if (targetObject == null) {
            return;
        }

        Class<?> clazz = targetObject.getClass();
        for (String fieldName : updates.keySet()) {
            Object newValue = updates.get(fieldName);
            if (newValue == null) {
                continue;
            }

            // 尝试更新对象的字段值
            Field field = getField(clazz, fieldName);
            if (field == null) {
                // 如果目标对象是 Map，直接添加新字段
                if (targetObject instanceof Map) {
                    //noinspection unchecked
                    ((Map<String, Object>)targetObject).put(fieldName, newValue);
                    continue;
                } else {
                    log.warn("Field [{}] not found in class [{}]", fieldName, clazz.getSimpleName());
                    continue;
                }
            }

            field.setAccessible(true); // 允许访问私有字段
            Object fieldValue = field.get(targetObject);

            // 如果字段是 Map 类型，递归更新 Map 的值
            if (fieldValue instanceof Map && newValue instanceof JSONObject) {
                updateMap((Map<?, ?>)fieldValue, (JSONObject)newValue);
            }
            // 如果字段是对象且新值是 JSONObject，则递归处理
            else if (fieldValue != null && newValue instanceof JSONObject) {
                updateNestedObject(fieldValue, (JSONObject)newValue);
            } else {
                // 否则直接更新字段值
                //TODO 这里要将ComfyUIUtils改成黑名单
                if (StringUtils.equals(targetObject.toString(), "tags")) {
                    field.set(targetObject, ComfyUIUtils.parseJsonStr(newValue.toString()));
                } else {
                    field.set(targetObject, newValue);
                }
            }
        }
    }

    /**
     * 变更map类型的值
     *
     * @param targetMap 目标map
     * @param updates   更新项
     */
    private static void updateMap(Map<?, ?> targetMap, JSONObject updates) {
        for (String key : updates.keySet()) {
            Object newValue = updates.get(key);
            Object currentValue = targetMap.get(key);

            //if (currentValue == null) {
            //    continue;
            //}

            if (currentValue instanceof Map && newValue instanceof JSONObject) {
                // 递归处理嵌套 Map
                updateMap((Map<?, ?>)currentValue, (JSONObject)newValue);
            } else {
                // 更新 Map 的值
                //noinspection unchecked
                ((Map<String, Object>)targetMap).put(key, newValue);
            }
        }
    }

    /**
     * 获取字段
     *
     * @param clazz     类
     * @param fieldName 字段名称
     * @return 字段
     */
    private static Field getField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass(); // 如果字段在父类中，继续查找
            }
        }
        return null; // 找不到字段
    }

    /**
     * 合并两个json
     *
     * @param mainObj   主对象
     * @param updateObj 待更新对象
     * @return 合并后json
     */
    private static JSONObject mergeJson(JSONObject mainObj, JSONObject updateObj) {
        if (mainObj == null) {return new JSONObject();}
        if (updateObj == null) {return mainObj;}

        // 遍历 updateObj 的所有键
        for (String key : updateObj.keySet()) {
            Object updateValue = updateObj.get(key);
            Object mainValue = mainObj.get(key);

            // 递归合并对象
            if (updateValue instanceof JSONObject && mainValue instanceof JSONObject) {
                JSONObject mergedChild = mergeJson((JSONObject)mainValue, (JSONObject)updateValue);
                mainObj.put(key, mergedChild);
            }
            // 合并数组
            else if (updateValue instanceof JSONArray && mainValue instanceof JSONArray) {
                JSONArray mergedArray = mergeArray((JSONArray)mainValue, (JSONArray)updateValue);
                mainObj.put(key, mergedArray);
            }
            // 覆盖其他类型
            else {
                mainObj.put(key, updateValue);
            }
        }
        return mainObj;
    }

    /**
     * 合并json数组
     *
     * @param mainArray   原数组
     * @param updateArray 待更新数组
     * @return 合并后结果
     */
    private static JSONArray mergeArray(JSONArray mainArray, JSONArray updateArray) {
        JSONArray merged = new JSONArray();
        merged.addAll(mainArray);
        merged.addAll(updateArray);
        return merged;
    }
}
