package ai.conrain.aigc.platform.service.component.onnx;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Size;
import org.bytedeco.javacpp.Loader;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Collections;
import java.util.Map;

@Slf4j
@Component
public class GenreRecognizeOnnxService {

    static {
        try {
            // 完全禁用OpenBLAS以避免macOS ARM64兼容性问题
            System.setProperty("org.bytedeco.javacpp.nopointergc", "true");
            System.setProperty("org.bytedeco.javacpp.nopreload", "true");
            System.setProperty("org.bytedeco.javacpp.logger.debug", "true");
            System.setProperty("org.bytedeco.openblas.load", "false");
            System.setProperty("org.bytedeco.javacpp.pathsFirst", "true");
            
            // 设置Java库路径，排除可能的OpenBLAS路径
            String javaLibraryPath = System.getProperty("java.library.path");
            log.info("当前java.library.path: {}", javaLibraryPath);
            
            // 尝试加载OpenCV库，如果失败则跳过OpenBLAS相关功能
             try {
                 Loader.load(opencv_core.class);
                 openCvLoaded = true;
                 log.info("OpenCV库加载成功");
             } catch (UnsatisfiedLinkError e) {
                 if (e.getMessage().contains("jniopenblas")) {
                     log.warn("检测到OpenBLAS库加载问题，将跳过相关功能: {}", e.getMessage());
                     openCvLoaded = false;
                     // 不抛出异常，允许应用继续启动
                 } else {
                     openCvLoaded = false;
                     throw e;
                 }
             }
        } catch (Exception e) {
            log.error("库加载过程中出现异常", e);
            // 不抛出运行时异常，允许应用继续启动
            log.warn("将在运行时检查库可用性");
        }
    }

    private SimpleOnnxWrapper onnxWrapper;
    private static final String ONNX_MODEL_PATH = "onnx/genre_model.onnx";
    private static boolean openCvLoaded = false;

    // 类别标签映射
    private static final Map<Integer, String> CLASS_LABELS = Map.of(
            0, "Product_lookbook",
            1, "Runway_shot",
            2, "Vogue_style_fashion_shoot",
            3, "LRSP2111_commercial",
            4, "Selfie",
            5, "Internet_style_photo");

    @PostConstruct
    public void init() {
        onnxWrapper = new SimpleOnnxWrapper(ONNX_MODEL_PATH, "流派识别模型服务");
        onnxWrapper.init();
    }

    @PreDestroy
    public void destroy() {
        if (onnxWrapper != null) {
            onnxWrapper.destroy();
        }
    }

    /**
     * 基于onnx包，识别图像的流派类别
     */
    public String recognizeGenreFromPath(String imgPath) throws OrtException {
        if (!openCvLoaded) {
            log.warn("OpenCV库未成功加载，无法进行图像处理");
            throw new IllegalStateException("OpenCV库未加载，图像识别功能不可用");
        }
        
        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        if (imgPath == null || imgPath.trim().isEmpty()) {
            throw new IllegalArgumentException("图像路径不能为空");
        }

        try {
            log.info("开始识别图像流派，图像路径: {}", imgPath);

            // 加载和预处理图像
            float[][][][] imageData = loadAndPreprocessImage(imgPath);

            // 创建输入张量
            OnnxTensor inputTensor = OnnxTensor.createTensor(onnxWrapper.getEnvironment(), imageData);

            // 执行推理
            Map<String, OnnxTensor> inputs = Collections.singletonMap("image", inputTensor);
            OrtSession.Result result = onnxWrapper.getSession().run(inputs);

            // 获取输出
            OnnxTensor outputTensor = (OnnxTensor) result.get(0);
            long[][] predictions = (long[][]) outputTensor.getValue();

            // 获取预测类别
            int predictedClass = (int) predictions[0][0];
            String genreLabel = CLASS_LABELS.get(predictedClass);

            log.info("图像流派识别完成，预测类别: {} ({})", predictedClass, genreLabel);

            // 清理资源
            inputTensor.close();
            result.close();

            return genreLabel;

        } catch (Exception e) {
            log.error("图像流派识别失败，图像路径: {}", imgPath, e);
            throw new OrtException("图像流派识别失败: " + e.getMessage());
        }
    }

    /**
     * 从URL下载图像并识别流派
     */
    public String recognizeGenreFromUrl(String imageUrl) throws OrtException {
        if (!openCvLoaded) {
            log.warn("OpenCV库未成功加载，无法进行图像处理");
            throw new IllegalStateException("OpenCV库未加载，图像识别功能不可用");
        }
        
        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("图像URL不能为空");
        }

        Path tempFile = null;

        try {
            log.info("开始下载并识别网络图像流派，图像URL: {}", imageUrl);

            // 下载图片到本地
            tempFile = downloadImageFromUrl(imageUrl);
            String localPath = tempFile.toAbsolutePath().toString();
            log.debug("图片下载完成: {} -> {}", imageUrl, localPath);

            // 调用现有的本地文件识别方法
            String result = recognizeGenreFromPath(localPath);

            log.info("网络图像流派识别完成，结果: {}", result);
            return result;

        } catch (IOException e) {
            log.error("下载图像失败，URL: {}", imageUrl, e);
            throw new OrtException("下载图像失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                    log.debug("临时文件已清理: {}", tempFile);
                } catch (Exception e) {
                    log.warn("清理临时文件失败: {}", tempFile, e);
                }
            }
        }
    }

    /**
     * 从URL下载图片到本地临时文件
     * 
     * @param imageUrl 图片URL
     * @return 本地临时文件路径
     * @throws IOException 下载异常
     */
    private Path downloadImageFromUrl(String imageUrl) throws IOException {
        // 从URL中提取文件扩展名，默认为.jpg
        String extension = ".jpg";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.split("\\?")[0]; // 去除URL参数
            int lastDotIndex = urlPath.lastIndexOf(".");
            if (lastDotIndex > 0 && lastDotIndex < urlPath.length() - 1) {
                String ext = urlPath.substring(lastDotIndex);
                if (ext.matches("\\.(jpg|jpeg|png|gif|bmp|webp)")) {
                    extension = ext;
                }
            }
        }

        // 创建临时文件
        Path tempFile = Files.createTempFile("genre_recognize_", extension);

        // 下载图片
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
        }

        return tempFile;
    }

    /**
     * 加载和预处理图像
     * 
     * @param imagePath 图像文件路径
     * @return 预处理后的图像数据
     */
    private float[][][][] loadAndPreprocessImage(String imagePath) {
        // 使用OpenCV加载图像
        Mat image = opencv_imgcodecs.imread(imagePath);

        if (image.empty()) {
            throw new RuntimeException("无法加载图像: " + imagePath);
        }

        // 转换颜色空间 BGR -> RGB
        Mat rgbImage = new Mat();
        opencv_imgproc.cvtColor(image, rgbImage, opencv_imgproc.COLOR_BGR2RGB);

        // 调整图像尺寸到模型要求的224x224
        Mat resizedImage = new Mat();
        opencv_imgproc.resize(rgbImage, resizedImage, new Size(224, 224));

        log.debug("原始图像尺寸: {}x{}, 调整后尺寸: {}x{}",
                rgbImage.cols(), rgbImage.rows(),
                resizedImage.cols(), resizedImage.rows());

        // 转换为float数组
        float[][][][] imageData = new float[1][3][224][224];

        // 获取图像数据
        byte[] imageBytes = new byte[(int) (resizedImage.total() * resizedImage.channels())];
        resizedImage.data().get(imageBytes);

        int pixelIndex = 0;
        for (int h = 0; h < 224; h++) {
            for (int w = 0; w < 224; w++) {
                // RGB通道分离，值范围保持[0, 255]
                imageData[0][0][h][w] = (float) (imageBytes[pixelIndex] & 0xFF); // R
                imageData[0][1][h][w] = (float) (imageBytes[pixelIndex + 1] & 0xFF); // G
                imageData[0][2][h][w] = (float) (imageBytes[pixelIndex + 2] & 0xFF); // B
                pixelIndex += 3;
            }
        }

        return imageData;
    }
}
