package ai.conrain.aigc.platform.service.util;

import jakarta.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * EasyExcel 工具类
 * 提供通用的Excel导入导出功能
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class EasyExcelUtils {

    /**
     * 默认的每批次处理数据量
     */
    private static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * 读取Excel文件并返回数据列表
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param <T>         泛型
     * @return 数据列表
     */
    public static <T> List<T> read(InputStream inputStream, Class<T> clazz) {
        return read(inputStream, clazz, 0);
    }

    /**
     * 读取Excel文件并返回数据列表
     *
     * @param inputStream 输入流
     * @param clazz       数据类型
     * @param sheetNo     sheet页码，从0开始
     * @param <T>         泛型
     * @return 数据列表
     */
    public static <T> List<T> read(InputStream inputStream, Class<T> clazz, int sheetNo) {
        List<T> dataList = new ArrayList<>();

        try {
            EasyExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                @Override
                public void invoke(T data, AnalysisContext context) {
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共读取{}条数据", dataList.size());
                }
            }).sheet(sheetNo).doRead();
        } catch (Exception e) {
            log.error("读取Excel文件失败", e);
            throw new RuntimeException("读取Excel文件失败: " + e.getMessage(), e);
        }

        return dataList;
    }

    public static List<List<String>> read(InputStream inputStream, int sheetNo) {
        List<List<String>> result = new ArrayList<>();

        EasyExcel.read(inputStream, new AnalysisEventListener<Map<String, String>>() {
            @Override
            public void invoke(Map<String, String> data, AnalysisContext context) {
                result.add(new ArrayList<>(data.values()));
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 读取完成后的操作
            }
        }).sheet(sheetNo).doRead();

        return result;
    }

    /**
     * 分批读取Excel文件，适用于大文件处理
     *
     * @param inputStream   输入流
     * @param clazz         数据类型
     * @param batchConsumer 批处理消费者
     * @param <T>           泛型
     */
    public static <T> void readBatch(InputStream inputStream, Class<T> clazz, Consumer<List<T>> batchConsumer) {
        readBatch(inputStream, clazz, DEFAULT_BATCH_SIZE, batchConsumer);
    }

    /**
     * 分批读取Excel文件，适用于大文件处理
     *
     * @param inputStream   输入流
     * @param clazz         数据类型
     * @param batchSize     批次大小
     * @param batchConsumer 批处理消费者
     * @param <T>           泛型
     */
    public static <T> void readBatch(InputStream inputStream, Class<T> clazz, int batchSize,
                                     Consumer<List<T>> batchConsumer) {
        try {
            EasyExcel.read(inputStream, clazz, new AnalysisEventListener<T>() {
                private List<T> batchData = new ArrayList<>();

                @Override
                public void invoke(T data, AnalysisContext context) {
                    batchData.add(data);
                    if (batchData.size() >= batchSize) {
                        batchConsumer.accept(new ArrayList<>(batchData));
                        batchData.clear();
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    if (CollectionUtils.isNotEmpty(batchData)) {
                        batchConsumer.accept(batchData);
                    }
                    log.info("Excel分批读取完成");
                }
            }).sheet().doRead();
        } catch (Exception e) {
            log.error("分批读取Excel文件失败", e);
            throw new RuntimeException("分批读取Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 写入Excel到输出流
     *
     * @param outputStream 输出流
     * @param clazz        数据类型
     * @param data         数据列表
     * @param sheetName    sheet名称
     * @param <T>          泛型
     */
    public static <T> void write(OutputStream outputStream, Class<T> clazz, List<T> data, String sheetName) {
        try {
            EasyExcel.write(outputStream, clazz).registerWriteHandler(getDefaultCellStyle()).sheet(sheetName).doWrite(
                data);
        } catch (Exception e) {
            log.error("写入Excel文件失败", e);
            throw new RuntimeException("写入Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 写入Excel到输出流（默认sheet名称）
     *
     * @param outputStream 输出流
     * @param clazz        数据类型
     * @param data         数据列表
     * @param <T>          泛型
     */
    public static <T> void write(OutputStream outputStream, Class<T> clazz, List<T> data) {
        write(outputStream, clazz, data, "Sheet1");
    }

    /**
     * 导出Excel到HTTP响应
     *
     * @param response  HTTP响应
     * @param clazz     数据类型
     * @param data      数据列表
     * @param fileName  文件名（不包含扩展名）
     * @param sheetName sheet名称
     * @param <T>       泛型
     */
    public static <T> void export(HttpServletResponse response, Class<T> clazz, List<T> data, String fileName,
                                  String sheetName) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            // 防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+",
                "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            EasyExcel.write(response.getOutputStream(), clazz).registerWriteHandler(getDefaultCellStyle()).sheet(
                sheetName).doWrite(data);
        } catch (IOException e) {
            log.error("导出Excel文件失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    public static void export(List<List<String>> data, HttpServletResponse response) throws IOException {
        export(data, "export.xlsx", response);
    }

    /**
     * 导出Excel到HTTP响应（默认sheet名称）
     *
     * @param response HTTP响应
     * @param data     数据列表
     */
    public static void export(List<List<String>> data, String fileName, HttpServletResponse response)
        throws IOException {

        if (StringUtils.isBlank(fileName)) {
            fileName = "export.xlsx";
        }

        // 创建工作簿对象
        Workbook workbook = new XSSFWorkbook();

        // 在工作簿中创建工作表
        Sheet sheet = workbook.createSheet("导出结果");

        int rowNum = 0;
        for (List<String> rowData : data) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            for (String field : rowData) {
                Cell cell = row.createCell(colNum++);
                cell.setCellValue(field);
            }
        }

        // 设置响应头信息
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        // 将工作簿写入到响应输出流中
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 导出Excel到HTTP响应（默认sheet名称）
     *
     * @param response HTTP响应
     * @param clazz    数据类型
     * @param data     数据列表
     * @param fileName 文件名（不包含扩展名）
     * @param <T>      泛型
     */
    public static <T> void export(HttpServletResponse response, Class<T> clazz, List<T> data, String fileName) {
        export(response, clazz, data, fileName, "数据导出");
    }

    /**
     * 多sheet导出
     *
     * @param response HTTP响应
     * @param fileName 文件名（不包含扩展名）
     * @param sheets   sheet数据列表
     */
    public static void exportMultiSheet(HttpServletResponse response, String fileName, List<SheetData<?>> sheets) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");

            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+",
                "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();

            for (int i = 0; i < sheets.size(); i++) {
                SheetData<?> sheetData = sheets.get(i);
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetData.getSheetName()).head(sheetData.getClazz())
                    .registerWriteHandler(getDefaultCellStyle()).build();
                excelWriter.write(sheetData.getData(), writeSheet);
            }

            excelWriter.finish();
        } catch (IOException e) {
            log.error("多sheet导出Excel文件失败", e);
            throw new RuntimeException("多sheet导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取默认的单元格样式
     *
     * @return 样式策略
     */
    private static HorizontalCellStyleStrategy getDefaultCellStyle() {
        // 头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short)12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short)11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * Sheet数据封装类
     *
     * @param <T> 数据类型
     */
    public static class SheetData<T> {
        private String sheetName;
        private Class<T> clazz;
        private List<T> data;

        public SheetData(String sheetName, Class<T> clazz, List<T> data) {
            this.sheetName = sheetName;
            this.clazz = clazz;
            this.data = data;
        }

        public String getSheetName() {
            return sheetName;
        }

        public Class<T> getClazz() {
            return clazz;
        }

        public List<T> getData() {
            return data;
        }
    }

    /**
     * 验证Excel文件格式
     *
     * @param fileName 文件名
     * @return 是否为Excel文件
     */
    public static boolean isExcelFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        String lowerCaseFileName = fileName.toLowerCase();
        return lowerCaseFileName.endsWith(".xlsx") || lowerCaseFileName.endsWith(".xls");
    }

    /**
     * 根据文件名获取Excel类型
     *
     * @param fileName 文件名
     * @return Excel类型
     */
    public static ExcelTypeEnum getExcelType(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return ExcelTypeEnum.XLSX;
        }
        return fileName.toLowerCase().endsWith(".xls") ? ExcelTypeEnum.XLS : ExcelTypeEnum.XLSX;
    }
}
