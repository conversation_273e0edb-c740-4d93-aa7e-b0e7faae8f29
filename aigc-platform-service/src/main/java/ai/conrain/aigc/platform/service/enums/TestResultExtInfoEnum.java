package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 测试结果扩展信息枚举
 */
@Getter
public enum TestResultExtInfoEnum {
    MATERIAL_SIMILARITY("materialSimilarity", "还原度"),
    MODEL_FACE_SIMILARITY("modelFaceSimilarity", "模特面部还原度"),
    MODEL_FIGURE_SIMILARITY("modelFigureSimilarity", "模特比例还原度"),
    SCENE_BG_SIMILARITY("sceneBgSimilarity", "场景背景还原度"),
    SCENE_POSE_SIMILARITY("scenePoseSimilarity", "场景姿势还原度"),
    SCENE_COMPOSITION_SIMILARITY("sceneCompositionSimilarity", "半身/全身构图还原度"),
    ;
    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    TestResultExtInfoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 Code 获取枚举信息
     *
     * @param code 类型编码
     * @return 枚举信息
     */
    public static TestResultExtInfoEnum getByCode(String code) {
        for (TestResultExtInfoEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}
