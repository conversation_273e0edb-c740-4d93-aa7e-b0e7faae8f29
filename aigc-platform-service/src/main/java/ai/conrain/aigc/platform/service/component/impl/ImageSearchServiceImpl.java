package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.aliyun.AliyunEmbeddingService;
import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageSearchService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.agent.ClusteringUtil;
import ai.conrain.aigc.platform.service.component.agent.DiversifyUtil;
import ai.conrain.aigc.platform.service.component.agent.PCAUtil;
import ai.conrain.aigc.platform.service.component.onnx.SortModelService;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.ClothDescription;
import ai.conrain.aigc.platform.service.model.biz.agent.ImageMatchScope;
import ai.conrain.aigc.platform.service.model.biz.agent.ProcessedInput;
import ai.conrain.aigc.platform.service.model.biz.agent.ShootStyleImageRecommendParam;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.biz.agent.UserRefImgDescription;
import ai.conrain.aigc.platform.service.model.biz.agent.UserUploadStyleImg;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendResult;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendation;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.VectorUtil;
import com.pgvector.PGvector;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ImageSearchServiceImpl implements ImageSearchService {

    private static final int TEXT_EMB_DIMENSION = 256;
    @Autowired
    private AliyunEmbeddingService aliyunEmbeddingService;
    @Autowired
    private ImageCaptionService imageCaptionService;
    @Autowired
    private ImageService imageService;
    // 线程池
    private ExecutorService executorService;
    @Autowired
    private SortModelService sortModelService;

    @PostConstruct
    public void init() {
        try {
            // 初始化线程池
            executorService = Executors.newVirtualThreadPerTaskExecutor();
        } catch (Exception e) {
            log.error("初始化失败", e);
            throw new RuntimeException("初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (executorService != null) {
                executorService.shutdown(); // 优雅关闭
                try {
                    // 虚拟线程关闭更快，等待30秒即可
                    if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                        executorService.shutdownNow(); // 强制关闭
                        // 再等待30秒
                        if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                            log.warn("虚拟线程执行器未能完全关闭");
                        }
                    }
                    log.info("虚拟线程执行器已关闭");
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                    log.warn("虚拟线程执行器关闭过程中被中断", e);
                }
            }
        } catch (Exception e) {
            log.error("资源释放失败", e);
        }
    }

    @Override
    public StyleImageRecommendResult searchAndRecommend(ShootStyleImageRecommendParam param) {
        log.info("🎯 开始服装拍摄风格图片推荐算法");

        // 整体耗时统计开始
        long totalStartTime = System.currentTimeMillis();
        Map<String, Long> performanceMetrics = new LinkedHashMap<>();

        try {
            // 1. 输入处理模块，计算各种向量
            long step1StartTime = System.currentTimeMillis();
            ProcessedInput processedInput = processInput(param);
            long step1EndTime = System.currentTimeMillis();
            long step1Duration = step1EndTime - step1StartTime;
            performanceMetrics.put("输入处理模块", step1Duration);
            log.info("📥 输入处理完成 | 耗时:{}ms | 服装类型:{} | 参考图:{}张",
                    step1Duration,
                    param.getClothType().getDesc(),
                    processedInput.getUserRefImgDescriptions().size());

            // 2. 服装图特征召回（100w->保留2000条）
            long step2StartTime = System.currentTimeMillis();
            List<StyleImageCandidate> candidates = recallByClothFeatures(processedInput, 0.5, 2000, param);
            long step2EndTime = System.currentTimeMillis();
            long step2Duration = step2EndTime - step2StartTime;
            performanceMetrics.put("服装图特征召回", step2Duration);
            Map<String, Integer> recallGenreStats = getGenreStats(candidates);
            log.info("🔍 服装特征召回完成 | 耗时:{}ms | 候选数:{} | 流派分布:{}",
                    step2Duration, candidates.size(), recallGenreStats);

            // 3. 流派分组和过滤（先按流派分组，过滤掉不需要的流派）
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups = groupByGenre(candidates, processedInput);
            // 过滤掉空的流派组
            genreGroups.entrySet().removeIf(entry -> entry.getValue().isEmpty());

            // 4. 对每个流派并行进行MMR打散过滤
            long step4StartTime = System.currentTimeMillis();
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> diversifiedGenreGroups = diversifyByGenre(genreGroups,
                    processedInput);
            long step4EndTime = System.currentTimeMillis();
            long step4Duration = step4EndTime - step4StartTime;
            performanceMetrics.put("流派并行MMR打散", step4Duration);
            int totalDiversified = diversifiedGenreGroups.values().stream().mapToInt(List::size).sum();
            log.info("🎯 流派并行MMR打散完成 | 耗时:{}ms | 输出总数:{} | 流派数:{}",
                    step4Duration, totalDiversified, diversifiedGenreGroups.size());

            // 5. 对每个流派内按背景聚类（并行处理）
            long step5StartTime = System.currentTimeMillis();
            StyleImageRecommendResult result = clusterByGenreAndBackground(diversifiedGenreGroups, processedInput);
            long step5EndTime = System.currentTimeMillis();
            long step5Duration = step5EndTime - step5StartTime;
            performanceMetrics.put("流派背景聚类", step5Duration);
            log.info("🔗 流派背景聚类完成 | 耗时:{}ms | 输入流派数:{} → 聚类结果:{}项",
                    step5Duration, diversifiedGenreGroups.size(),
                    result.getItems() != null ? result.getItems().size() : 0);

            // 计算总耗时
            long totalEndTime = System.currentTimeMillis();
            long totalDuration = totalEndTime - totalStartTime;
            performanceMetrics.put("总耗时", totalDuration);

            result.setPerformanceReport(logPerformanceReport(performanceMetrics, result));

            log.info("【耗时统计】:{}", result.getPerformanceReport());

            return result;

        } catch (Exception e) {
            long totalEndTime = System.currentTimeMillis();
            long totalDuration = totalEndTime - totalStartTime;
            log.error("服装拍摄风格图片推荐执行失败，总耗时：{}ms", totalDuration, e);
            throw new RuntimeException("推荐算法执行失败", e);
        }
    }

    /**
     * 获取流派统计信息
     */
    private Map<String, Integer> getGenreStats(List<StyleImageCandidate> candidates) {
        Map<String, Integer> tagCountMap = new HashMap<>();
        candidates.forEach(c -> {
            String genre = c.getGenreStr();
            if (StringUtils.isNotBlank(genre)) {
                String k = ClothShootGenreEnum.getByCode(genre).getDisplayName();
                tagCountMap.put(k, tagCountMap.getOrDefault(k, 0) + 1);
            }
        });
        return tagCountMap;
    }

    /**
     * 1. 输入处理模块（并行优化版本）
     */
    private ProcessedInput processInput(ShootStyleImageRecommendParam param) throws Exception {
        AssertUtil.assertNotNull(param, "参数不能为空");
        AssertUtil.assertNotBlank(param.getClothImgUrl(), "服装图片URL不能为空");
        AssertUtil.assertNotNull(param.getClothType(), "服装类型不能为空");
        AssertUtil.assertNotNull(param.getClothGender(), "服装性别不能为空");
        AssertUtil.assertNotNull(param.getClothAnalysis(), "服装标注不能为空");

        ProcessedInput input = new ProcessedInput();

        // 并行处理：服装图片向量计算 & 参考图片向量计算
        CompletableFuture<ClothDescription> clothFuture = CompletableFuture.supplyAsync(() -> {
            long clothProcessStartTime = System.currentTimeMillis();
            ClothDescription clothDescription = processClothImage(param);
            long clothProcessEndTime = System.currentTimeMillis();
            log.info("【耗时统计】服装图片处理完成，耗时：{}ms", clothProcessEndTime - clothProcessStartTime);
            return clothDescription;
        }, executorService);

        CompletableFuture<Pair<List<UserRefImgDescription>, Map<String, PGvector>>> refImagesFuture = CompletableFuture
                .supplyAsync(() -> {
                    long refProcessStartTime = System.currentTimeMillis();
                    List<UserRefImgDescription> userRefImgDescriptions = getUserUploadStyleImgDescriptionsParallel(
                            param);
                    long refProcessEndTime = System.currentTimeMillis();
                    log.info("【耗时统计】参考图片并行处理{}张图片，耗时：{}ms",
                            param.getUserUploadStyleImgs() != null ? param.getUserUploadStyleImgs().size() : 0,
                            refProcessEndTime - refProcessStartTime);

                    // 计算参考图的各维度的平均向量，用于优化MMR算法性能
                    Map<String, PGvector> averageDimensionVectors = calculateAverageDimensionVectors(
                            userRefImgDescriptions);

                    return Pair.of(userRefImgDescriptions, averageDimensionVectors);
                }, executorService);

        // 等待服装图片和参考图片处理完成
        Pair<List<UserRefImgDescription>, Map<String, PGvector>> refImagesResult = refImagesFuture.get();

        input.setClothDescription(clothFuture.get());
        input.setUserRefImgDescriptions(refImagesResult.getLeft());
        input.setAverageDimensionVectors(refImagesResult.getRight());

        // 计算动态权重
        input.setDynamicWeights(calculateDynamicWeights(param.getUserUploadStyleImgs()));

        return input;
    }

    /**
     * 计算动态权重
     */
    private Map<String, Double> calculateDynamicWeights(List<UserUploadStyleImg> userUploadStyleImgs) {
        Map<String, Double> weights = new HashMap<>();

        // 默认权重
        weights.put(ImageMatchScope.BG.name(), 0.3);
        weights.put(ImageMatchScope.ACCESSORIES.name(), 0.05);
        weights.put(ImageMatchScope.MODEL_FACIAL.name(), 0.05);
        weights.put(ImageMatchScope.MODEL_POSE.name(), 0.05);
        weights.put(ImageMatchScope.LIGHT.name(), 0.05);

        if (CollectionUtils.isEmpty(userUploadStyleImgs)) {
            return weights;
        }

        // 统计用户选择的维度
        Set<String> selectedDimensions = new HashSet<>();
        for (UserUploadStyleImg styleImg : userUploadStyleImgs) {
            if (CollectionUtils.isNotEmpty(styleImg.getImageMatchScopeList())) {
                for (ImageMatchScope scope : styleImg.getImageMatchScopeList()) {
                    selectedDimensions.add(scope.name());
                }
            }
        }

        // 重新分配权重
        if (!selectedDimensions.isEmpty()) {
            double totalWeight = 0.5;
            double weightPerDimension = totalWeight / selectedDimensions.size();

            // 重置权重
            weights.replaceAll((k, v) -> 0.0);

            // 分配权重给选中的维度
            for (String dimension : selectedDimensions) {
                weights.put(dimension, weightPerDimension);
            }
        }

        return weights;
    }

    /**
     * 计算各维度的平均向量
     * 
     * @param userRefImgDescriptions 用户参考图描述列表
     * @return 各维度的平均向量映射
     */
    private Map<String, PGvector> calculateAverageDimensionVectors(List<UserRefImgDescription> userRefImgDescriptions) {
        Map<String, PGvector> averageDimensionVectors = new HashMap<>();

        if (CollectionUtils.isEmpty(userRefImgDescriptions)) {
            return averageDimensionVectors;
        }

        // 按维度分组收集向量
        Map<String, List<PGvector>> dimensionVectorsMap = new HashMap<>();

        for (UserRefImgDescription refDesc : userRefImgDescriptions) {
            if (refDesc.getDimensionVectors() != null) {
                for (Map.Entry<String, PGvector> entry : refDesc.getDimensionVectors().entrySet()) {
                    String dimensionName = entry.getKey();
                    PGvector vector = entry.getValue();

                    if (vector != null) {
                        dimensionVectorsMap.computeIfAbsent(dimensionName, k -> new ArrayList<>()).add(vector);
                    }
                }
            }
        }

        // 计算每个维度的平均向量
        for (Map.Entry<String, List<PGvector>> entry : dimensionVectorsMap.entrySet()) {
            String dimensionName = entry.getKey();
            List<PGvector> vectors = entry.getValue();

            if (!vectors.isEmpty()) {
                try {
                    PGvector averageVector = VectorUtil.calculateAverageVector(vectors);
                    averageDimensionVectors.put(dimensionName, averageVector);
                    log.debug("维度{}计算平均向量完成，包含{}个向量", dimensionName, vectors.size());
                } catch (Exception e) {
                    log.warn("计算维度{}的平均向量时发生异常: {}", dimensionName, e.getMessage());
                }
            }
        }

        log.info("计算各维度平均向量完成，共{}个维度", averageDimensionVectors.size());
        return averageDimensionVectors;
    }

    /**
     * 并行处理参考图片向量计算
     */
    @NotNull
    private List<UserRefImgDescription> getUserUploadStyleImgDescriptionsParallel(
            ShootStyleImageRecommendParam param) {
        List<UserRefImgDescription> userRefImgDescriptions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getUserUploadStyleImgs())) {
            try {
                // 创建并行任务列表
                List<CompletableFuture<UserRefImgDescription>> futures = new ArrayList<>();

                // 为每个参考图创建并行处理任务
                for (int i = 0; i < param.getUserUploadStyleImgs().size(); i++) {
                    final int index = i;
                    UserUploadStyleImg styleImg = param.getUserUploadStyleImgs().get(index);
                    CompletableFuture<UserRefImgDescription> future = CompletableFuture
                            .supplyAsync(() -> processReferenceImageParallel(styleImg, index), executorService);
                    futures.add(future);
                }

                // 等待所有任务完成并收集结果
                for (CompletableFuture<UserRefImgDescription> future : futures) {
                    UserRefImgDescription result = future.get();
                    if (result != null) {
                        userRefImgDescriptions.add(result);
                    }
                }

            } catch (Exception e) {
                log.error("参考图片并行处理失败", e);
                throw new RuntimeException("参考图片并行处理失败", e);
            }
        }
        return userRefImgDescriptions;
    }

    /**
     * 处理单个参考图片（并行版本）
     */
    private UserRefImgDescription processReferenceImageParallel(UserUploadStyleImg styleImg, int index) {
        try {
            AssertUtil.assertNotNull(styleImg.getImgAnalysis(), ResultCode.PARAM_INVALID, "参考图片的视觉打标不能为空");
            AssertUtil.assertNotEmpty(styleImg.getImageMatchScopeList(), "imageMatchScopeList不能为空");

            // 统一处理：全局参考时，替换为4个子维度进行计算
            if (styleImg.getImageMatchScopeList().contains(ImageMatchScope.ALL)) {
                styleImg.getImageMatchScopeList().clear();
                styleImg.getImageMatchScopeList().addAll(Arrays.asList(ImageMatchScope.BG, ImageMatchScope.ACCESSORIES,
                        ImageMatchScope.MODEL_FACIAL, ImageMatchScope.MODEL_POSE));
            }

            // 收集当前参考图的所有维度文本
            List<String> currentImgTexts = new ArrayList<>();
            List<ImageMatchScope> currentImgScopes = new ArrayList<>();

            for (ImageMatchScope scope : styleImg.getImageMatchScopeList()) {
                String dimensionText = extractDimensionText(styleImg.getImgAnalysis(), scope);
                if (StringUtils.isNotBlank(dimensionText)) {
                    currentImgTexts.add(dimensionText);
                    currentImgScopes.add(scope);
                }
            }

            // 为当前参考图计算向量（每次调用不超过10个文本）
            Map<String, PGvector> dimensionVectorMap = new HashMap<>();
            if (!currentImgTexts.isEmpty()) {
                List<PGvector> currentImgVectors = aliyunEmbeddingService.getEmbeddingByTexts(currentImgTexts,
                        TEXT_EMB_DIMENSION);

                // 构建维度向量映射
                for (int j = 0; j < currentImgScopes.size(); j++) {
                    dimensionVectorMap.put(currentImgScopes.get(j).name(), currentImgVectors.get(j));
                }
            }

            // 创建参考图描述对象
            UserRefImgDescription refImgDescription = BeanUtils.deepCopy(styleImg, UserRefImgDescription.class);
            refImgDescription.setDimensionVectors(dimensionVectorMap);

            return refImgDescription;
        } catch (Exception e) {
            log.error("处理参考图片{}时发生异常", index, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 服装图片理解
     */
    private ClothDescription processClothImage(ShootStyleImageRecommendParam param) {
        try {
            ClothDescription description = BeanUtils.deepCopy(param, ClothDescription.class);
            // 并行计算3个向量
            CompletableFuture<PGvector> clothTextVectorFuture = CompletableFuture.supplyAsync(
                    () -> imageCaptionService.calcClothTextVector(param.getClothAnalysis(), param.getClothType()),
                    executorService);
            CompletableFuture<PGvector> styleTextVectorFuture = CompletableFuture.supplyAsync(() -> {
                String styleDescription = imageCaptionService.getClothStyleDescription(param.getClothAnalysis());
                return aliyunEmbeddingService.getEmbeddingByText(styleDescription, TEXT_EMB_DIMENSION);
            }, executorService);

            CompletableFuture<PGvector> clothImgVectorFuture = CompletableFuture.supplyAsync(
                    () -> aliyunEmbeddingService.getImgEmbeddingByMultiModalModel(param.getClothImgUrl()),
                    executorService);

            // 等待所有任务完成并设置结果
            description.setClothTextVector(clothTextVectorFuture.get());
            description.setStyleTextVector(styleTextVectorFuture.get());
            description.setClothImgVector(clothImgVectorFuture.get());

            return description;
        } catch (Exception e) {
            log.error("服装图片并行处理失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 2. 服装图特征召回（100w->保留2000条）
     * 支持并发查询所有流派或串行查询单一流派
     */
    private List<StyleImageCandidate> recallByClothFeatures(ProcessedInput input, Double similarity, int limit,
            ShootStyleImageRecommendParam param) {
        if (param.getEnableGenreConcurrency()) {
            return recallByClothFeaturesWithConcurrency(input, similarity, limit);
        } else {
            return recallByClothFeaturesSerial(input, similarity, limit);
        }
    }

    /**
     * 并发查询所有流派的服装图特征召回
     */
    private List<StyleImageCandidate> recallByClothFeaturesWithConcurrency(ProcessedInput input, Double similarity,
            int limit) {
        List<StyleImageCandidate> candidates = new ArrayList<>();

        // 2.1 获取所有流派枚举
        ClothShootGenreEnum[] allGenres = ClothShootGenreEnum.values();
        int genreCount = allGenres.length;
        int limitPerGenre = Math.max(limit / genreCount, 600); // 每个流派分配的查询数量

        // 2.2 并发查询所有流派
        List<CompletableFuture<List<ImageCaptionVO>>> futures = new ArrayList<>();

        for (ClothShootGenreEnum genre : allGenres) {
            CompletableFuture<List<ImageCaptionVO>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return imageCaptionService.queryByStyleVectorSimilarityFromCache(
                            input.getClothDescription().getStyleTextVector(),
                            input.getClothDescription().getClothGender(),
                            similarity,
                            limitPerGenre,
                            genre.getCode());
                } catch (Exception e) {
                    log.error("查询流派{}数据时发生异常", genre.getCode(), e);
                    return new ArrayList<>();
                }
            }, executorService);
            futures.add(future);
        }

        // 2.3 等待所有查询完成并合并结果
        List<ImageCaptionVO> allMatchedImages = new ArrayList<>();
        for (CompletableFuture<List<ImageCaptionVO>> future : futures) {
            try {
                List<ImageCaptionVO> genreResults = future.get();
                allMatchedImages.addAll(genreResults);
            } catch (Exception e) {
                log.error("获取流派查询结果时发生异常", e);
            }
        }

        // 2.4 按相似度排序并限制总数量(limit)
        allMatchedImages.sort((a, b) -> Double.compare(
                b.getClothStyleSimilarity() != null ? b.getClothStyleSimilarity() : 0.0,
                a.getClothStyleSimilarity() != null ? a.getClothStyleSimilarity() : 0.0));

        if (allMatchedImages.size() > limit) {
            allMatchedImages = allMatchedImages.subList(0, limit);
        }

        // 候选项处理和维度向量构建
        buildCandidates(allMatchedImages, candidates);

        return candidates;
    }

    private void buildCandidates(List<ImageCaptionVO> allMatchedImages, List<StyleImageCandidate> candidates) {
        for (ImageCaptionVO imageCaption : allMatchedImages) {
            // 解析图像标注
            ImageAnalysisCaption captionModel = imageCaption.getCaption();
            if (captionModel == null) {
                continue;
            }

            StyleImageCandidate candidate = new StyleImageCandidate();
            candidate.setImageCaption(imageCaption);
            candidate.setStyleSimilarity(imageCaption.getClothStyleSimilarity());

            // 计算其他维度向量
            buildDimensionVectorsMap(candidate);

            candidates.add(candidate);
        }
    }

    /**
     * 串行查询单一流派的服装图特征召回
     */
    private List<StyleImageCandidate> recallByClothFeaturesSerial(ProcessedInput input, Double similarity, int limit) {
        List<StyleImageCandidate> candidates = new ArrayList<>();

        List<ImageCaptionVO> matchedImages = imageCaptionService.queryByStyleVectorSimilarityFromCache(
                input.getClothDescription().getStyleTextVector(),
                input.getClothDescription().getClothGender(),
                similarity,
                limit,
                null);

        // 候选项处理和维度向量构建
        buildCandidates(matchedImages, candidates);

        return candidates;
    }

    /**
     * 对每个流派并行进行MMR打散过滤
     */
    private Map<ClothShootGenreEnum, List<StyleImageCandidate>> diversifyByGenre(
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups, ProcessedInput input) {

        Map<ClothShootGenreEnum, List<StyleImageCandidate>> result = new HashMap<>();

        // 创建并行任务列表
        List<CompletableFuture<Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>>>> futures = new ArrayList<>();

        // 为每个流派创建并行MMR处理任务
        for (Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry : genreGroups.entrySet()) {
            ClothShootGenreEnum genre = entry.getKey();
            List<StyleImageCandidate> candidates = entry.getValue();

            if (CollectionUtils.isNotEmpty(candidates)) {
                CompletableFuture<Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>>> future = CompletableFuture
                        .supplyAsync(() -> {
                            long genreStartTime = System.currentTimeMillis();
                            List<StyleImageCandidate> diversified = DiversifyUtil.diversifyByMMRWithWindow(candidates,
                                    input, 0.5, 10);
                            long genreEndTime = System.currentTimeMillis();
                            log.info("【耗时统计】流派{}MMR打散完成，输入:{}条 → 输出:{}条，耗时:{}ms",
                                    genre.getDisplayName(), candidates.size(), diversified.size(),
                                    genreEndTime - genreStartTime);
                            return Map.entry(genre, diversified);
                        }, executorService);

                futures.add(future);
            }
        }

        // 等待所有任务完成并收集结果
        try {
            for (CompletableFuture<Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>>> future : futures) {
                Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry = future.get();
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    result.put(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("流派并行MMR打散处理失败", e);
            throw new RuntimeException("流派并行MMR打散处理失败", e);
        }

        return result;
    }

    /**
     * 4. 按流派与背景聚类
     */
    private StyleImageRecommendResult clusterByGenreAndBackground(
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> diversifiedGenreGroups, ProcessedInput input)
            throws Exception {

        // 分配流派配额
        Map<ClothShootGenreEnum, Integer> genreQuotas = allocateGenreQuotas(diversifiedGenreGroups, input,
                diversifiedGenreGroups.size() * 5 * 30);

        // 流派内按背景聚类并排序截断
        List<List<List<StyleImageRecommendation>>> resultItems = new ArrayList<>();

        // 创建并行任务列表
        List<CompletableFuture<List<List<StyleImageRecommendation>>>> futures = new ArrayList<>();

        // 为每个流派创建并行处理任务
        for (Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry : diversifiedGenreGroups.entrySet()) {
            ClothShootGenreEnum genre = entry.getKey();
            List<StyleImageCandidate> genreCandidates = entry.getValue();
            Integer quotaOfGenre = genreQuotas.get(genre);

            if (quotaOfGenre != null && quotaOfGenre > 0 && CollectionUtils.isNotEmpty(genreCandidates)) {
                CompletableFuture<List<List<StyleImageRecommendation>>> future = CompletableFuture.supplyAsync(() -> {
                    long singleGenreStartTime = System.currentTimeMillis();
                    try {
                        List<List<StyleImageRecommendation>> genreItems = clusterByBgWithinGenre(genreCandidates,
                                quotaOfGenre, genre, input);
                        long singleGenreEndTime = System.currentTimeMillis();
                        log.info("【耗时统计】流派{}背景聚类完成，耗时：{}ms，生成{}个推荐",
                                genre.getDisplayName(), singleGenreEndTime - singleGenreStartTime,
                                genreItems != null ? genreItems.size() : 0);
                        return genreItems;
                    } catch (Exception e) {
                        log.error("流派{}背景聚类处理失败", genre.getDisplayName(), e);
                        return null;
                    }
                }, executorService);

                futures.add(future);
            }
        }

        // 等待所有任务完成并收集结果
        for (CompletableFuture<List<List<StyleImageRecommendation>>> future : futures) {
            List<List<StyleImageRecommendation>> genreItems = future.get();
            if (CollectionUtils.isNotEmpty(genreItems)) {
                resultItems.add(genreItems);
            }
        }

        StyleImageRecommendResult result = new StyleImageRecommendResult();
        result.setItems(resultItems);

        return result;
    }

    private String extractDimensionText(ImageAnalysisCaption caption, ImageMatchScope scope) {
        // 根据维度提取对应文本
        if (caption == null) {
            throw new IllegalArgumentException("Invalid caption model");
        }

        switch (scope) {
            case BG:
                if (caption.getShootingTheme() != null) {
                    return caption.getShootingTheme().getShootingScene();
                }
                break;
            case ACCESSORIES:
                if (caption.getClothing() != null) {
                    return caption.getClothing().getAccessories();
                }
                break;
            case MODEL_FACIAL:
                if (caption.getModel() != null) {
                    return caption.getModel().getHairstyle() + "," + caption.getModel().getFacialExpression();
                }
                break;
            case MODEL_POSE:
                if (caption.getModel() != null) {
                    return caption.getModel().getPosture();
                }
                break;
            case ALL: {
                StringBuilder sb = new StringBuilder();
                List<ImageMatchScope> otherScopes = Arrays.asList(ImageMatchScope.BG, ImageMatchScope.ACCESSORIES,
                        ImageMatchScope.MODEL_FACIAL, ImageMatchScope.MODEL_POSE);
                otherScopes.forEach(s -> {
                    String text = extractDimensionText(caption, s);
                    if (StringUtils.isNotBlank(text)) {
                        sb.append(text).append(" ");
                    }
                });
                return sb.toString();
            }
            default:
                throw new IllegalArgumentException("Invalid image match scope: " + scope);
        }

        return "";
    }

    private void buildDimensionVectorsMap(StyleImageCandidate candidate) {
        // 计算候选图片的各维度向量
        Map<String, PGvector> dimensionVectors = new HashMap<>();

        // 获取背景向量
        if (candidate.getImageCaption().getBgTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.BG.name(), candidate.getImageCaption().getBgTextEmb());
        }

        // 获取配饰向量
        if (candidate.getImageCaption().getAccessoriesTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.ACCESSORIES.name(),
                    candidate.getImageCaption().getAccessoriesTextEmb());
        }

        // 获取发型向量
        if (candidate.getImageCaption().getHairstyleTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.MODEL_FACIAL.name(),
                    candidate.getImageCaption().getHairstyleTextEmb());
        }

        // 获取姿势向量
        if (candidate.getImageCaption().getPoseTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.MODEL_POSE.name(), candidate.getImageCaption().getPoseTextEmb());
        }

        candidate.setDimensionVectors(dimensionVectors);
    }

    private Map<ClothShootGenreEnum, List<StyleImageCandidate>> groupByGenre(List<StyleImageCandidate> candidates,
            ProcessedInput input) {
        // 按流派分组
        Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups = new HashMap<>();

        // 初始化默认流派组
        Set<ClothShootGenreEnum> defaultGenres = new HashSet<>();
        defaultGenres.add(ClothShootGenreEnum.LOOKBOOK);
        defaultGenres.add(ClothShootGenreEnum.FASHION_BLOCKBUSTER);
        defaultGenres.add(ClothShootGenreEnum.COMMERCIAL_PHOTO);
        defaultGenres.add(ClothShootGenreEnum.SOCIAL_NETWORK);

        // 确定扩展流派（用户参考图中的走秀和对镜自拍）
        Set<ClothShootGenreEnum> extendedGenres = new HashSet<>();
        if (CollectionUtils.isNotEmpty(input.getUserRefImgDescriptions())) {
            for (UserRefImgDescription refDesc : input.getUserRefImgDescriptions()) {
                ImageAnalysisCaption captionModel = refDesc.getImgAnalysis();
                if (captionModel != null && captionModel.getShootingTheme() != null
                        && StringUtils.isNotBlank(captionModel.getShootingTheme().getGenre())) {

                    String genreStr = captionModel.getShootingTheme().getGenre();
                    ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genreStr);
                    if (genreEnum == ClothShootGenreEnum.SHOW || genreEnum == ClothShootGenreEnum.MIRROR_SELFIE) {
                        extendedGenres.add(genreEnum);
                    }
                }
            }
        }

        // 初始化所有流派组
        Set<ClothShootGenreEnum> allGenres = new HashSet<>(defaultGenres);
        allGenres.addAll(extendedGenres);

        for (ClothShootGenreEnum genre : allGenres) {
            genreGroups.put(genre, new ArrayList<>());
        }

        // 将候选图片按流派分组
        for (StyleImageCandidate candidate : candidates) {
            ClothShootGenreEnum genreEnum = candidate.getGenreEnum();

            // 只有在允许的流派中才加入分组
            if (genreEnum != null && genreGroups.containsKey(genreEnum)) {
                genreGroups.get(genreEnum).add(candidate);
            }
        }

        return genreGroups;
    }

    private Map<ClothShootGenreEnum, Integer> allocateGenreQuotas(
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups, ProcessedInput input, Integer totalQuota) {
        // 分配流派配额
        Map<ClothShootGenreEnum, Integer> genreQuotas = new HashMap<>();

        // 统计各类流派数量
        Set<ClothShootGenreEnum> genres = new HashSet<>();
        genres.add(ClothShootGenreEnum.LOOKBOOK);
        genres.add(ClothShootGenreEnum.FASHION_BLOCKBUSTER);
        genres.add(ClothShootGenreEnum.COMMERCIAL_PHOTO);
        genres.add(ClothShootGenreEnum.SOCIAL_NETWORK);

        Set<ClothShootGenreEnum> extendedGenres = new HashSet<>();
        if (CollectionUtils.isNotEmpty(input.getUserRefImgDescriptions())) {
            for (UserRefImgDescription refDesc : input.getUserRefImgDescriptions()) {
                ImageAnalysisCaption caption = refDesc.getImgAnalysis();
                if (caption != null && caption.getShootingTheme() != null
                        && StringUtils.isNotBlank(caption.getShootingTheme().getGenre())) {
                    String genreStr = caption.getShootingTheme().getGenre();
                    ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genreStr);
                    if (genreEnum == ClothShootGenreEnum.SHOW || genreEnum == ClothShootGenreEnum.MIRROR_SELFIE) {
                        extendedGenres.add(genreEnum);
                    }
                }
            }
        }

        genres.addAll(extendedGenres);

        // 分配默认流派配额（各流派平分）
        for (ClothShootGenreEnum genre : genres) {
            if (genreGroups.containsKey(genre)) {
                genreQuotas.put(genre, totalQuota / genres.size());
            }
        }

        return genreQuotas;
    }

    private List<List<StyleImageRecommendation>> clusterByBgWithinGenre(
            List<StyleImageCandidate> candidates, int quotaOfGenre, ClothShootGenreEnum genre, ProcessedInput input)
            throws Exception {

        log.info("【耗时统计】开始对流派{}进行背景聚类，候选图片数量：{}, 配额：{}", genre.getDisplayName(), candidates.size(), quotaOfGenre);

        // 4.3.1 计算匹配度评分
        long matchScoreStartTime = System.currentTimeMillis();
        if (candidates.get(0).getImageCaption().getImgEmb() == null) {
            imageCaptionService.fillSortVectors(candidates);
        }
        sortModelService.calculateMatchScoresBatch(candidates, input);
        long matchScoreEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}匹配度批量计算完成，耗时：{}ms", genre.getDisplayName(), matchScoreEndTime - matchScoreStartTime);

        if (candidates.isEmpty()) {
            log.warn("流派{}经过匹配度过滤后无候选图片", genre.getDisplayName());
            return new ArrayList<>();
        }

        // 去除匹配分小于阈值0.3的图片（如果匹配分小于阈值0.3的图片数量小于图片数量一半，则去除匹配分小于阈值的图片，保护测试期数据量小的情况）
        double th = 0.3;
        int n = candidates.size();
        if (candidates.stream().filter(candidate -> candidate.getMatchScore() < th).count() < n / 2) {
            candidates = candidates.stream().filter(candidate -> candidate.getMatchScore() >= th).toList();
            log.info("【背景聚类-低匹配分过滤】流派{}进行低匹配分过滤后，th={}, 图片数量由{} -> {}，过滤掉{}张图片", genre.getDisplayName(), th, n,
                    candidates.size(), n - candidates.size());
        }

        // 4.3.2 背景聚类
        long clusteringStartTime = System.currentTimeMillis();
        List<List<StyleImageCandidate>> backgroundClusters = performBackgroundClustering(candidates);
        long clusteringEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}背景聚类完成，生成{}个聚类，耗时：{}ms",
                genre.getDisplayName(), backgroundClusters.size(), clusteringEndTime - clusteringStartTime);

        if (backgroundClusters.isEmpty()) {
            log.warn("流派{}没有满足最小聚类大小要求的聚类", genre.getDisplayName());
            return new ArrayList<>();
        }

        // 4.3.3 聚类排序（类内排序和类间排序）
        sortByScore(genre, backgroundClusters);

        // 4.3.4 聚类裁剪
        return cutByQuota(genre, backgroundClusters);
    }

    @NotNull
    private List<List<StyleImageRecommendation>> cutByQuota(ClothShootGenreEnum genre,
            List<List<StyleImageCandidate>> backgroundClusters) {
        // 4.3.4 聚类排序和配额分配
        List<List<StyleImageRecommendation>> items = new ArrayList<>();

        // 计算每个聚类应分配的配额（每个聚类最多30张）
        int baseQuotaPerCluster = 10; // todo最终上线是30张（业务要求），这里测试时取10

        ImageQuery imageQuery = new ImageQuery();
        imageQuery.setIds(backgroundClusters.stream().flatMap(List::stream).map(c -> c.getImageCaption().getImageId())
                .collect(Collectors.toList()));
        List<ImageVO> imageVOList = imageService.queryImageList(imageQuery);
        Map<Integer, ImageVO> imageMap = imageVOList.stream().collect(Collectors.toMap(ImageVO::getId, v -> v));

        // 4.3.5 从聚类中选择最佳候选
        for (int clusterIndex = 0; clusterIndex < backgroundClusters.size(); clusterIndex++) {
            List<StyleImageCandidate> cluster = backgroundClusters.get(clusterIndex);

            // 从当前聚类中选择最佳候选
            List<StyleImageRecommendation> itemsOfCluster = new ArrayList<>();

            int selectedFromCluster = 0;
            for (StyleImageCandidate candidate : cluster) {
                if (selectedFromCluster >= baseQuotaPerCluster) {
                    break;
                }

                StyleImageRecommendation item = convertToRecommendationItem(candidate, genre, clusterIndex, imageMap);
                if (item != null) {
                    itemsOfCluster.add(item);
                    selectedFromCluster++;
                }
            }

            items.add(itemsOfCluster);
        }

        for (int i = 0; i < items.size(); i++) {
            log.info("【背景聚类-结果】流派{}聚类{}，大小：{}，前5平均分：{}，前5名分数:{}", genre.getDisplayName(), i, items.get(i).size(),
                    String.format("%.3f",
                            items.get(i).stream().limit(5).mapToDouble(StyleImageRecommendation::getMatchScore)
                                    .average().orElse(0.0)),
                    items.get(i).stream().map(StyleImageRecommendation::getMatchScore).limit(5).toArray());
        }
        return items;
    }

    private static void sortByScore(ClothShootGenreEnum genre, List<List<StyleImageCandidate>> backgroundClusters) {

        int sortLimit = 5;

        backgroundClusters.forEach(cluster -> {
            log.info("【背景聚类-类内排序前】流派{}，大小：{}，前{}平均分：{}，前{}名分数:{}", genre.getDisplayName(), cluster.size(), sortLimit,
                    String.format("%.3f",
                            cluster.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore).average()
                                    .orElse(0.0)),
                    sortLimit,
                    cluster.stream().map(StyleImageCandidate::getMatchScore).limit(sortLimit).toArray());

            cluster.sort(
                    (candidate1, candidate2) -> Double.compare(candidate2.getMatchScore(), candidate1.getMatchScore()));

            log.info("【背景聚类-类内排序后】流派{}，大小：{}，前{}平均分：{}，前{}名分数:{}", genre.getDisplayName(), cluster.size(), sortLimit,
                    String.format("%.3f",
                            cluster.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore).average()
                                    .orElse(0.0)),
                    sortLimit,
                    cluster.stream().map(StyleImageCandidate::getMatchScore).limit(sortLimit).toArray());
        });

        // 按聚类内部取前5名元素的平均匹配分进行排序，匹配度高的聚类优先分配
        backgroundClusters.sort((cluster1, cluster2) -> {
            double avgScore1 = cluster1.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore)
                    .average().orElse(0.0);
            double avgScore2 = cluster2.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore)
                    .average().orElse(0.0);
            return Double.compare(avgScore2, avgScore1);
        });

        for (int i = 0; i < backgroundClusters.size(); i++) {
            log.info("【背景聚类-类间排序后】流派{}聚类{}，大小：{}，前{}平均分：{}，前{}名分数:{}", genre.getDisplayName(), i,
                    backgroundClusters.get(i).size(), sortLimit,
                    String.format("%.3f",
                            backgroundClusters.get(i).stream().limit(sortLimit)
                                    .mapToDouble(StyleImageCandidate::getMatchScore).average().orElse(0.0)),
                    sortLimit,
                    backgroundClusters.get(i).stream().map(StyleImageCandidate::getMatchScore).limit(sortLimit)
                            .toArray());
        }
    }

    /**
     * 将StyleImageCandidate转换为ClothShootStyleImageRecommendationItem
     */
    private StyleImageRecommendation convertToRecommendationItem(
            StyleImageCandidate candidate, ClothShootGenreEnum genre, int backgroundClusterIndex,
            Map<Integer, ImageVO> imageMap) {
        try {
            StyleImageRecommendation item = new StyleImageRecommendation();
            item.setClothShootGenreEnum(genre);
            item.setImageId(candidate.getImageCaption().getImageId());
            item.setBackgroundClusterId(String.format("%s_%d", genre.getCode(), backgroundClusterIndex));
            item.setMatchScore(candidate.getMatchScore());
            item.setStyleSimilarity(candidate.getStyleSimilarity());

            ImageVO imageVO = imageMap.get(candidate.getImageCaption().getImageId());
            if (imageVO != null) {
                item.setImageUrl(imageVO.getUrl());
                item.setImageShowUrl(imageVO.getShowImgUrl());
            }

            return item;
        } catch (Exception e) {
            log.error("转换推荐项失败，候选图片ID：{}", candidate.getImageCaption().getImageId(), e);
            return null;
        }
    }

    /**
     * 执行背景聚类
     */
    private List<List<StyleImageCandidate>> performBackgroundClustering(List<StyleImageCandidate> candidates) {
        // 提取有效候选者和背景向量
        List<StyleImageCandidate> validCandidates = new ArrayList<>();
        List<double[]> vectorList = new ArrayList<>();

        for (StyleImageCandidate candidate : candidates) {
            PGvector bgVector = candidate.getImageCaption().getBgTextEmb();
            if (bgVector != null) {
                validCandidates.add(candidate);
                // 直接转换为double[]，避免中间变量
                float[] floatVector = bgVector.toArray();
                double[] doubleVector = new double[floatVector.length];
                for (int j = 0; j < floatVector.length; j++) {
                    doubleVector[j] = floatVector[j];
                }
                vectorList.add(doubleVector);
            }
        }

        AssertUtil.assertNotEmpty(validCandidates, "背景聚类:没有有效的候选者");

        // 转换为二维数组
        double[][] vectors = vectorList.toArray(new double[0][]);

        double[][] reducedVectors;

        // PCA降维（超过512维时）
        if (vectors[0].length >= 512) {
            long pcaStart = System.currentTimeMillis();
            reducedVectors = PCAUtil.performPCA(vectors, 128);
            log.info("PCA降维耗时：{}ms，维度：{} -> {}",
                    System.currentTimeMillis() - pcaStart, vectors[0].length, reducedVectors[0].length);
        } else {
            reducedVectors = vectors;
        }

        // HDBSCAN进行聚类
        return ClusteringUtil.performHDBSCANClustering(validCandidates, reducedVectors);
    }

    /**
     * 输出详细的耗时分析报告
     */
    private String logPerformanceReport(Map<String, Long> performanceMetrics, StyleImageRecommendResult result) {
        StringBuilder report = new StringBuilder();
        report.append("\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");
        report.append("【服装拍摄风格图片推荐算法 - 耗时分析报告】\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");

        long totalTime = performanceMetrics.get("总耗时");

        // 1. 各环节耗时详情（按耗时排序）
        report.append("📊 各算法环节耗时详情：\n");
        performanceMetrics.entrySet().stream()
                .filter(entry -> !"总耗时".equals(entry.getKey()))
                .forEach(entry -> {
                    String stepName = entry.getKey();
                    Long duration = entry.getValue();
                    double percentage = (duration * 100.0) / totalTime;
                    String icon = percentage > 30 ? "🔴" : percentage > 15 ? "🟡" : "🟢";
                    report.append(
                            String.format("  %s %-15s: %6d ms (%5.1f%%)\n", icon, stepName, duration, percentage));
                });
        report.append(String.format("  └─ %-15s: %6d ms (100.0%%)\n", "总耗时", totalTime));

        return report.toString();
    }

}