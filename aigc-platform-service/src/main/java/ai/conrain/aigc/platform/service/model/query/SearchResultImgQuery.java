package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * SearchResultImgQuery
 *
 * @version SearchResultImgService.java v 0.1 2025-08-16 05:50:47
 */
@Data
public class SearchResultImgQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** 主键 */
        private Integer id;

        /** 搜索记录id */
        private Integer searchId;

        /** 搜索结果批次，唯一标识符 */
        private String searchRetBatch;

        /** 图片id */
        private Integer imageId;

        /** 图片url */
        private String imageUrl;

        /** 图片展示url */
        private String imageShowUrl;

        /** 图片打标id */
        private Integer imageCaptionId;

        /** 图片类别 */
        private String genre;

        /** 背景聚类id */
        private Integer bgClusterId;

        /** 风格相似度 */
        private Double styleSimilarity;

        /** 匹配得分 */
        private Double matchScore;

        /** 扩展字段 */
        private String extInfo;

        /** 创建时间 */
        private Date createTime;

        /** 修改时间 */
        private Date modifyTime;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}