package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class CreatePoseSampleDiagramCreativeService extends FixedPostureCreationCreativeService {
    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private ComfyUIHelper comfyUIHelper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.POSE_SAMPLE_DIAGRAM;
    }

    @Override
    protected MaterialModelVO fetchModel(AddCreativeRequest request) {
        MaterialModelVO modelVO = new MaterialModelVO();
        modelVO.setVersion(ModelVersionEnum.FLUX);
        modelVO.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
        return modelVO;
    }

    @Override
    protected CreativeBatchVO buildData(AddCreativeRequest request, MaterialModelVO modelVO) {
        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildData::开始构建CreativeBatchVO 批次数据...");
        // 获取批次对象
        CreativeBatchVO creativeBatchVO = super.buildData(request, modelVO);

        // 设置为 ‘示例图创作’ 用户
        creativeBatchVO.setUserId(CommonUtil.mockPostureSampleDiagramContext().getMasterUser());

        // 设置任务数量为1
        creativeBatchVO.setBatchCnt(1);

        // 添加创建图片数量
        creativeBatchVO.addExtInfo(IMAGE_COUNT, request.getPoseSampleDiagram().getImgNum());

        // 设置图片比例
        creativeBatchVO.setImageProportion(request.getPoseSampleDiagram().getProportion());

        // 添加姿势示例图关键字
        creativeBatchVO.addExtInfo(KEY_BIZTAG, POSE_SAMPLE_DIAGRAM);

        // 设置 level 为 3 的场景id
        creativeBatchVO.addExtInfo(KEY_SCENE_ID, request.getPoseSampleDiagram().getCurrentPoseId());
        // 设置CURRENT_POSE_ID
        creativeBatchVO.addExtInfo(CURRENT_POSE_ID, request.getPoseSampleDiagram().getCurrentPoseId());


        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildData::CreativeBatchVO 批次数据构建完成:{}",
                creativeBatchVO);

        // 返回结果
        return creativeBatchVO;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据...");

        // 反向解析参考图
        List<AddCreativeRequest.ReferenceInfo> referenceInfoList = objectMapper.convertValue(
                batch.getExtInfo().get(REFERENCE_INFO_LIST), new TypeReference<List<AddCreativeRequest.ReferenceInfo>>() {
                });

        if (CollectionUtils.isEmpty(referenceInfoList)) {
            return null;
        }


        // 获取参考图配置信息
        AddCreativeRequest.ReferenceInfo referenceInfo = referenceInfoList.get(0);
        if (referenceInfo == null) {
            log.warn("[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::参考图配置信息为空,跳过姿势:{}", batch.getIntegerFromExtInfo(CURRENT_POSE_ID));
            return new ArrayList<>();
        }


        // 构建任务数据
        List<CreativeTaskVO> result = new ArrayList<>();

        // 构建任务信息
        CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);


        // 填充场景信息
        CreativeElementVO sceneElement = creativeElementService.selectById(referenceInfo.getLoraId());
        elements.add(sceneElement);
        // 填充扩展信息,i为referenceInfo的索引
        fillTaskExt(target, batch, elements, 1);

        // 获取姿势图片地址
        String referenceImageUrl = StringUtils.EMPTY;
        try {

            String imageUrl = referenceInfo.getOriginalImageUrl();
            if (StringUtils.isBlank(imageUrl) && referenceInfo.getReferenceConfig() != null) {
                imageUrl = referenceInfo.getReferenceConfig().getString(CommonConstants.ORIGINAL_IMAGE_URL);
            }

            if (StringUtils.isBlank(imageUrl)) {
                log.error(
                        "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::原始参考图为空，终止流程");
                throw new BizException(ResultCode.BIZ_FAIL);
            }

            referenceImageUrl = comfyUIHelper.upLoadImage(imageUrl);
        } catch (IOException e) {
            log.error(
                    "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::图片上传至 ComfyUI 时出现错误::{}",
                    e.getMessage());
        }


        // 设置 loraId
        target.addExtInfo(KEY_MODEL_ID, referenceInfo.getLoraId());

        //  设置任务类型
        target.setType(getCreativeType());

        // 添加 参考图图片
        target.addExtInfo(CommonConstants.REFERENCE_IMAGE, referenceImageUrl);

        // 添加参考图原图片
        target.addExtInfo(CommonConstants.REFERENCE_ORIGINAL_IMAGE, referenceInfo.getImageUrl());
        // 添加风格lora配置
        if (Objects.nonNull(referenceInfo.getReferenceConfig())) {
            // 设置 lens
            target.addExtInfo(CommonConstants.KEY_LENS,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_LENS));
            // 设置 posture
            target.addExtInfo(CommonConstants.KEY_POSTURE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_POSTURE));
            // 设置 style
            target.addExtInfo(CommonConstants.KEY_STYLE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_STYLE));
        }
        // 设置扩展标签
        target.addExtInfo(CommonConstants.BACK_TAGS, referenceInfo.getBackExtTags());
        // 设置 lora 地址
        target.addExtInfo(CommonConstants.KEY_LORA_PATH, referenceInfo.getLoraPath());

        // 设置图片比例
        target.setImageProportion(batch.getImageProportion());

        target.setBatchCnt(batch.getIntegerFromExtInfo(IMAGE_COUNT));

        // 设置为 ‘示例图创作’ 用户
        batch.setUserId(CommonUtil.mockPostureSampleDiagramContext().getMasterUser());

        // 设置姿势场景 id
        target.addExtInfo(CommonConstants.KEY_POSE_ELEMENT_ID, batch.getIntegerFromExtInfo(CURRENT_POSE_ID));

        // 添加姿势示例图关键字
        target.addExtInfo(CommonConstants.KEY_BIZTAG, CommonConstants.POSE_SAMPLE_DIAGRAM);

        // 插入任务信息
        CreativeTaskVO data = creativeTaskService.insert(target);

        // 添加至集合中
        result.add(data);

        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::CreativeTaskVO 任务数据构建完成:{}",
                result);

        return result;
    }

    @Override
    protected Map<Integer, List<Integer>> getConfigs(AddCreativeRequest request) {
        return request.getPoseSampleDiagram().getConfigs();
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task, Map<String, Object> context) {
        // 调用祖父类方法，传递姿势示例图的日志前缀
        String flowKey = determineFlowKeyWithLogging(elements, modelVO, task, context, "【姿势示例图创作】");

        // 将通用流程映射为固定姿势创作专用流程
        return mapToFixedPostureFlow(flowKey);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context) {
        // 调用父级方法
        super.preParse(task, elements, modelVO, context);

        // 1-2048的随机数字,无法发挥随机数seed，调整成15位
        long promptSeed = Long.parseLong(RandomStringUtils.randomNumeric(15));
        // 重新进行数据处理，覆盖父类对于服装部分的修改
        dataCorrect(elements, modelVO, context, task, promptSeed);
    }

    /**
     * 数据修正
     *
     * @param elements   元素列表
     * @param modelVO    模型数据
     * @param context    配置map上下文
     * @param task       任务
     * @param promptSeed prompt的种子
     */
    protected void dataCorrect(List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context,
                               CreativeTaskVO task, long promptSeed) {
        // 模型修正
        super.dataCorrect(elements, modelVO, context, task, promptSeed);

        CreativeElementVO originSceneElement = elements.stream()
                .filter(element -> element.getConfigKey().equals(ElementConfigKeyEnum.SCENE.name())
                        && element.getLevel() == 3)
                .findFirst()
                .orElse(null);

        CreativeElementVO originFaceElement = elements.stream()
                .filter(element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())
                        && element.getLevel() == 3)
                .findFirst()
                .orElse(null);

        CreativeElementVO sceneElement = CommonUtil.deepCopy(originSceneElement);
        CreativeElementVO faceElement = CommonUtil.deepCopy(originFaceElement);

        // 重新构建模型信息
        buildModelVoByScene(sceneElement, modelVO, context);

        // 重新构建模特信息
        buildFaceVoByFace(faceElement, context, task);
    }


    /**
     * 构建模型信息
     *
     * @param sceneElement 场景元素
     * @param modelVO      模型信息
     * @param context      上下文
     */
    private void buildModelVoByScene(CreativeElementVO sceneElement, MaterialModelVO modelVO,
                                     Map<String, Object> context) {
        // 参数校验
        if (sceneElement == null) {
            log.warn("场景元素为空，无法构建模型信息");
            return;
        }
        if (context == null) {
            context = new HashMap<>();
        }

        String lensInfo = sceneElement.getExtInfo(KEY_LENS, String.class);
        String tempLensInfo = null;
        boolean isLowerBody = false;
        boolean isUpperBody = false;

        // 处理镜头信息
        if (StringUtils.isNotBlank(lensInfo)) {
            if (StringUtils.containsIgnoreCase(lensInfo, "lower")) {
                tempLensInfo = CommonConstants.KEY_CLOTH_LOWER_BODY_PROMPT;
                isLowerBody = true;
            } else if (StringUtils.containsIgnoreCase(lensInfo, "upper") && StringUtils.containsIgnoreCase(lensInfo,
                    "front")) {
                tempLensInfo = CommonConstants.KEY_CLOTH_UPPER_BODY_FRONT_PROMPT;
                isUpperBody = true;
            }
        }

        modelVO.setTags(tempLensInfo);

        // 处理服装信息
        Object outfitExtInfo = sceneElement.getExtInfo(CommonConstants.KEY_STYLE_OUTFIT);
        if (outfitExtInfo != null) {
            try {
                JSONObject outfitJson;
                if (outfitExtInfo instanceof String) {
                    outfitJson = JSONObject.parseObject((String) outfitExtInfo);
                } else if (outfitExtInfo instanceof JSONObject) {
                    outfitJson = (JSONObject) outfitExtInfo;
                } else {
                    log.warn("服装信息格式不正确，sceneId={}, type={}", sceneElement.getId(),
                            outfitExtInfo.getClass().getName());
                    return;
                }

                StringBuilder outfitTags = new StringBuilder();

                // 根据镜头信息决定添加哪些服装标签
                if (!isLowerBody) {
                    if (outfitJson.containsKey("innerwear") && outfitJson.getString("innerwear") != null) {
                        outfitTags.append("innerwear: ").append(outfitJson.getString("innerwear")).append(", ");
                    }
                    if (outfitJson.containsKey("outerwear") && outfitJson.getString("outerwear") != null) {
                        outfitTags.append("outerwear: ").append(outfitJson.getString("outerwear")).append(", ");
                    }
                }

                if (!isUpperBody) {
                    if (outfitJson.containsKey("bottoms") && outfitJson.getString("bottoms") != null) {
                        outfitTags.append("bottoms: ").append(outfitJson.getString("bottoms")).append(", ");
                    }
                    if (outfitJson.containsKey("shoes") && outfitJson.getString("shoes") != null) {
                        outfitTags.append("shoes: ").append(outfitJson.getString("shoes")).append(", ");
                    }
                }

                // 处理accessories数组
                if (outfitJson.containsKey("accessories")) {
                    List<String> accessories = outfitJson.getJSONArray("accessories").toJavaList(String.class);
                    if (CollectionUtils.isNotEmpty(accessories)) {
                        outfitTags.append("accessories: ").append(String.join(", ", accessories)).append(", ");
                    }
                }

                // 移除最后的逗号和空格
                if (outfitTags.length() > 0) {
                    outfitTags.setLength(outfitTags.length() - 2);
                }

                // 将拼接后的标签添加到 modelVO 的 extInfo的features 中
                if (outfitTags.length() > 0) {
                    // features
                    modelVO.addExtInfo(CommonConstants.KEY_FEATURES, outfitTags.toString());
                }
            } catch (Exception e) {
                log.error("处理服装信息时发生异常，sceneId={}, outfitExtInfo={}", sceneElement.getId(), outfitExtInfo,
                        e);
            }
        }

        // extTags
        modelVO.addExtInfo(CommonConstants.KEY_EXT_TAGS,
                sceneElement.getExtTags() != null ? sceneElement.getExtTags() : "");

        // version
        modelVO.setVersion(ModelVersionEnum.FLUX);

        // 从Spring配置文件中获取当前激活的环境
        String loraName;
        if (EnvUtil.isLocalEnv()) {
            loraName = "local/白色套装训练计划_100606_20250214_114825/白色套装训练计划_100606_20250214_114825-flux"
                    + "/白色套装训练计划_100606_20250214_114825-flux.safetensors";
        } else if (EnvUtil.isDevEnv()) {
            loraName = "dev/男款上衣_copy_2_100627_20250422_191515/男款上衣_copy_2_100627_20250422_191515-flux/男款上衣_copy_2_100627_20250422_191515-flux.safetensors";
        } else {
            loraName = "product/6308_8607_20250311_133310/6308_8607_20250311_133310-flux/6308_8607_20250311_133310-flux"
                    + ".safetensors";
        }
        modelVO.setLoraName(loraName);

        // 设置 aiGenFeatures ，设置之后 resetClothCollocation 会走新流程
        ClothCollocationModel clothCollocationModel = new ClothCollocationModel();
        clothCollocationModel.setShoe("");
        clothCollocationModel.setTops("");
        clothCollocationModel.setBottoms("");
        clothCollocationModel.setOthers("");
        clothCollocationModel.setProps("");


        modelVO.addExtInfo(CommonConstants.aiGenFeatures, JSONObject.toJSONString(clothCollocationModel));

        // 添加模型信息
        context.put("lora", modelVO);

        // 服装 lora 强度置为 0，尽可能不影响出图效果
        context.put("loraStrength", 0);
    }


    /**
     * 重新构建模特信息
     * @param faceElement 模特信息
     * @param context 上下文内容
     * @param task 任务信息
     */
    private void buildFaceVoByFace(CreativeElementVO faceElement, Map<String, Object> context, CreativeTaskVO task) {
        // 若 faceElement 不为 null 则直接返回，不再执行该 mock 逻辑
        if (faceElement != null) {
            return;
        }

        // 1、初始化模特信息
        faceElement = new CreativeElementVO();

        //noinspection unchecked
        List<String> cameraAngle = task.getExtInfo(KEY_CAMERA_ANGLE, List.class);
        // 额外添加一个背部视角
        cameraAngle.add(CameraAngleEnum.BACK_VIEW.getCode());
        task.addExtInfo(KEY_CAMERA_ANGLE, cameraAngle);


        faceElement.setParentId(1);
        faceElement.setLevel(3);
        faceElement.setConfigKey(ElementConfigKeyEnum.FACE.name());
        faceElement.setStatus(ElementStatusEnum.PROD);
        // 初始化扩展信息
        faceElement.setExtInfo(new JSONObject());

        faceElement.addExtInfo(KEY_REPAIR_FACE_TYPE, NO);

        // 其他脸部图片置为空
        faceElement.addExtInfo(KEY_FACE_IMAGE_MORE, null);
        // face lora 强度置为 0
        faceElement.addExtInfo(KEY_FACE_LORA_STRENGTH, 0);
        // extTags 置为空
        faceElement.setExtTags(StringUtils.EMPTY);
        // tags 置为空
        faceElement.setTags(StringUtils.EMPTY);
        // expression 置为空
        faceElement.addExtInfo(KEY_EXPRESSION, StringUtils.EMPTY);
        // hairstyle 置为空
        faceElement.addExtInfo(KEY_HAIRSTYLE, StringUtils.EMPTY);
        // negative 置为空
        faceElement.addExtInfo(KEY_NEGATIVE, StringUtils.EMPTY);
        // 设置faceRestoreVisibility
        faceElement.addExtInfo(KEY_FACE_RESTORE_VISIBILITY, "0.1");


        // 添加模特提示词
        String facePrompt = task.getStringFromExtInfo(KEY_FACE_PROMPT);
        if (StringUtils.isNotEmpty(facePrompt)) {
            faceElement.addExtInfo(KEY_EXPRESSION, facePrompt);
        }


        // 添加模特信息
        context.put("FACE", faceElement);
    }

}
