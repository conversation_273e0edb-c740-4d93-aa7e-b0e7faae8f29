/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MULTIPLY;

/**
 * 创作工具类
 *
 * <AUTHOR>
 * @version : CreativeUtils.java, v 0.1 2025/7/23 14:45 renxiao.wu Exp $
 */
public abstract class CreativeUtils {
    /**
     * 格式化服装激活词
     *
     * @param modelTags    服装激活词
     * @param sceneElement 场景元素
     * @return 格式化后的服装激活词
     */
    public static String formatModelTags(String modelTags, CreativeElementVO sceneElement) {
        if (!ElementUtils.isStyleScene(sceneElement)) {
            return modelTags;
        }

        String activateKey = StringUtils.contains(modelTags, "(linrun2111:1.3)") ? "(linrun2111:1.3), "
            : (StringUtils.contains(modelTags, "linrun2111") ? "linrun2111, " : "");

        String sceneLens = sceneElement.getExtInfo(KEY_LENS, String.class);
        if (StringUtils.contains(modelTags, "mid shot") || StringUtils.contains(modelTags, "close shot")) {
            sceneLens = StringUtils.replace(sceneLens, CameraAngleEnum.UPPER_BODY.getCode(), "");
            sceneLens = StringUtils.replace(sceneLens, CameraAngleEnum.LOWER_BODY.getCode(), "");
            sceneLens = StringUtils.replace(sceneLens, ",,", ",");

            modelTags = activateKey + sceneLens + (StringUtils.contains(modelTags, "mid shot") ? "mid shot," : "") + (
                StringUtils.contains(modelTags, "close shot") ? "close shot," : "");
        } else {
            modelTags = activateKey + sceneLens;
        }

        //去掉原有场景的角度
        sceneElement.getExtInfo().remove(KEY_LENS);

        return modelTags;
    }

    /**
     * 填充图片尺寸
     *
     * @param context    上下文
     * @param proportion 比例
     */
    public static void fillImageSizeFromProportion(Map<String, Object> context, String proportion) {
        String[] proportions = getImageSizeFromProportion(proportion);

        if (proportions == null) {
            return;
        }

        context.put("width", proportions[0]);
        context.put("height", proportions[1]);
    }

    public static String[] getImageSizeFromProportion(String proportion) {
        if (StringUtils.isBlank(proportion)) {
            return null;
        }

        ProportionTypeEnum proportionType = ProportionTypeEnum.getByCode(proportion);

        Integer width = null;
        Integer height = null;
        if (proportionType != null) {
            width = proportionType.getWidth();
            height = proportionType.getHeight();
        } else if (StringUtils.contains(proportion, KEY_MULTIPLY)) {
            String[] split = StringUtils.split(proportion, KEY_MULTIPLY);
            width = Integer.parseInt(split[0]);
            height = Integer.parseInt(split[1]);
        } else {
            return new String[] {null, null};
        }

        return new String[] {width + "", height + ""};
    }

    /**
     * 构建比例
     *
     * @param width  宽度
     * @param height 高度
     * @return 比例
     */
    public static String buildProportion(Integer width, Integer height) {
        return width + KEY_MULTIPLY + height;
    }

}
