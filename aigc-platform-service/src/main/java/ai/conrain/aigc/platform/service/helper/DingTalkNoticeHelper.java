package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.model.dingding.DingTalkNotifyMarkdownMessage;
import ai.conrain.aigc.platform.service.enums.DingTalkMessageEnum;

import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.Date;
import java.util.List;

@Slf4j
public class DingTalkNoticeHelper {

    public static final String PROD_TOKEN = "1d3d7981c24c34adff1957076b01bbc28ab2b20389652beca9dd3040ab3cc857";
    public static final String MONITOR_TOKEN = "384a30bff37ba432bd8dfa18e6b83ce4aecf6b348c53e3f7af0f5591dfdd973f";

    // 用户注册机器人 - token
    public static final String USER_REGISTER_ROBOT_TOKEN
        = "fb1fbc2e9c0f88794b5dae18452d68af8ca2b86f73ed8c934c60719bc1168456";

    /**
     * 发送消息
     * <p>
     * <a href=
     * "https://open.dingtalk.com/document/orgapp/custom-bot-send-message-type">...</a>
     *
     * @param msg         消息内容或Markdown消息对象
     * @param atMobiles   需要@的手机号列表
     * @param messageType 消息类型，默认为TEXT
     * @param accessToken 机器人访问令牌
     */
    public static void sendMessage(Object msg, List<String> atMobiles, DingTalkMessageEnum messageType,
                                   String accessToken) {
        if (msg == null || accessToken == null) {
            log.error("【钉钉消息】消息内容或accessToken不能为空");
            return;
        }

        // 非生产环境时，忽略消息
        if (!EnvUtil.isProdEnv()) {
            log.info("【钉钉消息】非生产环境忽略消息msg:{}, atMobiles:{}", msg, atMobiles);
            return;
        }

        // 消息类型为空时，默认为TEXT类型
        if (messageType == null) {
            messageType = DingTalkMessageEnum.TEXT;
        }

        // 根据消息类型，发送不同消息
        switch (messageType) {
            case MARKDOWN:
                if (!(msg instanceof DingTalkNotifyMarkdownMessage)) {
                    log.error("【钉钉消息】【Markdown】消息类型为MARKDOWN时，msg参数必须是DingTalkNotifyMarkdownMessage类型");
                    return;
                }
                sendMarkdownMessage((DingTalkNotifyMarkdownMessage)msg, accessToken, atMobiles);
                break;
            case TEXT:
                if (!(msg instanceof String)) {
                    log.error("【钉钉消息】【Text】消息类型为TEXT时，msg参数必须是String类型");
                    return;
                }
                sendMessage((String)msg, accessToken, atMobiles);
                break;
        }
    }

    /**
     * 发送开发消息（文本类型）
     *
     * @param msg       消息内容
     * @param atMobiles 需要@的手机号列表
     */
    public static void sendMsg2DevGroup(String msg, List<String> atMobiles) {
        sendMessage(msg, atMobiles, DingTalkMessageEnum.TEXT, MONITOR_TOKEN);
    }

    /**
     * 发送开发消息（文本类型）
     *
     * @param msg 消息内容
     */
    public static void sendMsg2DevGroup(String msg) {
        sendMessage(msg, null, DingTalkMessageEnum.TEXT, MONITOR_TOKEN);
    }

    /**
     * 发送业务消息（文本类型）
     *
     * @param msg       消息内容
     * @param atMobiles 需要@的手机号列表
     */
    public static void sendMsg2BizGroup(String msg, List<String> atMobiles) {
        sendMessage(msg, atMobiles, DingTalkMessageEnum.TEXT, PROD_TOKEN);
    }

    /**
     * 发送业务消息（文本类型）
     *
     * @param msg 消息内容
     */
    public static void sendMsg2BizGroup(String msg) {
        sendMessage(msg, null, DingTalkMessageEnum.TEXT, PROD_TOKEN);
    }

    /**
     * 发送用户注册消息（Msg类型）
     * @param atMobiles 需要@的手机号列表
     */
    public static void sendMsg2UserRegister(String msg, List<String> atMobiles) {
        sendMessage(msg, atMobiles, DingTalkMessageEnum.TEXT, USER_REGISTER_ROBOT_TOKEN);
    }

    /**
     * 发送用户注册消息（Msg类型）
     */
    public static void sendMsg2UserRegister(String msg) {
        sendMessage(msg, null, DingTalkMessageEnum.TEXT, USER_REGISTER_ROBOT_TOKEN);
    }

    /**
     * 发送普通消息
     *
     * @param msg         消息内容
     * @param accessToken 机器人访问令牌
     * @param atMobiles   需要@的手机号列表
     */
    private static void sendMessage(String msg, String accessToken, List<String> atMobiles) {
        try {
            HttpClient httpclient = HttpClients.createDefault();
            HttpPost httppost = new HttpPost("https://oapi.dingtalk.com/robot/send?access_token=" + accessToken);
            httppost.addHeader("Content-Type", "application/json; charset=utf-8");

            DingTalkNotifyTextMessage dingTalkMessage = new DingTalkNotifyTextMessage();
            dingTalkMessage.setMsgtype("text");

            DingTalkNotifyTextMessage.Text text = new DingTalkNotifyTextMessage.Text();
            StringBuffer msgBuffer = new StringBuffer();
            msgBuffer.append(
                String.format("[MuseGate %s %s]\n%s", DateUtils.formatTime(new Date()), EnvUtil.getEnv().toUpperCase(),
                    msg));
            text.setContent(msgBuffer.toString());
            dingTalkMessage.setText(text);

            if (CollectionUtils.isNotEmpty(atMobiles)) {
                DingTalkNotifyTextMessage.At at = new DingTalkNotifyTextMessage.At();
                at.setAtMobiles(atMobiles);
                dingTalkMessage.setAt(at);
            }

            StringEntity se = new StringEntity(JSONObject.toJSONString(dingTalkMessage), "utf-8");

            log.info("钉钉消息发送内容：{}, {}", JSONObject.toJSONString(dingTalkMessage), atMobiles);

            httppost.setEntity(se);
            HttpResponse response = httpclient.execute(httppost);

            log.info("钉钉消息发送状态：{}", response.getStatusLine().getStatusCode());

            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity(), "utf-8");
                log.info("钉钉消息发送结果：{}", result);
            }

        } catch (Throwable e) {
            log.error("发消息到钉钉群异常，忽略", e);
        }
    }

    /**
     * 发送 Markdown 格式消息到钉钉
     *
     * @param message     Markdown 消息对象
     * @param accessToken 机器人访问令牌
     * @param atMobiles   需要@的手机号列表
     */
    private static void sendMarkdownMessage(DingTalkNotifyMarkdownMessage message, String accessToken,
                                            List<String> atMobiles) {
        try {
            HttpClient httpclient = HttpClients.createDefault();
            HttpPost httppost = new HttpPost("https://oapi.dingtalk.com/robot/send?access_token=" + accessToken);
            httppost.addHeader("Content-Type", "application/json; charset=utf-8");

            // 在原有 markdown 内容后添加 @ 信息
            if (CollectionUtils.isNotEmpty(atMobiles)) {
                DingTalkNotifyMarkdownMessage.At at = new DingTalkNotifyMarkdownMessage.At();
                at.setAtMobiles(atMobiles);
                message.setAt(at);
                
                // 获取原有的 markdown 内容
                DingTalkNotifyMarkdownMessage.Markdown markdown = message.getMarkdown();
                String originalText = markdown.getText();
                
                // 添加 @ 信息，使用钉钉的 @ 格式
                StringBuilder newText = new StringBuilder(originalText);
                newText.append("\n\n"); // 换行
                for (String mobile : atMobiles) {
                    newText.append("@").append(mobile).append(" ");
                }
                
                // 设置新的内容
                markdown.setText(newText.toString());
                message.setMarkdown(markdown);
            }

            StringEntity se = new StringEntity(JSONObject.toJSONString(message), "utf-8");
            httppost.setEntity(se);

            log.info("[markdown]钉钉消息发送内容：{}, {}", JSONObject.toJSONString(se), atMobiles);

            HttpResponse response = httpclient.execute(httppost);

            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity(), "utf-8");
                log.info("Markdown消息发送结果：{}", result);
            }

        } catch (Throwable e) {
            log.error("发送Markdown消息到钉钉群异常", e);
        }
    }



}
