package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.agent.ShootStyleImageRecommendParam;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendResult;

/**
 * 图像检索服务
 */
public interface ImageSearchService {

    /**
     * 搜索并推荐
     * @param param
     * @return
     */
    StyleImageRecommendResult searchAndRecommend(ShootStyleImageRecommendParam param);
}
