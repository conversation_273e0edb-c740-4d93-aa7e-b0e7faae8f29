package ai.conrain.aigc.platform.service.model.biz.agent;

import lombok.Data;

import java.util.List;

@Data
public class ShootStyleImageRecommendParam extends ClothImgItem {
    // 用户上传的拍摄风格参考图片
    private List<UserUploadStyleImg> userUploadStyleImgs;
    
    // 是否启用流派并发查询模式
    private Boolean enableGenreConcurrency = true;

    //排除当前对话所有已经返回的批次，用于完全重新出一批
    private List<String> excludeRetBatch;

    //指定的流派，用于搜索更多指定流派的图
    private List<String> specifiedGenres;

    //指定的风格参考图Id，用于基于单张图搜索更多类似
    private Integer specifiedRetImgId;
}
