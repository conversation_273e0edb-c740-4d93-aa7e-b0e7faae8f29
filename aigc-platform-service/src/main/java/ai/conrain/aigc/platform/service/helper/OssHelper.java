/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import javax.imageio.ImageIO;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.model.XOssProcessEnum;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * oss服务帮助类
 *
 * <AUTHOR>
 * @version : OssHelper.java, v 0.1 2024/8/15 16:24 renxiao.wu Exp $
 */
@Slf4j
@Component
public class OssHelper {

    @Value("${aliyun.oss.bucket}")
    String bucketName;

    @Autowired
    private OssService ossService;

    @Autowired
    private FileDispatch fileDispatch;

    /**
     * 将图片宽高缩放到大边不超过800
     */
    public String resize800(String url) {
        //对于已经处理过或者非当前bucket的文件，直接返回，防止dev的image在prod环境图片出错
        if (StringUtils.isBlank(url) || StringUtils.contains(url, "x-oss-process=image") || !StringUtils.contains(url,
            bucketName)) {
            return url;
        }

        return ossService.reprocessImage(url, XOssProcessEnum.RESIZE_800);
    }

    /**
     * 将图片宽高缩放到大边不超过1024
     */
    public String resize1024(String url) {
        //对于已经处理过或者非当前bucket的文件，直接返回，防止dev的image在prod环境图片出错
        if (StringUtils.isBlank(url) || StringUtils.contains(url, "x-oss-process=image") || !StringUtils.contains(url,
            bucketName)) {
            return url;
        }

        return ossService.reprocessImage(url, XOssProcessEnum.RESIZE_1024);
    }

    /**
     * 预处理图片url
     *
     * @param url oss url
     * @return 处理后的图片url
     */
    public String reprocessImage(String url) {
        //对于已经处理过或者非当前bucket的文件，直接返回，防止dev的image在prod环境图片出错
        if (StringUtils.isBlank(url) || StringUtils.contains(url, "x-oss-process=image") || !StringUtils.contains(url,
            bucketName)) {
            return url;
        }

        return ossService.reprocessImage(url, XOssProcessEnum.RESIZE_AND_FORMAT);
    }

    /**
     * 将一组图片url列表打包为zip文件并上传到oss，返回zip文件的url
     *
     * @param imageUrls
     * @param rewriteFileName
     */
    public String createZipFromUrlsAndUpload(List<String> imageUrls, String fileName, boolean rewriteFileName) {
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(imageUrls), "imageUrls不能为空");

        try {
            // 1. 创建本地临时 ZIP 文件
            File zipFile = createZipFromImages(imageUrls, rewriteFileName);

            // 2. 上传 ZIP 文件到 OSS
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
            String zipFileName = sdf.format(new Date()) + "zips/" + StringUtils.defaultIfBlank(fileName,
                "images_" + System.currentTimeMillis()) + ".zip";  // 自定义 ZIP 文件名称
            String zipUrl = ossService.upload(zipFileName, Files.newInputStream(zipFile.toPath()));

            // 3. 删除本地临时 ZIP 文件
            zipFile.delete();

            return zipUrl;  // 返回 OSS 上的 ZIP 文件 URL

        } catch (Exception e) {
            log.error("createZipFromUrlsAndUpload异常", e);
            return null;
        }
    }

    /**
     * 将图片下载为BufferedImage
     *
     * @param imageOssUrl 图片的oss地址
     * @return 图片
     */
    public BufferedImage downloadToBufferedImage(String imageOssUrl) throws IOException {
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(imageOssUrl);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        return ImageIO.read(new File(tmpUrl));
    }

    /**
     * 将图片下载为字节流
     *
     * @param imageOssUrl 图片的oss地址
     * @return 字节流
     */
    public InputStream downloadToInputStream(String imageOssUrl) throws IOException {
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(imageOssUrl);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        return new ByteArrayInputStream(Files.readAllBytes(new File(tmpUrl).toPath()));
    }

    /**
     * 将图片下载并上传到comfyui
     *
     * @param imgOssUrl 图片的oss地址
     * @param path      文件目录
     * @param userId    用户id
     * @param serverUrl 服务地址
     * @return true，成功
     */
    public boolean downloadAndUploadToPath(String imgOssUrl, String path, Integer userId, String serverUrl,
                                           boolean orderly) {
        return downloadAndUploadToPath(Collections.singletonList(imgOssUrl), path, userId, serverUrl, orderly);
    }

    /**
     * 将图片列表下载并上传到comfyui
     *
     * @param imgOssUrls 图片的oss地址列表
     * @param path       文件目录
     * @param userId     用户id
     * @param serverUrl  服务地址
     * @return true，成功
     */
    public boolean downloadAndUploadToPath(List<String> imgOssUrls, String path, Integer userId, String serverUrl,
                                           boolean orderly) {
        long start = System.currentTimeMillis();
        try {
            for (int i = 0; i < imgOssUrls.size(); i++) {
                String url = imgOssUrls.get(i);

                String fileName = StringUtils.substringBeforeLast(StringUtils.substringAfterLast(url, "/"), "?");
                if (orderly) {
                    fileName = (i + 1) + "_" + fileName;
                }
                InputStream inputStream = downloadToInputStream(url);

                if (StringUtils.isNotBlank(serverUrl)) {
                    fileDispatch.uploadFile(path + "/" + fileName, inputStream, serverUrl, null);
                } else {
                    fileDispatch.uploadFile(path + "/" + fileName, inputStream, userId);
                }
            }
        } catch (Exception e) {
            log.error("下载并上传原始图片异常", e);
            return false;
        } finally {
            log.info("下载并上传原始图片耗时：{}ms", System.currentTimeMillis() - start);
        }
        return true;
    }

    /**
     * 将oss中的图片进行等比压缩，并重新上传oss
     *
     * @param imageOssUrl oss的图片地址
     * @param width       宽度
     * @param height      高度
     * @return 压缩后的oss地址
     * @throws IOException 异常
     */
    public String resizeAndUpload(String imageOssUrl, int width, int height) throws IOException {
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(imageOssUrl);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = null;
        try {
            tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        } catch (Exception e) {
            return null;
        }
        File imageFile = new File(tmpUrl);
        BufferedImage bufferedImage = ImageIO.read(imageFile);

        if (StringUtils.contains(imageOssUrl, ".png?")) {
            log.info("将png图片转换成jpg文件，url={}", imageOssUrl);
            bufferedImage = FileUtils.convertPngToJpg(bufferedImage);
        }

        //纠正方向
        bufferedImage = FileUtils.correctOrientation(bufferedImage, imageFile);

        int targetHeight = bufferedImage.getHeight() * width / bufferedImage.getWidth();
        BufferedImage resize = FileUtils.resize(bufferedImage, width, targetHeight);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        String format = "jpg";
        ImageIO.write(resize, format, baos);
        byte[] imageBytes = baos.toByteArray();

        return ossService.upload(originImageName + "." + format, new ByteArrayInputStream(imageBytes));
    }

    // 将 URL 对应的图片下载并打包为 ZIP 文件
    private File createZipFromImages(List<String> imageUrls, boolean rewriteFileName) throws IOException {
        // 创建一个临时的 ZIP 文件
        File zipFile = Files.createTempFile("images", ".zip").toFile();
        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(zipFile.toPath()))) {
            for (int i = 0; i < imageUrls.size(); i++) {
                String imageUrl = imageUrls.get(i);
                URL url = new URL(imageUrl);
                try (InputStream inputStream = url.openStream()) {
                    String fileExt = CommonUtil.getFileExtensionFromUrl(imageUrl);

                    String fileName = CommonUtil.getFileNameFromUrl(imageUrl);
                    if (rewriteFileName) {
                        fileName = (i + 1) + fileExt;
                    }

                    zipOut.putNextEntry(new ZipEntry(fileName));

                    // 将图片内容写入 ZIP 文件
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = inputStream.read(buffer)) > 0) {
                        zipOut.write(buffer, 0, len);
                    }
                    zipOut.closeEntry();
                }
            }
        }
        return zipFile;
    }
}
