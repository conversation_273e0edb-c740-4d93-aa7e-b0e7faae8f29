{"client_id": "${clientId}", "prompt": {"177": {"_meta": {"title": "Text String"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["283", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "216": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "232": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "235": {"_meta": {"title": "Inspyrenet Rembg"}, "class_type": "InspyrenetRembg", "inputs": {"image": ["236", 0], "torchscript_jit": "default"}, "disable": "${isPureBg?then('false','true')}"}, "236": {"_meta": {"title": "🔍 CR Upscale Image"}, "class_type": "CR Upscale Image", "inputs": {"image": ["584", 0], "mode": "rescale", "resampling_method": "lanc<PERSON>s", "rescale_factor": 2, "resize_width": "${height}", "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}, "disable": "${isPureBg?then('false','true')}"}, "248": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": "${pureRgb}", "height": ["261", 5], "width": ["261", 4]}, "disable": "${isPureBg?then('false','true')}"}, "258": {"_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}, "class_type": "ImageRGBA2RGB", "inputs": {"image": ["235", 0]}, "disable": "${isPureBg?then('false','true')}"}, "261": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["236", 0]}, "disable": "${isPureBg?then('false','true')}"}, "263": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["316", 0], "scale_by": ["266", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isPureBg?then('false','true')}"}, "266": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["316", 0], "target_size": ["367", 0]}, "disable": "${isPureBg?then('false','true')}"}, "268": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["283", 0]}}, "269": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "272": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "${baseModelDType!'default'}"}}, "273": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["277", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "${samplerName!'euler'}"}}, "275": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "${scheduleName!'beta'}", "steps": "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}"}}, "276": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "277": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["268", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(378,296)}", 0], "width": ["352", 0]}}, "282": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["${isPromptCorrect?then(552,282)}", 0]}}, "284": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"_meta": {"title": "服装lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${((isPureBg||!isAntiBlueLora)&&!isLoraFace)?then(0,1)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,377),298)}", 0], "strength_clip": 1, "strength_model": "${loraStrength}"}}, "297": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "298": {"_meta": {"title": "风格lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isLoraFace?then(297,271)}", "${isLoraFace?then(1,0)?number}"], "lora_name": "${sceneLora}", "model": ["${isLoraFace?then(297,377)}", 0], "strength_clip": 1, "strength_model": "${sceneLoraStrength}"}, "disable": "${(isPureBg||!isAntiBlueLora)?then('true','false')}"}, "316": {"_meta": {"title": "conrain image composite masked"}, "class_type": "ConrainImageCompositeMasked", "inputs": {"destination": ["248", 0], "mask": ["235", 1], "resize_source": false, "source": ["258", 0], "x": 0, "y": 0}, "disable": "${isPureBg?then('false','true')}"}, "320": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "331": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1335}}, "349": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,584)}", 0], "width": ["285", 0], "x": 0, "y": 0}}, "365": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["584", 0]}}, "367": {"_meta": {"title": "Integer Maximum"}, "class_type": "JWIntegerMax", "inputs": {"a": ["365", 4], "b": ["365", 5]}}, "373": {"_meta": {"title": "推理加速开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${speedUpSwitch?then(1,2)}"}}, "375": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}}, "377": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}}, "378": {"_meta": {"title": "合并PW和flux模型"}, "class_type": "ModelMergeFlux1", "inputs": {"double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "final_layer.": 1, "guidance_in": 1, "img_in.": 1, "model1": ["508", 0], "model2": ["379", 0], "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.2.": 1, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.3.": 1, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "time_in.": 1, "txt_in.": 1, "vector_in.": 1}, "disable": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then('false','true')}"}, "379": {"_meta": {"title": "PW模型"}, "class_type": "UNETLoader", "inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "disable": "${(isPWModel)?then('false','true')}"}, "446": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["447", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "447": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["615", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "462": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}}, "474": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["547", 0], "image1": ["475", 0]}}, "475": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "476": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "477": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["548", 0], "image1": ["476", 0]}}, "508": {"_meta": {"title": "Apply PuLID Flux"}, "class_type": "ApplyPulidFlux", "inputs": {"end_at": 1, "eva_clip": ["513", 0], "face_analysis": ["515", 0], "image": ["536", 0], "model": ["296", 0], "pulid_flux": ["512", 0], "start_at": 0, "weight": 0.8}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "512": {"_meta": {"title": "Load PuLID Flux Model"}, "class_type": "PulidFluxModelLoader", "inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "513": {"_meta": {"title": "Load <PERSON> (PuLID Flux)"}, "class_type": "PulidFluxEvaClipLoader", "inputs": {}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "515": {"_meta": {"title": "Load InsightFace (PuLID Flux)"}, "class_type": "PulidFluxInsightFaceLoader", "inputs": {"provider": "CUDA"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "525": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["462", 0], "mask_components": "1,3", "method": "selfie_multiclass_256x256"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "536": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["462", 0], "mask": ["525", 1], "padding_bottom": 96, "padding_left": 96, "padding_right": 96, "padding_top": 96}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "547": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"}}, "548": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"}}, "549": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": ": ", "text_a": ["550", 0], "text_b": ["282", 0]}, "disable": "${isPromptCorrect?then('false','true')}"}, "550": {"_meta": {"title": "🔤 CR Text"}, "class_type": "CR Text", "inputs": {"text": "Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"}, "disable": "${isPromptCorrect?then('false','true')}"}, "552": {"_meta": {"title": "llmodel"}, "class_type": "LLModel", "inputs": {"llm_model": "default", "prompt": ["549", 0]}, "disable": "${isPromptCorrect?then('false','true')}"}, "557": {"_meta": {"title": "Bounded Image Crop With Mask LR v3"}, "class_type": "BoundedImageCropWithMask_v3_LR", "inputs": {"expand_B": 20, "expand_LRU": 20, "image": ["269", 0], "mask": ["585", 4]}}, "561": {"_meta": {"title": "Repeat Latent Batch"}, "class_type": "RepeatLatentBatch", "inputs": {"amount": 1, "samples": ["578", 2]}}, "562": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["569", 0], "vae": ["270", 0]}}, "563": {"_meta": {"title": "Mask Upscale LR"}, "class_type": "MaskUpscale_LR", "inputs": {"mask": ["565", 0], "scale_by": ["564", 0]}}, "564": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["557", 0], "target_size": 1024}}, "565": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "class_type": "ConrainGrowMaskWithBlur", "inputs": {"blur_radius": 15, "decay_factor": 1, "expand": -10, "fill_holes": false, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 1, "mask": ["557", 2], "tapered_corners": true}}, "567": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["557", 0], "scale_by": ["564", 0], "upscale_method": "lanc<PERSON>s"}}, "568": {"_meta": {"title": "Sapiens<PERSON><PERSON>der"}, "class_type": "Sapiens<PERSON><PERSON>der", "inputs": {"convert_torchscript_to_bf16": true, "depth_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "normal_ckpt": "none", "pose_ckpt": "none", "remove_background": true, "seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "seg_pellete": true, "show_pose_object": false, "use_yolo": false}}, "569": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["580", 0], "latent_image": ["561", 0], "noise": ["579", 0], "sampler": ["581", 0], "sigmas": ["577", 0]}}, "570": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["271", 0], "text": ["331", 0]}}, "571": {"_meta": {"title": "Load LoRA"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "child_face_lora_20250607.safetensors", "model": ["377", 0], "strength_clip": 1, "strength_model": 1}, "disable": "${is<PERSON>hild?then('false','true')}"}, "572": {"_meta": {"title": "Load LoRA"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isChild?then(571,271)}", "${isChild?then(1,0)?number}"], "lora_name": "${FACE.extInfo.faceLora}", "model": ["${isChild?then(571,377)}", 0], "strength_clip": 1, "strength_model": 1}}, "574": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["570", 0], "guidance": 2}}, "575": {"_meta": {"title": "ConditioningZeroOut"}, "class_type": "ConditioningZeroOut", "inputs": {"conditioning": ["570", 0]}}, "576": {"_meta": {"title": "Differential Diffusion"}, "class_type": "DifferentialDiffusion", "inputs": {"model": ["572", 0]}}, "577": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 0.15, "model": ["572", 0], "scheduler": "sgm_uniform", "steps": 8}}, "578": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["607", 0], "negative": ["575", 0], "noise_mask": false, "pixels": ["598", 0], "positive": ["574", 0], "vae": ["270", 0]}}, "579": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": 0}}, "580": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["578", 0], "model": ["576", 0]}}, "581": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "dpm_2"}}, "584": {"_meta": {"title": "Bounded Image Blend with Mask"}, "class_type": "Bounded Image Blend with Mask", "inputs": {"blend_factor": 1, "feathering": 0, "source": ["597", 0], "target": ["269", 0], "target_bounds": ["557", 1], "target_mask": ["565", 0]}}, "585": {"_meta": {"title": "SapiensSampler"}, "class_type": "SapiensSampler", "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "add_seg_index": "3,23,24,25,26,27", "image": ["269", 0], "model": ["568", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>"}}, "588": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}}, "589": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["567", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "590": {"_meta": {"title": "Ordered Face Filter"}, "class_type": "OrderedFaceFilter", "inputs": {"criteria": "area", "faces": ["589", 0], "order": "descending", "take_count": 1, "take_start": 0}}, "591": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["590", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "592": {"_meta": {"title": "SapiensSampler"}, "class_type": "SapiensSampler", "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "add_seg_index": "23,24,25,26,27", "image": ["591", 0], "model": ["568", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>"}}, "593": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}}, "594": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["588", 1], "text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"}}, "595": {"_meta": {"title": "Load InstantID Model"}, "class_type": "InstantIDModelLoader", "inputs": {"instantid_file": "ip-adapter.bin"}}, "596": {"_meta": {"title": "InstantID Face Analysis"}, "class_type": "InstantIDFaceAnalysis", "inputs": {"provider": "CUDA"}}, "597": {"_meta": {"title": "<PERSON>p Faces Back"}, "class_type": "WarpFacesBack", "inputs": {"crop": ["601", 0], "face": ["590", 0], "images": ["567", 0], "mask": ["602", 0], "warp": ["591", 2]}}, "598": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["599", 0], "vae": ["588", 2]}}, "599": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.5, "latent_image": ["600", 2], "model": ["606", 0], "negative": ["600", 1], "positive": ["600", 0], "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "seed": 42029499340256, "steps": 4}}, "600": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["607", 0], "negative": ["606", 2], "noise_mask": true, "pixels": ["591", 0], "positive": ["606", 1], "vae": ["588", 2]}}, "601": {"_meta": {"title": "Image Color Match"}, "class_type": "easy imageColorMatch", "inputs": {"image_output": "<PERSON>de", "image_ref": ["591", 0], "image_target": ["562", 0], "method": "adain", "save_prefix": "ComfyUI"}}, "602": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "class_type": "ConrainGrowMaskWithBlur", "inputs": {"blur_radius": 0, "decay_factor": 1, "expand": 40, "fill_holes": false, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 1, "mask": ["607", 0], "tapered_corners": false}}, "604": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["588", 1], "text": ["331", 0]}}, "605": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["591", 0], "mask_components": "13", "method": "human_parsing_lip"}}, "606": {"_meta": {"title": "Apply InstantID"}, "class_type": "ApplyInstantID", "inputs": {"control_net": ["593", 0], "end_at": 1, "image": ["446", 0], "image_kps": ["591", 0], "insightface": ["596", 0], "instantid": ["595", 0], "model": ["588", 0], "negative": ["594", 0], "positive": ["604", 0], "start_at": 0, "weight": 1}}, "607": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 4, "enabled": true, "grow": 5, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["605", 1]}}, "608": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "upscale if smaller", "height": 2048, "image": ["629", 0], "interpolation": "lanc<PERSON>s", "method": "keep proportion", "multiple_of": 0, "width": 2048}}, "609": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}}, "610": {"_meta": {"title": "Simple Detector (SEGS)"}, "class_type": "ImpactSimpleDetectorSEGS", "inputs": {"bbox_detector": ["609", 0], "bbox_dilation": 0, "bbox_threshold": 0.5, "crop_factor": 3, "drop_size": 10, "image": ["608", 0], "post_dilation": 0, "sam_mask_hint_threshold": 0.7, "sub_bbox_expansion": 0, "sub_dilation": 0, "sub_threshold": 0.5}}, "611": {"_meta": {"title": "SEGS Filter (ordered)"}, "class_type": "ImpactSEGSOrderedFilter", "inputs": {"order": true, "segs": ["610", 0], "take_count": 1, "take_start": 0, "target": "area(=w*h)"}}, "612": {"_meta": {"title": "SEGS to MASK (combined)"}, "class_type": "SegsToCombinedMask", "inputs": {"segs": ["611", 0]}}, "613": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": 256, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["612", 0]}}, "614": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["608", 0], "mask": ["613", 0], "padding_bottom": 64, "padding_left": 64, "padding_right": 64, "padding_top": 64}}, "615": {"_meta": {"title": "Image List To Image Batch"}, "class_type": "easy imageListToImageBatch", "inputs": {"images": ["614", 0]}}, "629": {"_meta": {"title": "Make Image List"}, "class_type": "ImpactMakeImageList", "inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [3362.6934963332183, 2771.7170066656527], "scale": 0.15772225476662663}}, "groups": [{"id": 3, "bounding": [7445.9052734375, -1685.104736328125, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 5, "bounding": [641.1348876953125, -1689.5174560546875, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 6, "bounding": [7444.98828125, -1004.390625, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 7, "bounding": [-1031.672119140625, -1730.5921630859375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "模型加载"}, {"id": 10, "bounding": [3084.989501953125, -1694.2711181640625, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "人脸原图"}, {"id": 12, "bounding": [3090, -410, 1893.4822998046875, 743.8132934570312], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 13, "bounding": [5220.31640625, -394.3552551269531, 2118.918701171875, 688.3203735351562], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 14, "bounding": [5214.2060546875, -1708.161865234375, 1880.18798828125, 1196.9237060546875], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}], "last_link_id": 1141, "last_node_id": 635, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [760, 447, 0, 446, 0, "FACE"], [813, 475, 0, 474, 0, "IMAGE"], [815, 476, 0, 477, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [978, 547, 0, 474, 2, "INT"], [979, 548, 0, 477, 2, "INT"], [980, 550, 0, 549, 0, "STRING"], [981, 282, 0, 549, 1, "STRING"], [982, 549, 0, 552, 2, "STRING"], [983, 552, 0, 553, 0, "*"], [990, 283, 0, 287, 0, "*"], [1007, 566, 0, 557, 0, "IMAGE"], [1008, 585, 4, 557, 1, "MASK"], [1009, 565, 0, 558, 0, "*"], [1010, 566, 0, 559, 0, "*"], [1011, 557, 1, 560, 0, "*"], [1012, 578, 2, 561, 0, "LATENT"], [1013, 569, 0, 562, 0, "LATENT"], [1014, 586, 0, 562, 1, "VAE"], [1015, 565, 0, 563, 0, "MASK"], [1016, 564, 0, 563, 1, "FLOAT"], [1017, 557, 0, 564, 0, "IMAGE"], [1018, 557, 2, 565, 0, "MASK"], [1019, 557, 0, 567, 0, "IMAGE"], [1020, 564, 0, 567, 1, "FLOAT"], [1021, 579, 0, 569, 0, "NOISE"], [1022, 580, 0, 569, 1, "GUIDER"], [1023, 581, 0, 569, 2, "SAMPLER"], [1024, 577, 0, 569, 3, "SIGMAS"], [1025, 561, 0, 569, 4, "LATENT"], [1026, 583, 0, 570, 0, "CLIP"], [1027, 583, 0, 571, 1, "CLIP"], [1028, 571, 0, 572, 0, "MODEL"], [1029, 571, 1, 572, 1, "CLIP"], [1030, 572, 0, 573, 0, "*"], [1031, 570, 0, 574, 0, "CONDITIONING"], [1032, 570, 0, 575, 0, "CONDITIONING"], [1033, 573, 0, 576, 0, "MODEL"], [1034, 573, 0, 577, 0, "MODEL"], [1035, 574, 0, 578, 0, "CONDITIONING"], [1036, 575, 0, 578, 1, "CONDITIONING"], [1037, 586, 0, 578, 2, "VAE"], [1039, 576, 0, 580, 0, "MODEL"], [1040, 578, 0, 580, 1, "CONDITIONING"], [1041, 567, 0, 582, 0, "*"], [1042, 559, 0, 584, 0, "IMAGE"], [1043, 558, 0, 584, 1, "MASK"], [1044, 560, 0, 584, 2, "IMAGE_BOUNDS"], [1046, 568, 0, 585, 0, "MODEL_SAPIEN"], [1047, 566, 0, 585, 1, "IMAGE"], [1048, 587, 0, 589, 0, "IMAGE"], [1049, 589, 0, 590, 0, "FACE"], [1050, 590, 0, 591, 0, "FACE"], [1051, 591, 0, 592, 1, "IMAGE"], [1052, 588, 1, 594, 0, "CLIP"], [1053, 587, 0, 597, 0, "IMAGE"], [1054, 590, 0, 597, 1, "FACE"], [1055, 601, 0, 597, 2, "IMAGE"], [1056, 602, 0, 597, 3, "MASK"], [1057, 591, 2, 597, 4, "WARP"], [1058, 599, 0, 598, 0, "LATENT"], [1059, 588, 2, 598, 1, "VAE"], [1060, 606, 0, 599, 0, "MODEL"], [1061, 600, 0, 599, 1, "CONDITIONING"], [1062, 600, 1, 599, 2, "CONDITIONING"], [1063, 600, 2, 599, 3, "LATENT"], [1064, 606, 1, 600, 0, "CONDITIONING"], [1065, 606, 2, 600, 1, "CONDITIONING"], [1066, 588, 2, 600, 2, "VAE"], [1067, 603, 0, 600, 3, "IMAGE"], [1068, 607, 0, 600, 4, "MASK"], [1069, 591, 0, 601, 0, "IMAGE"], [1071, 607, 0, 602, 0, "MASK"], [1072, 591, 0, 603, 0, "*"], [1073, 588, 1, 604, 0, "CLIP"], [1074, 591, 0, 605, 0, "IMAGE"], [1075, 595, 0, 606, 0, "INSTANTID"], [1076, 596, 0, 606, 1, "FACEANALYSIS"], [1077, 593, 0, 606, 2, "CONTROL_NET"], [1078, 588, 0, 606, 4, "MODEL"], [1079, 604, 0, 606, 5, "CONDITIONING"], [1080, 594, 0, 606, 6, "CONDITIONING"], [1081, 603, 0, 606, 7, "IMAGE"], [1082, 605, 1, 607, 0, "MASK"], [1087, 609, 0, 610, 0, "BBOX_DETECTOR"], [1088, 611, 0, 612, 0, "SEGS"], [1089, 612, 0, 613, 0, "MASK"], [1090, 613, 0, 614, 1, "MASK"], [1091, 614, 0, 615, 0, "IMAGE"], [1092, 610, 0, 611, 0, "SEGS"], [1093, 608, 0, 614, 0, "IMAGE"], [1094, 615, 0, 447, 0, "IMAGE"], [1095, 608, 0, 610, 1, "IMAGE"], [1097, 446, 0, 616, 0, "*"], [1098, 616, 0, 606, 3, "IMAGE"], [1099, 331, 0, 617, 0, "*"], [1100, 617, 0, 618, 0, "*"], [1102, 618, 0, 619, 0, "*"], [1103, 619, 0, 604, 1, "STRING"], [1104, 618, 0, 570, 1, "STRING"], [1111, 269, 0, 566, 0, "*"], [1112, 582, 0, 587, 0, "*"], [1113, 386, 0, 624, 0, "*"], [1114, 624, 0, 625, 0, "*"], [1115, 625, 0, 583, 0, "*"], [1117, 568, 0, 626, 0, "*"], [1118, 626, 0, 592, 0, "MODEL_SAPIEN"], [1120, 584, 0, 627, 0, "*"], [1121, 627, 0, 236, 0, "IMAGE"], [1122, 627, 0, 365, 0, "IMAGE"], [1123, 282, 0, 283, 0, "STRING"], [1125, 462, 0, 629, 0, "IMAGE"], [1126, 474, 0, 629, 1, "IMAGE"], [1127, 477, 0, 629, 2, "IMAGE"], [1128, 629, 0, 608, 0, "IMAGE"], [1129, 270, 0, 586, 0, "*"], [1130, 377, 0, 571, 0, "MODEL"], [1135, 598, 0, 578, 3, "IMAGE"], [1136, 607, 0, 578, 4, "MASK"], [1137, 562, 0, 601, 1, "IMAGE"], [1138, 597, 0, 584, 3, "IMAGE"]], "nodes": [{"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [392, 394], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [386, 539], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [8034.98828125, -744.3985595703125], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 62, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [9164.98046875, -594.3985595703125], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7664.98828125, -534.3985595703125], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6898.775390625, -619.42529296875], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 216, "flags": {}, "inputs": [{"label": "images", "link": 856, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 392, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 386, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 138, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [9724.98046875, -874.3985595703125], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 34, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [8514.9853515625, -774.3985595703125], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [8024.98828125, -874.3985595703125], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 135, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [8015.9052734375, -1265.104736328125], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1121, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 133, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [7538.634765625, -1321.619873046875], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [], "pos": [8445.90234375, -1585.104736328125], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {"collapsed": false}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 139, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8475.9033203125, -1405.104736328125], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 137, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8465.9033203125, -1185.104736328125], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 140, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [8025.9052734375, -1495.104736328125], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 141, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [9785.8984375, -1515.104736328125], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}, {"link": 615, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": "${isPureBg?then(0,4)}", "order": 142, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [9475.8984375, -1395.104736328125], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 268, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 513, "name": "clip", "type": "CLIP"}, {"link": 453, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 82, "outputs": [{"links": [464], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1393.572998046875, -1348.140869140625], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 54], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 97, "outputs": [{"links": [611, 1111, 951], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2622.702880859375, -1422.1380615234375], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"links": [455, 520, 1129], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [2395.71435546875, -1196.9207763671875], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"links": [622, 664, 664], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-961.8040771484375, -912.8571166992188], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [621, 647, 647, 944], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-949.96533203125, -1104.5126953125], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "${baseModelDType!'default'}"]}, {"id": 273, "flags": {}, "inputs": [{"link": 456, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 95, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [2432.************, -1611.155517578125], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [292.29705810546875, 127.71533966064453], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [2010.49560546875, -1139.160888671875], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["${samplerName!'euler'}"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 92, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [2031.17724609375, -971.8941040039062], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["${scheduleName!'beta'}", "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}", 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 93, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [1994.201171875, -1380.5081787109375], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 277, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [456], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [2059.2626953125, -1607.************], "properties": {"Node name for S&R": "RandomNoise"}, "size": [317.5343933105469, 84.33126831054688], "type": "RandomNoise", "widgets_values": ["${seed}", "fixed"]}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 464, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 86, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [1756.1866455078125, -1359.78466796875], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 71, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [1718.************, -1133.7596435546875], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 651, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 90, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [1701.7969970703125, -884.855712890625], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [981, 473, 1123], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [789.1341552734375, -968.331787109375], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [400, 200], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "fixed"]}, {"id": 283, "flags": {}, "inputs": [{"link": 1123, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 38, "outputs": [{"links": [453, 990], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [1400.793701171875, -1160.6016845703125], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 226], "type": "ShowText|pysssss", "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [587, 589], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1345.7989501953125, -503.8546447753906], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [590, 591], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1331.7989501953125, -689.8560791015625], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [798.8578491210938, -683.801025390625], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [411.6590881347656, 124], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 990, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 53, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2535.691650390625, -838.6723022460938], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 41, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2525.9609375, -664.3173217773438], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 76, "outputs": [{"links": [514, 654, 947], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [513, 510, 513, 630, 631], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [740.1643676757812, -1196.1575927734375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [499.25970458984375, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", "${loraStrength}", 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 946, "name": "model", "type": "MODEL"}, {"link": 665, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 61, "outputs": [{"links": [627], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [628, 625], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [726.767822265625, -1590.7061767578125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [477.3377990722656, 128.31455993652344], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 627, "name": "model", "type": "MODEL"}, {"link": 628, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 69, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [724.9411010742188, -1389.706298828125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [491.7470703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 143, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8895.8994140625, -1315.104736328125], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 70, "outputs": [], "pos": [9734.98046875, -524.3985595703125], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"links": [575, 575, 1099], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2460.299072265625, -1021.8446655273438], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [319.1407165527344, 134.37188720703125], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1335, "fixed"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 40, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1679.1009521484375, -613.5169067382812], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 39, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1702.960693359375, -512.515625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 55, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1911.972900390625, -635.5166625976562], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 64, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2161.5029296875, -639.5166625976562], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 54, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1982.985107421875, -505.5156555175781], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 63, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2215.380859375, -484.5157165527344], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 144, "outputs": [{"links": [856], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [9204.98046875, -904.3985595703125], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 365, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1122, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 134, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [613], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [614], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [8905.8994140625, -1555.104736328125], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 367, "flags": {}, "inputs": [{"link": 613, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 614, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 136, "outputs": [{"links": [615], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [9205.8984375, -1475.104736328125], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 373, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"links": [644, 644], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-205.92677307128906, -1002.7108764648438], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "推理加速开关", "type": "JWStringToInteger", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 375, "flags": {}, "inputs": [{"link": 647, "name": "model", "type": "MODEL"}], "mode": 0, "order": 37, "outputs": [{"links": [642], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-483.5779724121094, -705.3702392578125], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "flags": {"collapsed": false}, "inputs": [{"link": 642, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 944, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 644, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 52, "outputs": [{"links": [946, 1130], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [83.21484375, -725.4107666015625], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [257.191650390625, 78.78076171875], "type": "CR Model Input Switch", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 378, "flags": {"collapsed": true}, "inputs": [{"link": 948, "name": "model1", "type": "MODEL"}, {"link": 663, "name": "model2", "type": "MODEL"}], "mode": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(0,4)}", "order": 85, "outputs": [{"links": [651], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [209.58755493164062, -1070.8203125], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isPWModel)?then(0,4)}", "order": 16, "outputs": [{"links": [662, 662], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-951.9925537109375, -1307.653076171875], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "title": "PW模型", "type": "UNETLoader", "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"]}, {"id": 385, "flags": {}, "inputs": [{"link": 662, "name": "", "type": "*"}], "mode": 0, "order": 43, "outputs": [{"links": [663], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-374.7147216796875, -1127.6103515625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 386, "flags": {}, "inputs": [{"link": 664, "name": "", "type": "*"}], "mode": 0, "order": 36, "outputs": [{"links": [665, 1113], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [415.603759765625, -950.938232421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 446, "flags": {}, "inputs": [{"link": 760, "name": "faces", "type": "FACE"}], "mode": 0, "order": 98, "outputs": [{"links": [762, 1097], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [4748.59521484375, -1052.1627197265625], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "flags": {}, "inputs": [{"link": 1094, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 96, "outputs": [{"links": [760], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [4506.21728515625, -1050.9080810546875], "properties": {"Node name for S&R": "DetectFaces"}, "size": [216.65777587890625, 143.53131103515625], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 462, "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [899, 957, 1125, 957], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [3189.112548828125, -1583.8983154296875], "properties": {"Node name for S&R": "LoadImage"}, "size": [235.8109893798828, 314], "type": "LoadImage", "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 474, "flags": {}, "inputs": [{"link": 813, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 978, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 48, "outputs": [{"links": [1126], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [3491.64697265625, -1152.820556640625], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 475, "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [{"links": [813], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [3491.127197265625, -1589.1981201171875], "properties": {"Node name for S&R": "LoadImage"}, "size": [249.60922241210938, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 476, "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [{"links": [815], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [3806.9091796875, -1597.4578857421875], "properties": {"Node name for S&R": "LoadImage"}, "size": [234.48504638671875, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 477, "flags": {}, "inputs": [{"link": 815, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 979, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 49, "outputs": [{"links": [1127], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [3484.615966796875, -1002.4156494140625], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 508, "flags": {}, "inputs": [{"link": 947, "name": "model", "type": "MODEL"}, {"link": 879, "name": "pulid_flux", "type": "PULIDFLUX"}, {"link": 882, "name": "eva_clip", "type": "EVA_CLIP"}, {"link": 883, "name": "face_analysis", "type": "FACEANALYSIS"}, {"link": 966, "name": "image", "type": "IMAGE"}, {"name": "attn_mask", "shape": 7, "type": "MASK"}, {"name": "options", "shape": 7, "type": "OPTIONS"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 81, "outputs": [{"links": [948], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [183.51483154296875, -1463.5994873046875], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "size": [315, 226], "type": "ApplyPulidFlux", "widgets_values": [0.8, 0, 1]}, {"id": 512, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 30, "outputs": [{"links": [879], "name": "PULIDFLUX", "type": "PULIDFLUX"}], "pos": [-466.6873779296875, -1493.7525634765625], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "size": [315, 58], "type": "PulidFluxModelLoader", "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "flags": {"collapsed": false}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 31, "outputs": [{"links": [882], "name": "EVA_CLIP", "type": "EVA_CLIP"}], "pos": [-468.8070068359375, -1372.1922607421875], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "size": [327.5999755859375, 26], "type": "PulidFluxEvaClipLoader", "widgets_values": []}, {"id": 515, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 32, "outputs": [{"links": [883], "name": "FACEANALYSIS", "type": "FACEANALYSIS"}], "pos": [-479.9522705078125, -1278.173095703125], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "size": [365.4000244140625, 58], "type": "PulidFluxInsightFaceLoader", "widgets_values": ["CUDA"]}, {"id": 525, "flags": {}, "inputs": [{"link": 899, "name": "image", "type": "IMAGE"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 46, "outputs": [{"name": "image", "type": "IMAGE"}, {"links": [960], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "type": "BBOX"}], "pos": [-841.5623779296875, -1652.26416015625], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "size": [300, 260], "type": "easy humanSegmentation", "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 536, "flags": {}, "inputs": [{"link": 959, "name": "image", "type": "IMAGE"}, {"link": 960, "name": "mask", "type": "MASK"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 57, "outputs": [{"links": [966], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [-54.86212158203125, -1627.6336669921875], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [236.27499389648438, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [96, 96, 96, 96]}, {"id": 537, "flags": {}, "inputs": [{"link": 957, "name": "", "type": "*"}], "mode": 0, "order": 47, "outputs": [{"links": [959], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-288.80596923828125, -1664.228271484375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 547, "flags": {}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"links": [978], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3143.4921875, -1143.5277099609375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "type": "JWStringToInteger", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"]}, {"id": 548, "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [{"links": [979], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3122.81103515625, -989.7696533203125], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [213.92233276367188, 58], "type": "JWStringToInteger", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"]}, {"id": 549, "flags": {}, "inputs": [{"link": 980, "name": "text_a", "shape": 7, "type": "STRING", "widget": {"name": "text_a"}}, {"link": 981, "name": "text_b", "shape": 7, "type": "STRING", "widget": {"name": "text_b"}}, {"name": "text_c", "shape": 7, "type": "STRING", "widget": {"name": "text_c"}}, {"name": "text_d", "shape": 7, "type": "STRING", "widget": {"name": "text_d"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 50, "outputs": [{"links": [982], "name": "STRING", "slot_index": 0, "type": "STRING"}], "pos": [1418.9630126953125, -1600.443359375], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [213.69569396972656, 179.2432861328125], "type": "Text Concatenate", "widgets_values": [": ", "true", "", "", "", ""]}, {"id": 550, "flags": {}, "inputs": [], "mode": "${isPromptCorrect?then('0','4')}", "order": 33, "outputs": [{"links": [980], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [777.7445678710938, -1800.5491943359375], "properties": {"Node name for S&R": "CR Text"}, "size": [415.5107116699219, 126.7047348022461], "type": "CR Text", "widgets_values": ["Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"]}, {"id": 552, "flags": {}, "inputs": [{"name": "image_list", "shape": 7, "type": "IMAGE"}, {"name": "ref_image", "shape": 7, "type": "IMAGE"}, {"link": 982, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 59, "outputs": [{"links": [983], "name": "result_text", "slot_index": 0, "type": "STRING"}, {"name": "result_detail", "type": "STRING"}], "pos": [1760.8935546875, -1600.6900634765625], "properties": {"Node name for S&R": "LLModel"}, "size": [254.6305389404297, 154.52664184570312], "type": "LLModel", "widgets_values": ["你能干嘛", "default"]}, {"id": 553, "flags": {}, "inputs": [{"link": 983, "name": "", "type": "*"}], "mode": "${isPromptCorrect?then('0','4')}", "order": 67, "outputs": [{"links": [], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [1281.9537353515625, -1206.7755126953125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 557, "flags": {}, "inputs": [{"link": 1007, "name": "image", "type": "IMAGE"}, {"link": 1008, "name": "mask", "type": "MASK"}], "mode": 0, "order": 103, "outputs": [{"links": [1017, 1019], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [1011], "name": "IMAGE_BOUNDS", "shape": 3, "slot_index": 1, "type": "IMAGE_BOUNDS"}, {"links": [1018], "name": "MASK", "shape": 3, "slot_index": 2, "type": "MASK"}, {"links": [], "name": "SCALE_BY", "shape": 3, "slot_index": 3, "type": "FLOAT"}], "pos": [3830, -240], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "size": [285.6000061035156, 149.66432189941406], "type": "BoundedImageCropWithMask_v3_LR", "widgets_values": [20, 20]}, {"id": 558, "flags": {}, "inputs": [{"link": 1009, "name": "", "type": "*"}], "mode": 0, "order": 108, "outputs": [{"links": [1043], "name": "", "type": "MASK"}], "pos": [6640.31640625, -264.3552551269531], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 559, "flags": {}, "inputs": [{"link": 1010, "name": "", "type": "*"}], "mode": 0, "order": 101, "outputs": [{"links": [1042], "name": "", "type": "IMAGE"}], "pos": [6620.31640625, -344.3552551269531], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 560, "flags": {}, "inputs": [{"link": 1011, "name": "", "type": "*"}], "mode": 0, "order": 105, "outputs": [{"links": [1044], "name": "", "type": "IMAGE_BOUNDS"}], "pos": [6640.31640625, -194.35525512695312], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 561, "flags": {}, "inputs": [{"link": 1012, "name": "samples", "type": "LATENT"}], "mode": 0, "order": 126, "outputs": [{"links": [1025], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [6260.31640625, 145.64474487304688], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "size": [210, 80.70018768310547], "type": "RepeatLatentBatch", "widgets_values": [1]}, {"id": 562, "flags": {"collapsed": false}, "inputs": [{"link": 1013, "name": "samples", "type": "LATENT"}, {"link": 1014, "name": "vae", "type": "VAE"}], "mode": 0, "order": 128, "outputs": [{"links": [1137], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6560.31640625, 145.64474487304688], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 563, "flags": {}, "inputs": [{"label": "mask", "link": 1015, "name": "mask", "type": "MASK"}, {"label": "scale_by", "link": 1016, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 109, "outputs": [{"label": "MASK", "links": [], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4710, -130], "properties": {"Node name for S&R": "MaskUpscale_LR"}, "size": [248.52598571777344, 73.40138244628906], "type": "MaskUpscale_LR", "widgets_values": [4]}, {"id": 564, "flags": {}, "inputs": [{"link": 1017, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 104, "outputs": [{"links": [1016, 1020], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [4180, 20], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [210, 136.1963348388672], "type": "UpscaleSizeCalculator", "widgets_values": [1024]}, {"id": 565, "flags": {"collapsed": false}, "inputs": [{"link": 1018, "name": "mask", "type": "MASK"}], "mode": 0, "order": 106, "outputs": [{"links": [1009, 1015], "name": "mask", "shape": 3, "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "shape": 3, "type": "MASK"}], "pos": [4380, -270], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "size": [236.14947509765625, 246], "type": "ConrainGrowMaskWithBlur", "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 566, "flags": {}, "inputs": [{"link": 1111, "name": "", "type": "*"}], "mode": 0, "order": 99, "outputs": [{"links": [1007, 1010, 1047], "name": "", "type": "IMAGE"}], "pos": [3210, -310], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 567, "flags": {}, "inputs": [{"label": "image", "link": 1019, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 1020, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 107, "outputs": [{"label": "IMAGE", "links": [1041], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [4540, 120], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [250.58731079101562, 109.74829864501953], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 568, "flags": {}, "inputs": [], "mode": 0, "order": 19, "outputs": [{"links": [1046, 1117], "name": "model", "slot_index": 0, "type": "MODEL_SAPIEN"}], "pos": [3110, -130], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "size": [283.3903503417969, 298], "type": "Sapiens<PERSON><PERSON>der", "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 569, "flags": {}, "inputs": [{"link": 1021, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 1022, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 1023, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 1024, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 1025, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 127, "outputs": [{"links": [1013], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [6570.31640625, -94.35526275634766], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [236.8000030517578, 109.************], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 570, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 1026, "name": "clip", "type": "CLIP"}, {"link": 1104, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 74, "outputs": [{"links": [1031, 1032], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [5280.31640625, 185.64474487304688], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [296.32208251953125, 77.2895278930664], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 571, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1130, "name": "model", "type": "MODEL"}, {"link": 1027, "name": "clip", "type": "CLIP"}], "mode": "${isChild?then(0,4)}", "order": 75, "outputs": [{"links": [1028], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [1029], "name": "CLIP", "slot_index": 1, "type": "CLIP"}], "pos": [5280.31640625, -4.355260848999023], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [324.723388671875, 134.23927307128906], "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["child_face_lora_20250607.safetensors", 1, 1]}, {"id": 572, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1028, "name": "model", "type": "MODEL"}, {"link": 1029, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 80, "outputs": [{"links": [1030], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "CLIP", "type": "CLIP"}], "pos": [5280.31640625, -214.35525512695312], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [315, 126], "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", 1, 1]}, {"id": 573, "flags": {}, "inputs": [{"link": 1030, "name": "", "type": "*"}], "mode": 0, "order": 84, "outputs": [{"links": [1033, 1034], "name": "", "type": "MODEL"}], "pos": [5610.31640625, -324.3552551269531], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 574, "flags": {}, "inputs": [{"link": 1031, "name": "conditioning", "slot_index": 0, "type": "CONDITIONING"}], "mode": 0, "order": 78, "outputs": [{"links": [1035], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [5700.31640625, 165.64474487304688], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": [2]}, {"id": 575, "flags": {}, "inputs": [{"link": 1032, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 79, "outputs": [{"links": [1036], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [5700.31640625, 25.644739151000977], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "size": [222.26809692382812, 26], "type": "ConditioningZeroOut", "widgets_values": []}, {"id": 576, "flags": {}, "inputs": [{"link": 1033, "name": "model", "type": "MODEL"}], "mode": 0, "order": 88, "outputs": [{"links": [1039], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [5700.31640625, -104.35526275634766], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "size": [210, 26], "type": "DifferentialDiffusion", "widgets_values": []}, {"id": 577, "flags": {}, "inputs": [{"link": 1034, "name": "model", "type": "MODEL"}], "mode": 0, "order": 89, "outputs": [{"links": [1024], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [5990.31640625, 105.64473724365234], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["sgm_uniform", 8, 0.15]}, {"id": 578, "flags": {}, "inputs": [{"link": 1035, "name": "positive", "type": "CONDITIONING"}, {"link": 1036, "name": "negative", "slot_index": 1, "type": "CONDITIONING"}, {"link": 1037, "name": "vae", "type": "VAE"}, {"link": 1135, "name": "pixels", "type": "IMAGE"}, {"link": 1136, "name": "mask", "slot_index": 4, "type": "MASK"}], "mode": 0, "order": 124, "outputs": [{"links": [1040], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "shape": 3, "type": "CONDITIONING"}, {"links": [1012], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [5976.31982421875, -298.1543273925781], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [216.59999084472656, 138], "type": "InpaintModelConditioning", "widgets_values": [false]}, {"id": 579, "flags": {}, "inputs": [], "mode": 0, "order": 20, "outputs": [{"links": [1021], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [6250.31640625, -54.355255126953125], "properties": {"Node name for S&R": "RandomNoise"}, "size": [221.2050323486328, 83.35130310058594], "type": "RandomNoise", "widgets_values": [0, "fixed"]}, {"id": 580, "flags": {}, "inputs": [{"link": 1039, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 1040, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 125, "outputs": [{"links": [1022], "name": "GUIDER", "shape": 3, "type": "GUIDER"}], "pos": [6270.31640625, -294.3552551269531], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 581, "flags": {}, "inputs": [], "mode": 0, "order": 21, "outputs": [{"links": [1023], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [6240.31640625, -174.35525512695312], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["dpm_2"]}, {"id": 582, "flags": {}, "inputs": [{"link": 1041, "name": "", "type": "*"}], "mode": 0, "order": 110, "outputs": [{"links": [1112], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4850, 50], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 583, "flags": {}, "inputs": [{"link": 1115, "name": "", "type": "*"}], "mode": 0, "order": 68, "outputs": [{"links": [1026, 1027], "name": "", "type": "CLIP"}], "pos": [4890, 263.97613525390625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 584, "flags": {}, "inputs": [{"link": 1042, "name": "target", "type": "IMAGE"}, {"link": 1043, "name": "target_mask", "type": "MASK"}, {"link": 1044, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 1138, "name": "source", "type": "IMAGE"}], "mode": 0, "order": 131, "outputs": [{"links": [1120], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7059.74609375, -324.9255065917969], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "size": [243.60000610351562, 172.84213256835938], "type": "Bounded Image Blend with Mask", "widgets_values": [1, 0]}, {"id": 585, "flags": {}, "inputs": [{"link": 1046, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 1047, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 102, "outputs": [{"links": [], "name": "seg_img", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "depth_img", "slot_index": 1, "type": "IMAGE"}, {"links": [], "name": "normal_img", "slot_index": 2, "type": "IMAGE"}, {"links": [], "name": "pose_img", "slot_index": 3, "type": "IMAGE"}, {"links": [1008], "name": "mask", "slot_index": 4, "type": "MASK"}], "pos": [3500, -130], "properties": {"Node name for S&R": "SapiensSampler"}, "size": [247.4663848876953, 259.6097412109375], "type": "SapiensSampler", "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 586, "flags": {}, "inputs": [{"link": 1129, "name": "", "type": "*"}], "mode": 0, "order": 35, "outputs": [{"links": [1014, 1037], "name": "", "type": "VAE"}], "pos": [5330.31640625, -314.3552551269531], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 587, "flags": {}, "inputs": [{"link": 1112, "name": "", "type": "*"}], "mode": 0, "order": 111, "outputs": [{"links": [1048, 1053], "name": "", "type": "IMAGE"}], "pos": [5903.5830078125, -1092.927978515625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 588, "flags": {}, "inputs": [], "mode": 0, "order": 22, "outputs": [{"links": [1078], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [1052, 1073], "name": "CLIP", "slot_index": 1, "type": "CLIP"}, {"links": [1059, 1066], "name": "VAE", "slot_index": 2, "type": "VAE"}], "pos": [5248.390625, -1644.19873046875], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [314.34735107421875, 128.43458557128906], "type": "CheckpointLoaderSimple", "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 589, "flags": {}, "inputs": [{"link": 1048, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 112, "outputs": [{"links": [1049], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [5231.2734375, -1471.755615234375], "properties": {"Node name for S&R": "DetectFaces"}, "size": [210, 126], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 590, "flags": {}, "inputs": [{"link": 1049, "name": "faces", "type": "FACE"}], "mode": 0, "order": 113, "outputs": [{"links": [1050, 1054], "name": "filtered", "slot_index": 0, "type": "FACE"}, {"name": "rest", "type": "FACE"}], "pos": [5244.98828125, -1255.52685546875], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "size": [227.9144744873047, 169.93338012695312], "type": "OrderedFaceFilter", "widgets_values": ["area", "descending", 0, 1]}, {"id": 591, "flags": {}, "inputs": [{"link": 1050, "name": "faces", "type": "FACE"}], "mode": 0, "order": 114, "outputs": [{"links": [1051, 1069, 1072, 1074], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [1057], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [5544.72607421875, -1191.357421875], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 592, "flags": {}, "inputs": [{"link": 1118, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 1051, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 115, "outputs": [{"links": [], "name": "seg_img", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "depth_img", "slot_index": 1, "type": "IMAGE"}, {"links": [], "name": "normal_img", "slot_index": 2, "type": "IMAGE"}, {"links": [], "name": "pose_img", "slot_index": 3, "type": "IMAGE"}, {"links": [], "name": "mask", "slot_index": 4, "type": "MASK"}], "pos": [5238.166015625, -1037.31396484375], "properties": {"Node name for S&R": "SapiensSampler"}, "size": [265.8616027832031, 258], "type": "SapiensSampler", "widgets_values": ["2.<PERSON>_<PERSON>", "23,24,25,26,27", false, 255, 255, 255]}, {"id": 593, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 23, "outputs": [{"label": "CONTROL_NET", "links": [1077], "name": "CONTROL_NET", "shape": 3, "slot_index": 0, "type": "CONTROL_NET"}], "pos": [5883.46044921875, -1502.615234375], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [378.708740234375, 58], "type": "ControlNetLoader", "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 594, "bgcolor": "#353", "color": "#232", "flags": {"collapsed": false}, "inputs": [{"link": 1052, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 45, "outputs": [{"links": [1080], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [5541.3818359375, -1434.293701171875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [239.4051971435547, 91.89370727539062], "type": "CLIPTextEncode", "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"]}, {"id": 595, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 24, "outputs": [{"label": "INSTANTID", "links": [1075], "name": "INSTANTID", "shape": 3, "slot_index": 0, "type": "INSTANTID"}], "pos": [5847.34619140625, -1611.684326171875], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDModelLoader", "widgets_values": ["ip-adapter.bin"]}, {"id": 596, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 25, "outputs": [{"label": "FACEANALYSIS", "links": [1076], "name": "FACEANALYSIS", "shape": 3, "slot_index": 0, "type": "FACEANALYSIS"}], "pos": [5859.9208984375, -1551.356689453125], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDFaceAnalysis", "widgets_values": ["CUDA"]}, {"id": 597, "flags": {}, "inputs": [{"link": 1053, "name": "images", "type": "IMAGE"}, {"link": 1054, "name": "face", "type": "FACE"}, {"link": 1055, "name": "crop", "type": "IMAGE"}, {"link": 1056, "name": "mask", "type": "MASK"}, {"link": 1057, "name": "warp", "type": "WARP"}], "mode": 0, "order": 130, "outputs": [{"links": [1138], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7109.482421875, 27.729785919189453], "properties": {"Node name for S&R": "WarpFacesBack"}, "size": [182.46627807617188, 157.38844299316406], "type": "WarpFacesBack", "widgets_values": []}, {"id": 598, "flags": {"collapsed": false}, "inputs": [{"link": 1058, "name": "samples", "type": "LATENT"}, {"link": 1059, "name": "vae", "type": "VAE"}], "mode": 0, "order": 123, "outputs": [{"links": [1135], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [6902.4267578125, -1117.7196044921875], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 599, "flags": {}, "inputs": [{"link": 1060, "name": "model", "type": "MODEL"}, {"link": 1061, "name": "positive", "type": "CONDITIONING"}, {"link": 1062, "name": "negative", "type": "CONDITIONING"}, {"link": 1063, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 122, "outputs": [{"links": [1058], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [6809.79248046875, -1451.95947265625], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [261.8017578125, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": [42029499340256, "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.5]}, {"id": 600, "flags": {}, "inputs": [{"link": 1064, "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"link": 1065, "name": "negative", "type": "CONDITIONING"}, {"link": 1066, "name": "vae", "type": "VAE"}, {"link": 1067, "name": "pixels", "type": "IMAGE"}, {"link": 1068, "name": "mask", "type": "MASK"}], "mode": 0, "order": 120, "outputs": [{"links": [1061], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"links": [1062], "name": "negative", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"links": [1063], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [6536.25048828125, -1435.2982177734375], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 601, "flags": {"collapsed": false}, "inputs": [{"link": 1069, "name": "image_ref", "type": "IMAGE"}, {"link": 1137, "name": "image_target", "type": "IMAGE"}], "mode": 0, "order": 129, "outputs": [{"links": [1055], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [6831.185546875, 78.27633666992188], "properties": {"Node name for S&R": "easy imageColorMatch"}, "size": [210, 102], "type": "easy imageColorMatch", "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 602, "flags": {"collapsed": false}, "inputs": [{"link": 1071, "name": "mask", "type": "MASK"}], "mode": 0, "order": 121, "outputs": [{"links": [1056], "name": "mask", "shape": 3, "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "shape": 3, "type": "MASK"}], "pos": [6449.83984375, -1199.398681640625], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "size": [236.14947509765625, 246], "type": "ConrainGrowMaskWithBlur", "widgets_values": [40, 0, false, false, 0, 1, 1, false]}, {"id": 603, "flags": {}, "inputs": [{"link": 1072, "name": "", "type": "*"}], "mode": 0, "order": 116, "outputs": [{"links": [1067, 1081], "name": "", "type": "IMAGE"}], "pos": [6134.86328125, -1466.5614013671875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 604, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 1073, "name": "clip", "type": "CLIP"}, {"link": 1103, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 72, "outputs": [{"links": [1079], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [5641.8115234375, -1612.7529296875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 96], "type": "CLIPTextEncode", "widgets_values": ["a 5 year girl"]}, {"id": 605, "flags": {"collapsed": false}, "inputs": [{"link": 1074, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 117, "outputs": [{"links": [], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"links": [1082], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "slot_index": 2, "type": "BBOX"}], "pos": [5839.80078125, -1194.588134765625], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "size": [300, 500], "type": "easy humanSegmentation", "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 606, "flags": {}, "inputs": [{"label": "instantid", "link": 1075, "name": "instantid", "slot_index": 0, "type": "INSTANTID"}, {"label": "insightface", "link": 1076, "name": "insightface", "slot_index": 1, "type": "FACEANALYSIS"}, {"label": "control_net", "link": 1077, "name": "control_net", "slot_index": 2, "type": "CONTROL_NET"}, {"label": "image", "link": 1098, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 1078, "name": "model", "slot_index": 4, "type": "MODEL"}, {"label": "positive", "link": 1079, "name": "positive", "slot_index": 5, "type": "CONDITIONING"}, {"label": "negative", "link": 1080, "name": "negative", "slot_index": 6, "type": "CONDITIONING"}, {"label": "image_kps", "link": 1081, "name": "image_kps", "shape": 7, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 118, "outputs": [{"label": "MODEL", "links": [1060], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "positive", "links": [1064], "name": "positive", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"label": "negative", "links": [1065], "name": "negative", "shape": 3, "slot_index": 2, "type": "CONDITIONING"}], "pos": [6285.20849609375, -1638.934814453125], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [210, 266], "type": "ApplyInstantID", "widgets_values": [1, 0, 1]}, {"id": 607, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1082, "name": "mask", "type": "MASK"}], "mode": 0, "order": 119, "outputs": [{"links": [1068, 1071, 1136], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [6203.44091796875, -1267.2886962890625], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 5, 4, 0, 1, true]}, {"id": 608, "flags": {}, "inputs": [{"link": 1128, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 66, "outputs": [{"links": [1093, 1095], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [4136.724609375, -1192.1729736328125], "properties": {"Node name for S&R": "ImageResize+"}, "size": [261.8576965332031, 224.5092315673828], "type": "ImageResize+", "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 609, "flags": {}, "inputs": [], "mode": 0, "order": 26, "outputs": [{"label": "BBOX_DETECTOR", "links": [1087], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": [3768.260986328125, -982.2147827148438], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [315, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 610, "flags": {}, "inputs": [{"link": 1087, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"link": 1095, "name": "image", "type": "IMAGE"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}], "mode": 0, "order": 73, "outputs": [{"links": [1092], "name": "SEGS", "slot_index": 0, "type": "SEGS"}], "pos": [4138.52880859375, -1613.1448974609375], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "size": [277.6************, 319.97015380859375], "type": "ImpactSimpleDetectorSEGS", "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 611, "flags": {}, "inputs": [{"link": 1092, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 77, "outputs": [{"links": [1088], "name": "filtered_SEGS", "slot_index": 0, "type": "SEGS"}, {"links": [], "name": "remained_SEGS", "slot_index": 1, "type": "SEGS"}], "pos": [4463.92041015625, -1600.606201171875], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "size": [210, 158.96408081054688], "type": "ImpactSEGSOrderedFilter", "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 612, "flags": {"collapsed": true}, "inputs": [{"link": 1088, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 83, "outputs": [{"links": [1089], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4633.7421875, -1696.2783203125], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "size": [289.79998779296875, 26], "type": "SegsToCombinedMask", "widgets_values": []}, {"id": 613, "flags": {}, "inputs": [{"link": 1089, "name": "mask", "type": "MASK"}], "mode": 0, "order": 87, "outputs": [{"links": [1090], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4735.19970703125, -1603.572265625], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 614, "flags": {}, "inputs": [{"link": 1093, "name": "image", "type": "IMAGE"}, {"link": 1090, "name": "mask", "type": "MASK"}], "mode": 0, "order": 91, "outputs": [{"links": [1091], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [4466.734375, -1308.2440185546875], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [235.1999969482422, 150.37045288085938], "type": "Bounded Image Crop with Mask", "widgets_values": [64, 64, 64, 64]}, {"id": 615, "flags": {}, "inputs": [{"link": 1091, "name": "images", "type": "IMAGE"}], "mode": 0, "order": 94, "outputs": [{"links": [1094], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4746.7099609375, -1312.882080078125], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "size": [222.84095764160156, 26], "type": "easy imageListToImageBatch", "widgets_values": []}, {"id": 616, "flags": {}, "inputs": [{"link": 1097, "name": "", "type": "*"}], "mode": 0, "order": 100, "outputs": [{"links": [1098], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [5087.3310546875, -1635.439453125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 617, "flags": {}, "inputs": [{"link": 1099, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 42, "outputs": [{"links": [1100], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2989.97412109375, -844.7568359375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 618, "flags": {}, "inputs": [{"link": 1100, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 56, "outputs": [{"links": [1102, 1104], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [4939.9091796875, -852.3460693359375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 619, "flags": {}, "inputs": [{"link": 1102, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 65, "outputs": [{"links": [1103], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [5110.6943359375, -1569.9432373046875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 624, "flags": {}, "inputs": [{"link": 1113, "name": "", "type": "*"}], "mode": 0, "order": 51, "outputs": [{"links": [1114], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [797.66748046875, 272.5708312988281], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 625, "flags": {}, "inputs": [{"link": 1114, "name": "", "type": "*"}], "mode": 0, "order": 60, "outputs": [{"links": [1115], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [2962.908935546875, 261.55731201171875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 626, "flags": {}, "inputs": [{"link": 1117, "name": "", "type": "*"}], "mode": 0, "order": 44, "outputs": [{"links": [1118], "name": "", "slot_index": 0, "type": "MODEL_SAPIEN"}], "pos": [4947.50048828125, -748.3097534179688], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 627, "flags": {}, "inputs": [{"link": 1120, "name": "", "type": "*"}], "mode": 0, "order": 132, "outputs": [{"links": [1121, 1122], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [7310.9521484375, -1583.37744140625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 629, "flags": {}, "inputs": [{"link": 1125, "name": "image1", "type": "IMAGE"}, {"link": 1126, "name": "image2", "type": "IMAGE"}, {"link": 1127, "name": "image3", "type": "IMAGE"}, {"name": "image4", "type": "IMAGE"}], "mode": 0, "order": 58, "outputs": [{"links": [1128], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [3809.640625, -1187.49951171875], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "size": [231.68582153320312, 104.573486328125], "type": "ImpactMakeImageList", "widgets_values": []}], "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "579": 0, "599": 0}, "version": 0.4}}}}