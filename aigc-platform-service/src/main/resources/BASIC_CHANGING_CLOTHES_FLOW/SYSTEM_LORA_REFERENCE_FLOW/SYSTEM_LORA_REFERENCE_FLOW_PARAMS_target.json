{"client_id": "ebd1ce05bfad42de8475d9c4f74b1701", "prompt": {"52": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "images": ["243", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "53": {"inputs": {"mask": ["52", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "54": {"inputs": {"mask": ["322", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "56": {"inputs": {"channel": "red", "image": ["324", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "60": {"inputs": {"image": ["243", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "61": {"inputs": {"image": ["764", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "159": {"inputs": {"text": "product/20250721/100386/547707", "text_b": "product_3275375", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存换装结果图片"}}, "168": {"inputs": {"padding_left": 0, "padding_right": 0, "padding_top": 0, "padding_bottom": 0, "image": ["397", 0], "mask": ["315", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "176": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]", "any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}}, "194": {"inputs": {"mask": ["315", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "211": {"inputs": {"channel": "red", "image": ["767", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "242": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["346", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "243": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["245", 0], "source": ["254", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "245": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "246": {"inputs": {"seed": ["774", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认宽"}}, "247": {"inputs": {"seed": ["775", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认高"}}, "249": {"inputs": {"value": "b/2-a/2", "a": ["320", 1], "b": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "250": {"inputs": {"value": "b/2-a/2", "a": ["320", 0], "b": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "253": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]", "any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}}, "254": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["242", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "256": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]", "any_a": ["264", 0], "any_b": ["265", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "concat的图片大小"}}, "258": {"inputs": {"a": ["314", 0], "b": ["246", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum宽"}}, "259": {"inputs": {"a": ["314", 1], "b": ["247", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum高"}}, "264": {"inputs": {"a": ["316", 0], "b": ["258", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum宽"}}, "265": {"inputs": {"a": ["316", 1], "b": ["259", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum高"}}, "275": {"inputs": {"seed": 661045452417164, "steps": 20, "cfg": 2.5, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["283", 0], "positive": ["282", 0], "negative": ["282", 1], "latent_image": ["436", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "276": {"inputs": {"samples": ["275", 0], "vae": ["280", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "277": {"inputs": {"text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. ", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "278": {"inputs": {"guidance": 30, "conditioning": ["277", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"unet_name": "flux-fill-tryon-20250308.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "280": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "281": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "282": {"inputs": {"noise_mask": true, "positive": ["286", 0], "negative": ["290", 0], "vae": ["280", 0], "pixels": ["289", 1], "mask": ["289", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "283": {"inputs": {"model": ["293", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "284": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "285": {"inputs": {"crop": "center", "clip_vision": ["284", 0], "image": ["243", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "286": {"inputs": {"strength": 1, "strength_type": "multiply", "conditioning": ["278", 0], "style_model": ["287", 0], "clip_vision_output": ["285", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "287": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "288": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["793", 0], "inpainted_image": ["794", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "✂️ Inpaint <PERSON>itch"}}, "289": {"inputs": {"context_expand_pixels": 10, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 0, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 1536, "max_height": 1785, "padding": 32, "image": ["321", 0], "mask": ["56", 0], "optional_context_mask": ["297", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "✂️ Inpaint Crop"}}, "290": {"inputs": {"conditioning": ["847", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "291": {"inputs": {"width": ["61", 0], "height": ["61", 1], "x": ["60", 0], "y": 0, "image": ["288", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "293": {"inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "strength_model": 1, "model": ["279", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "297": {"inputs": {"channel": "red", "image": ["323", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "300": {"inputs": {"mask": ["351", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "306": {"inputs": {"blend_factor": 1, "feathering": 5, "target": ["397", 0], "target_bounds": ["176", 0], "source": ["291", 0]}, "class_type": "Bounded_Image_Blend_LR", "_meta": {"title": "Bounded Image Blend LR"}}, "308": {"inputs": {"output_path": ["159", 0], "filename_prefix": ["159", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["306", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "314": {"inputs": {"image": ["397", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "315": {"inputs": {"expand": 10, "tapered_corners": false, "mask": ["812", 0]}, "class_type": "GrowMask", "_meta": {"title": "替换区域扩张大小"}}, "316": {"inputs": {"image": ["168", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "319": {"inputs": {"image": ["242", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "320": {"inputs": {"image": ["254", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "321": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["243", 0], "image2": ["764", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "322": {"inputs": {"expand": 0, "tapered_corners": true, "mask": ["211", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "323": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["385", 0], "image2": ["53", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "324": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["53", 0], "image2": ["54", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "346": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["408", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "347": {"inputs": {"value": "a/2+b", "a": ["264", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "348": {"inputs": {"value": "a/2+b", "a": ["265", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "349": {"inputs": {"mask": ["461", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "350": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["349", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "351": {"inputs": {"channel": "red", "image": ["350", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "362": {"inputs": {"value": "a*2*2", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "365": {"inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "Conrain_SAMModelLoader", "_meta": {"title": "Conrain SAMModelLoader"}}, "366": {"inputs": {"prompt": ["419", 0], "background": "white", "threshold": 0.3, "sam_model": ["365", 0], "grounding_dino_model": ["367", 0], "image": ["844", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "367": {"inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader", "_meta": {"title": "Conrain GroundingDinoModelLoader"}}, "368": {"inputs": {"text": "upper garment"}, "class_type": "CR Text", "_meta": {"title": "抠图词"}}, "369": {"inputs": {"prompt": ["820", 0], "background": "white", "threshold": 0.3, "sam_model": ["365", 0], "grounding_dino_model": ["367", 0], "image": ["844", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "384": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["392", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "385": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["386", 0], "source": ["384", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "386": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "392": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["300", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "397": {"inputs": {"upscale_method": "area", "scale_by": ["398", 0], "image": ["669", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "398": {"inputs": {"target_size": ["399", 0], "image": ["669", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "399": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "400": {"inputs": {"image": ["669", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "401": {"inputs": {"seed": 2100}, "class_type": "CR Seed", "_meta": {"title": "最大支持尺寸"}}, "404": {"inputs": {"image": ["462", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "405": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "406": {"inputs": {"target_size": ["405", 0], "image": ["462", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "408": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["462", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "418": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要替换的区域"}}, "419": {"inputs": {"text": ["418", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "433": {"inputs": {"text": "3"}, "class_type": "CR Text", "_meta": {"title": "tryon生成图片张数"}}, "434": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]", "any_a": ["433", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "435": {"inputs": {"text": ["434", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "436": {"inputs": {"multiply_by": ["435", 0], "latents": ["282", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}}, "456": {"inputs": {"image": "product/202507/100386/product_3275330_Woxil.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "459": {"inputs": {"width": ["404", 0], "height": ["404", 1], "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["456", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "460": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["459", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "461": {"inputs": {"channel": "red", "image": ["460", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "462": {"inputs": {"image": "product/202507/100386/6186e591e3a74608b0ce2711825b24e6.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "模特图"}}, "463": {"inputs": {"image": "product/202507/100386/product_2706401_1750906658_1932368_0_qaFBR.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "590": {"inputs": {"image": "202507/100634/9777975b9b19456c8fe046c223639b4b.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "导入人脸图片"}}, "591": {"inputs": {"text": "product/20250721/100386/547707", "text_b": "face_product_3275375", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存生成图片"}}, "592": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["594", 0], "text_b": ["621", 0], "text_c": ["593", 0], "text_d": ["625", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "593": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "594": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "595": {"inputs": {"swap_model": "inswapper_128.onnx", "facedetection_model": "retinaface_resnet50", "face_restore_model": "GFPGANv1.4.pth", "parse_model": "parsenet"}, "class_type": "LoadConrainReactorModels", "_meta": {"title": "导入换脸模型"}}, "596": {"inputs": {"enabled": true, "face_restore_visibility": "0.7", "codeformer_weight": "0.7", "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "keep_largest": "yes", "input_image": ["631", 0], "swap_model": ["595", 0], "facedetection": ["595", 1], "face_restore_model": ["595", 2], "faceparse_model": ["595", 3], "source_image": ["590", 0]}, "class_type": "ConrainReActorFaceSwap", "_meta": {"title": "换脸"}}, "597": {"inputs": {"output_path": ["591", 0], "filename_prefix": ["591", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["660", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "598": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["599", 0], "text_b": ["591", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "599": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "608": {"inputs": {"text": ["622", 0], "clip": ["630", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "609": {"inputs": {"samples": ["613", 0], "vae": ["610", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "610": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "611": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "612": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "613": {"inputs": {"noise": ["617", 0], "guider": ["616", 0], "sampler": ["614", 0], "sigmas": ["615", 0], "latent_image": ["619", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "614": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "615": {"inputs": {"scheduler": "beta", "steps": "20", "denoise": 1, "model": ["620", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "616": {"inputs": {"model": ["620", 0], "conditioning": ["618", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "617": {"inputs": {"noise_seed": 766055086762299}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "618": {"inputs": {"guidance": "3.5", "conditioning": ["870", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "619": {"inputs": {"width": ["657", 0], "height": ["659", 0], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "620": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["657", 0], "height": ["659", 0], "model": ["630", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "621": {"inputs": {"prompts": "Brown hooded jacket. The jacket has a waist-length cut and long sleeves with a loose fit. It features a beige ribbed hood lining and two front flap pockets. Includes a front opening with beige trim.front view, slight turn to the left,whole body,The model is wearing { mgs2222 dark navy wide-leg trousers} underneath. The model is wearing mgs2222 dark brown leather loafers. The  mgs2222 dark brown leather loafers are made of leather. \n\n\nmgs2222,an outdoor urban sidewalk with a paved stone ground, light gray exterior wall, glass window display partially visible on the right, and a cylindrical concrete column. The lighting is natural daylight, casting subtle, diffused shadows near the model's feet and to the right of the column.posing casually.Standing upright, the left arm is bent at the elbow with the left hand raised towards the forehead, as if shielding or adjusting hair. The right arm hangs naturally by the side. Both legs are straight, feet slightly apart, facing forward. The head is turned slightly to the left.\n\n\na mgm3004 porcelain skin with a rosy glow chinese female model,20-year-old,  long straight dark brown hair styled with a with a subtle fringe,", "seed": 2048}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "622": {"inputs": {"text": ["621", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "623": {"inputs": {"seed": 2100}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "624": {"inputs": {"seed": 1610}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "625": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "629": {"inputs": {"lora_name": "product/美甜-原像素_37070_20250709_195910/美甜-原像素_37070_20250709_195910-flux/美甜-原像素_37070_20250709_195910-flux.safetensors", "strength_model": 0.8, "strength_clip": "1", "model": ["612", 0], "clip": ["611", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "630": {"inputs": {"lora_name": "product/风格-少女-短款棉服街拍_5493_20250106_155346/风格-少女-短款棉服街拍_5493_20250106_155346-flux/风格-少女-短款棉服街拍_5493_20250106_155346-flux.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["629", 0], "clip": ["629", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "风格lora"}}, "631": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 516268210931281, "steps": 10, "cfg": 2.5, "sampler_name": "euler", "scheduler": "beta", "denoise": 0.55, "feather": 3, "noise_mask": true, "force_inpaint": false, "bbox_threshold": 0.5, "bbox_dilation": 2, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": 0, "noise_mask_feather": false, "image": ["609", 0], "detailer_pipe": ["632", 0]}, "class_type": "FaceDetailerPipe", "_meta": {"title": "FaceDetailer (pipe)"}}, "632": {"inputs": {"wildcard": "", "Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["633", 0], "bbox_detector": ["635", 0]}, "class_type": "BasicPipeToDetailerPipe", "_meta": {"title": "BasicPipe -> DetailerPipe"}}, "633": {"inputs": {"model": ["629", 0], "clip": ["630", 1], "vae": ["610", 0], "positive": ["634", 0], "negative": ["636", 0]}, "class_type": "ToBasicPipe", "_meta": {"title": "ToBasicPipe"}}, "634": {"inputs": {"text": ["651", 0], "clip": ["630", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "修脸prompt"}}, "635": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "636": {"inputs": {}, "class_type": "ImpactNegativeConditioningPlaceholder", "_meta": {"title": "Negative Cond Placeholder"}}, "644": {"inputs": {"text": ["592", 0], "path": ["598", 0], "filename": ["591", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "645": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "646": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "Prefer GPU"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "647": {"inputs": {"text": ["651", 0], "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["650", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "648": {"inputs": {"text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["650", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "649": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 512, "seed": 889757844059893, "steps": 8, "cfg": "3", "sampler_name": "euler", "scheduler": "normal", "denoise": 0.4, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 500, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "wildcard": "", "cycle": 1, "inpaint_model": 1, "noise_mask_feather": 0, "image": ["609", 0], "model": ["650", 0], "clip": ["650", 1], "vae": ["650", 2], "positive": ["647", 0], "negative": ["648", 0], "bbox_detector": ["645", 0], "sam_model_opt": ["646", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "650": {"inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "651": {"inputs": {"prompts": "a mgm3004 porcelain skin with a rosy glow chinese female model,20-year-old,  long straight dark brown hair styled with a with a subtle fringe,", "seed": 1858}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "654": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["624", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "655": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["623", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "656": {"inputs": {"any_a": ["654", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "657": {"inputs": {"text": ["656", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "658": {"inputs": {"any_a": ["655", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "659": {"inputs": {"text": ["658", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "660": {"inputs": {"width": ["624", 0], "height": ["623", 0], "x": 0, "y": 0, "image": ["596", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "666": {"inputs": {"text": "1"}, "class_type": "CR Text", "_meta": {"title": "是否使用风格lora生成图"}}, "667": {"inputs": {"comparison": "a == b", "a": ["666", 0], "b": ["668", 0]}, "class_type": "easy compare", "_meta": {"title": "Compare"}}, "668": {"inputs": {"text": "0"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "669": {"inputs": {"boolean": ["667", 0], "on_true": ["463", 0], "on_false": ["673", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "672": {"inputs": {"text": "4", "anything": ["436", 1]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "673": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n", "any_a": ["660", 0], "any_b": ["463", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "764": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["397", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "765": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "766": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "767": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["194", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "774": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "775": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "779": {"inputs": {"seed": 0}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}}, "793": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b", "any_a": ["289", 0], "any_b": ["435", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "794": {"inputs": {"image": ["276", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "800": {"inputs": {"model": "densepose_r50_fpn_dl.torchscript", "cmap": "<PERSON><PERSON><PERSON> (MagicAnimate)", "resolution": 512, "image": ["844", 0]}, "class_type": "DensePosePreprocessor", "_meta": {"title": "DensePose Estimator"}}, "801": {"inputs": {"color_list": ["803", 0], "threshold": 2, "image": ["800", 0]}, "class_type": "ConrainMaskFromColors", "_meta": {"title": "Conrain Mask From Colors"}}, "803": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "densepose提取mask区域"}}, "808": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的mask"}}, "810": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["801", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "812": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["366", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "814": {"inputs": {"height": ["815", 1], "width": ["815", 0], "interpolation_mode": "bilinear", "mask": ["810", 0]}, "class_type": "JWMaskResize", "_meta": {"title": "Mask Resize"}}, "815": {"inputs": {"image": ["366", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "818": {"inputs": {"width": ["815", 0], "height": ["815", 1], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "819": {"inputs": {"channel": "red", "image": ["818", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "820": {"inputs": {"text": ["808", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "821": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的区域"}}, "823": {"inputs": {"boolean": ["821", 0], "on_true": ["369", 1], "on_false": ["819", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "837": {"inputs": {"mask": ["812", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "839": {"inputs": {"text": "product/20250721/100386/547707", "text_b": "mask_product_3275375", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存参看图的mask"}}, "840": {"inputs": {"output_path": ["839", 0], "filename_prefix": ["839", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["837", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "844": {"inputs": {"torchscript_jit": "default", "image": ["397", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "847": {"inputs": {"text": "", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "850": {"inputs": {"text": ["651", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "860": {"inputs": {"type": "openpose", "control_net": ["862", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "861": {"inputs": {"type": "depth", "control_net": ["862", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "862": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "863": {"inputs": {"text": ["621", 0], "clip": ["630", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "865": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "866": {"inputs": {"image": ["872", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "867": {"inputs": {"text": "", "clip": ["630", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "868": {"inputs": {"ckpt_name": "depth_anything_vits14.pth", "resolution": 1024, "image": ["883", 0]}, "class_type": "DepthAnythingPreprocessor", "_meta": {"title": "Depth Anything"}}, "869": {"inputs": {"strength": 0.6, "start_percent": 0, "end_percent": 0.2, "positive": ["863", 0], "negative": ["867", 0], "control_net": ["860", 0], "vae": ["865", 0], "image": ["866", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "870": {"inputs": {"strength": 0.4, "start_percent": 0, "end_percent": 0.2, "positive": ["869", 0], "negative": ["869", 1], "control_net": ["861", 0], "vae": ["865", 0], "image": ["868", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "872": {"inputs": {"detect_hand": "enable", "detect_body": "enable", "detect_face": "enable", "resolution": 1024, "bbox_detector": "yolox_l.onnx", "pose_estimator": "dw-ll_ucoco_384.onnx", "image": ["883", 0]}, "class_type": "DWPreprocessor", "_meta": {"title": "DWPose Estimator"}}, "874": {"inputs": {"expression": "a[1][0]", "a": ["880", 0]}, "class_type": "MathExpressionPlus", "_meta": {"title": "Math Expression +"}}, "875": {"inputs": {"x": ["874", 0], "y": ["879", 0], "offset_x": 0, "offset_y": 0, "destination": ["884", 0], "source": ["882", 0]}, "class_type": "ImageComposite+", "_meta": {"title": "🔧 Image Composite"}}, "877": {"inputs": {"expression": "a[0][0]", "a": ["880", 0]}, "class_type": "MathExpressionPlus", "_meta": {"title": "Math Expression +"}}, "878": {"inputs": {"expression": "a[0][1]", "a": ["880", 0]}, "class_type": "MathExpressionPlus", "_meta": {"title": "Math Expression +"}}, "879": {"inputs": {"expression": "a[1][1]", "a": ["880", 0]}, "class_type": "MathExpressionPlus", "_meta": {"title": "Math Expression +"}}, "880": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    img_w, img_h = any_a, any_b\n    tgt_w, tgt_h = any_c, any_d\n\t\n    if tgt_w/tgt_h > img_w/img_h:\n        _img_w = int(img_h*tgt_w/tgt_h)\n        _img_h = img_h\n        coordinates = [(_img_w-img_w)//2, 0]\n    else:\n        _img_h = int(img_w*tgt_h/tgt_w)\n        _img_w = img_w\n        coordinates = [0, (_img_h-img_h)//2]\n\n    return ([_img_w, _img_h], coordinates),", "any_a": ["881", 0], "any_b": ["881", 1], "any_c": ["624", 0], "any_d": ["623", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "881": {"inputs": {"image": ["882", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "882": {"inputs": {"image": "product/202507/100386/product_2706401_1750906658_1932368_0_qaFBR.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "883": {"inputs": {"width": 1024, "height": 1024, "interpolation": "nearest", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 0, "image": ["875", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "884": {"inputs": {"width": ["877", 0], "height": ["878", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 884, "last_link_id": 1667, "nodes": [{"id": 52, "type": "APersonMaskGenerator", "pos": [-3768.537109375, 815.3216552734375], "size": [261.10693359375, 178], "flags": {}, "order": 232, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 820}], "outputs": [{"name": "masks", "type": "MASK", "links": [109], "slot_index": 0}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "type": "MaskToImage", "pos": [-2910.48828125, 882.318603515625], "size": [264.5999755859375, 26], "flags": {}, "order": 236, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [594, 596], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 54, "type": "MaskToImage", "pos": [-2914.48828125, 1064.301513671875], "size": [264.5999755859375, 26], "flags": {}, "order": 223, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [597], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 56, "type": "ImageToMask", "pos": [-2015, 901], "size": [210, 59.905555725097656], "flags": {}, "order": 242, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 598}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1434], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 60, "type": "Get Image Size", "pos": [-3979.537109375, 1269.284423828125], "size": [298.42425537109375, 46], "flags": {}, "order": 233, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 821}], "outputs": [{"name": "width", "type": "INT", "links": [522], "slot_index": 0}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 61, "type": "Get Image Size", "pos": [-3224, 2098], "size": [210, 46], "flags": {}, "order": 222, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 186}], "outputs": [{"name": "width", "type": "INT", "links": [520], "slot_index": 0}, {"name": "height", "type": "INT", "links": [521], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 113, "type": "Reroute", "pos": [-4104.23046875, 671.5852661132812], "size": [75, 26], "flags": {}, "order": 219, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 819}], "outputs": [{"name": "", "type": "IMAGE", "links": [186, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 159, "type": "Text String", "pos": [-2256, 2284], "size": [228.5572052001953, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [873], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [874], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存换装结果图片", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250721/100386/547707", "product_3275375", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 167, "type": "Reroute", "pos": [-4471.634765625, 789.323486328125], "size": [75, 26], "flags": {}, "order": 231, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 441}], "outputs": [{"name": "", "type": "IMAGE", "links": [820, 821, 822, 823], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 176, "type": "ConrainPythonExecutor", "pos": [-5825.6416015625, 656.2598876953125], "size": [423.4119567871094, 402.7642517089844], "flags": {"collapsed": true}, "order": 202, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 566, "shape": 7}, {"name": "any_b", "type": "*", "link": 567, "shape": 7}, {"name": "any_c", "type": "*", "link": 292, "shape": 7}, {"name": "any_d", "type": "*", "link": 451, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [544, 1423, 1424], "slot_index": 0}], "title": "以涂抹区域的为中心点画一个矩形框", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "type": "MaskToImage", "pos": [-5858.185546875, 987.3473510742188], "size": [264.5999755859375, 26], "flags": {}, "order": 184, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 570}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1429], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 208, "type": "Reroute", "pos": [-4375.634765625, 471.330810546875], "size": [75, 26], "flags": {}, "order": 215, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1428}], "outputs": [{"name": "", "type": "IMAGE", "links": [819], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 211, "type": "ImageToMask", "pos": [-5035.185546875, 1110.34619140625], "size": [210, 84.43663024902344], "flags": {}, "order": 216, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1432}], "outputs": [{"name": "MASK", "type": "MASK", "links": [591], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 242, "type": "Bounded Image Crop with Mask", "pos": [-6862, 1899.17578125], "size": [243.56057739257812, 150], "flags": {}, "order": 213, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 655}, {"name": "mask", "type": "MASK", "link": 1465}, {"name": "padding_left", "type": "INT", "link": 1456, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1457, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1458, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1459, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439, 580], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 243, "type": "ImageCompositeMasked", "pos": [-5039.65869140625, 1842.2310791015625], "size": [210, 138], "flags": {}, "order": 229, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 418, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 440, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 420, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 421, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 245, "type": "EmptyImage", "pos": [-5749.5673828125, 1642.17578125], "size": [243.3533935546875, 102], "flags": {}, "order": 205, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 757, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 759, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [418], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215], "color": "#222", "bgcolor": "#000"}, {"id": 246, "type": "CR Seed", "pos": [-7410.6416015625, 779.2598876953125], "size": [281.7162780761719, 102], "flags": {}, "order": 190, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1446, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [453], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认宽", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 247, "type": "CR Seed", "pos": [-7388.6416015625, 1075.259521484375], "size": [278.3121032714844, 102], "flags": {}, "order": 191, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1451, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [455], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认高", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 249, "type": "SimpleMath+", "pos": [-5389.65869140625, 1847.23095703125], "size": [220.35072326660156, 98], "flags": {}, "order": 228, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 585, "shape": 7}, {"name": "b", "type": "*", "link": 760, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [421, 770], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 250, "type": "SimpleMath+", "pos": [-5389.65869140625, 1648.231689453125], "size": [210, 112.43743896484375], "flags": {}, "order": 227, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 584, "shape": 7}, {"name": "b", "type": "*", "link": 758, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [420, 771], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 253, "type": "ConrainPythonExecutor", "pos": [-6450.5673828125, 1647.17578125], "size": [282.12066650390625, 195.71939086914062], "flags": {}, "order": 221, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 481, "shape": 7}, {"name": "any_b", "type": "*", "link": 478, "shape": 7}, {"name": "any_c", "type": "*", "link": 581, "shape": 7}, {"name": "any_d", "type": "*", "link": 582, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [486, 748], "slot_index": 0}], "title": "原logo比目标尺寸大时缩小到目标尺寸", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "type": "ImageScaleBy", "pos": [-6106.5673828125, 1903.17578125], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 224, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}, {"name": "scale_by", "type": "FLOAT", "link": 486, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [440, 583], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 256, "type": "ConrainPythonExecutor", "pos": [-6218.6416015625, 596.2598876953125], "size": [270.53582763671875, 200.77845764160156], "flags": {}, "order": 199, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 474, "shape": 7}, {"name": "any_b", "type": "*", "link": 477, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [451], "slot_index": 0}], "title": "concat的图片大小", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "type": "JWIntegerMin", "pos": [-6958.185546875, 694.3478393554688], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 192, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 564, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 453, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [473], "slot_index": 0}], "title": "Minimum宽", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 259, "type": "JWIntegerMin", "pos": [-6964.185546875, 1044.346923828125], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 193, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 565, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 455, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [476], "slot_index": 0}], "title": "Minimum高", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 264, "type": "JWIntegerMax", "pos": [-6605.185546875, 670.3475952148438], "size": [210, 71.68185424804688], "flags": {}, "order": 194, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 572, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 473, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [474, 481, 648, 675, 755, 1426, 1430], "slot_index": 0}], "title": "Maximum宽", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 265, "type": "JWIntegerMax", "pos": [-6647.185546875, 961.34765625], "size": [210, 71.68185424804688], "flags": {}, "order": 195, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 573, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 476, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [477, 478, 652, 756, 1427, 1431], "slot_index": 0}], "title": "Maximum高", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 268, "type": "Note", "pos": [-6955.6416015625, 830.2598876953125], "size": [210, 67.93143463134766], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模特图较小时，以模特图的大小为准"], "color": "#432", "bgcolor": "#653"}, {"id": 269, "type": "Note", "pos": [-6592.185546875, 828.347900390625], "size": [210, 67.93143463134766], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"], "color": "#432", "bgcolor": "#653"}, {"id": 275, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2921, 1656], "size": [234.29580688476562, 262], "flags": {}, "order": 248, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 493, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 494, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 495, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 850, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [497], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [661045452417164, "fixed", 20, 2.5, "euler", "simple", 1]}, {"id": 276, "type": "VAEDecode", "pos": [-2651, 1594], "size": [210, 46], "flags": {}, "order": 250, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 497, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 498, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1498], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 277, "type": "CLIPTextEncode", "pos": [-4136.73095703125, 1819.626220703125], "size": [269.3892822265625, 89.79380798339844], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 499, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [500], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "], "color": "#222", "bgcolor": "#000"}, {"id": 278, "type": "FluxGuidance", "pos": [-3830.************, 1833.23193359375], "size": [210, 58], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 500, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 279, "type": "UNETLoader", "pos": [-4035, 1583], "size": [326.5174865722656, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [516], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux-fill-tryon-20250308.safetensors", "default"]}, {"id": 280, "type": "VAELoader", "pos": [-4452.8623046875, 1858.************], "size": [300, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [498, 503], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 281, "type": "DualCLIPLoader", "pos": [-4479.83203125, 1664.2646484375], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [499, 1593], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 282, "type": "InpaintModelConditioning", "pos": [-3258, 1766], "size": [210, 138], "flags": {}, "order": 246, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 501, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 502, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 503, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 1437, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1438, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [494], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [495], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [848], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 283, "type": "DifferentialDiffusion", "pos": [-3256.462890625, 1645.7598876953125], "size": [184.8000030517578, 26], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [493], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 284, "type": "CLIPVisionLoader", "pos": [-4473.435546875, 2063.94091796875], "size": [370, 60], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [507], "slot_index": 0, "label": "CLIP视觉"}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "type": "CLIPVisionEncode", "pos": [-3917.685302734375, 1980.57470703125], "size": [210, 78], "flags": {}, "order": 234, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 507, "label": "CLIP视觉"}, {"name": "image", "type": "IMAGE", "link": 822, "label": "图像"}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [510], "slot_index": 0, "label": "CLIP视觉输出"}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 286, "type": "StyleModelApply", "pos": [-3552.980712890625, 1847.6009521484375], "size": [210, 122], "flags": {}, "order": 237, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 508, "label": "条件"}, {"name": "style_model", "type": "STYLE_MODEL", "link": 509, "label": "风格模型"}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 510, "shape": 7, "label": "CLIP视觉输出"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [501], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 287, "type": "StyleModelLoader", "pos": [-4462.771484375, 1981.87158203125], "size": [340, 60], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [509], "label": "风格模型"}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "type": "InpaintStitch", "pos": [-2232, 1585], "size": [256.60272216796875, 78], "flags": {}, "order": 252, "mode": 0, "inputs": [{"name": "stitch", "type": "STITCH", "link": 1497, "label": "接缝"}, {"name": "inpainted_image", "type": "IMAGE", "link": 1499, "label": "图像"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1440], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "InpaintStitch"}, "widgets_values": ["bislerp"]}, {"id": 289, "type": "InpaintCrop", "pos": [-3546, 2132], "size": [245.64613342285156, 386], "flags": {}, "order": 243, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1435, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1436, "label": "遮罩"}, {"name": "optional_context_mask", "type": "MASK", "link": 530, "shape": 7, "label": "上下文遮罩(可选)"}], "outputs": [{"name": "stitch", "type": "STITCH", "links": [1496], "slot_index": 0, "label": "接缝"}, {"name": "cropped_image", "type": "IMAGE", "links": [1437], "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [1438], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintCrop"}, "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1785, 32]}, {"id": 290, "type": "ConditioningZeroOut", "pos": [-3840.737548828125, 1724.7645263671875], "size": [317.4000244140625, 26], "flags": {"collapsed": false}, "order": 86, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1594, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [502], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 291, "type": "ImageCrop", "pos": [-2483, 1925], "size": [210, 118], "flags": {}, "order": 253, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1440}, {"name": "width", "type": "INT", "link": 520, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 521, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 522, "widget": {"name": "x"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [551], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 293, "type": "LoraLoaderModelOnly", "pos": [-3582, 1611], "size": [271.6474304199219, 86.10514068603516], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 516}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 297, "type": "ImageToMask", "pos": [-2061, 614], "size": [210, 59.905555725097656], "flags": {}, "order": 241, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 595}], "outputs": [{"name": "MASK", "type": "MASK", "links": [530], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 300, "type": "MaskToImage", "pos": [-6910, 2387.185546875], "size": [176.39999389648438, 26], "flags": {}, "order": 214, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1466}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [765], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 304, "type": "Reroute", "pos": [-2961.54296875, 2145.298828125], "size": [75, 26], "flags": {}, "order": 164, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 610}], "outputs": [{"name": "", "type": "IMAGE", "links": [547], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 305, "type": "Reroute", "pos": [-2614.54296875, 2141.298828125], "size": [75, 26], "flags": {}, "order": 207, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 544}], "outputs": [{"name": "", "type": "*", "links": [550], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 306, "type": "Bounded_Image_Blend_LR", "pos": [-2227, 2015], "size": [239.650634765625, 122], "flags": {}, "order": 254, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 547}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 550}, {"name": "source", "type": "IMAGE", "link": 551}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555, 879], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "widgets_values": [1, 5]}, {"id": 308, "type": "ConrainImageSave", "pos": [-1859, 2255], "size": [231.75296020507812, 266], "flags": {}, "order": 255, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 555, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 873, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 874, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 315, "type": "GrowMask", "pos": [-7805, 381], "size": [232.74205017089844, 84.21175384521484], "flags": {}, "order": 181, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1552}], "outputs": [{"name": "MASK", "type": "MASK", "links": [569, 570], "slot_index": 0}], "title": "替换区域扩张大小", "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [10, false], "color": "#232", "bgcolor": "#353"}, {"id": 319, "type": "GetImageSize+", "pos": [-6619.5673828125, 1719.17578125], "size": [144.6750030517578, 71.8825912475586], "flags": {}, "order": 217, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 580}], "outputs": [{"name": "width", "type": "INT", "links": [581], "slot_index": 0}, {"name": "height", "type": "INT", "links": [582], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 320, "type": "GetImageSize+", "pos": [-5706.65869140625, 1981.8992919921875], "size": [214.20001220703125, 66], "flags": {}, "order": 226, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 583}], "outputs": [{"name": "width", "type": "INT", "links": [584], "slot_index": 0}, {"name": "height", "type": "INT", "links": [585], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 321, "type": "easy imageConcat", "pos": [-3834.8525390625, 403.8241271972656], "size": [315, 102], "flags": {}, "order": 235, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 823}, {"name": "image2", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [589, 1433], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 322, "type": "GrowMask", "pos": [-3365.26123046875, 981.890625], "size": [315, 82], "flags": {}, "order": 220, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 591}], "outputs": [{"name": "MASK", "type": "MASK", "links": [592], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, true]}, {"id": 323, "type": "easy imageConcat", "pos": [-2429.48828125, 610.3302001953125], "size": [315, 102], "flags": {}, "order": 239, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 773}, {"name": "image2", "type": "IMAGE", "link": 594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 324, "type": "easy imageConcat", "pos": [-2486, 925], "size": [315, 102], "flags": {"collapsed": false}, "order": 240, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 596}, {"name": "image2", "type": "IMAGE", "link": 597}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [598], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 328, "type": "Reroute", "pos": [-8748.2783203125, 435.44891357421875], "size": [75, 26], "flags": {}, "order": 162, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 807}], "outputs": [{"name": "", "type": "IMAGE", "links": [606, 608, 610, 1425, 1585, 1586], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "Reroute", "pos": [-7862.5673828125, 1620.17578125], "size": [75, 26], "flags": {}, "order": 124, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1510}], "outputs": [{"name": "", "type": "MASK", "links": [647, 657, 670], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Bounded Image Crop with Mask", "pos": [-7434, 1899.17578125], "size": [243.56057739257812, 150], "flags": {}, "order": 203, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1444}, {"name": "mask", "type": "MASK", "link": 647}, {"name": "padding_left", "type": "INT", "link": 651, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 650, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 653, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 654, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [655], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 347, "type": "SimpleMath+", "pos": [-7797, 1927.17578125], "size": [210, 98], "flags": {"collapsed": false}, "order": 196, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 648, "shape": 7}, {"name": "b", "type": "*", "link": 1468, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [650, 651, 659, 660], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 348, "type": "SimpleMath+", "pos": [-7785.5673828125, 2150.185546875], "size": [210, 98], "flags": {"collapsed": false}, "order": 200, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 652, "shape": 7}, {"name": "b", "type": "*", "link": 1469, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [653, 654, 661, 662], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 349, "type": "MaskToImage", "pos": [-7774, 2329.185546875], "size": [176.39999389648438, 26], "flags": {}, "order": 126, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 670}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [658], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 350, "type": "Bounded Image Crop with Mask", "pos": [-7423, 2207.185546875], "size": [243.56057739257812, 150], "flags": {}, "order": 204, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 658}, {"name": "mask", "type": "MASK", "link": 657}, {"name": "padding_left", "type": "INT", "link": 659, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 660, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 661, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 662, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 351, "type": "ImageToMask", "pos": [-7171.5673828125, 2274.185546875], "size": [210, 83.63514709472656], "flags": {}, "order": 210, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 663}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1465, 1466, 1467], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 362, "type": "SimpleMath+", "pos": [-4408.435546875, 2163.94091796875], "size": [210, 98], "flags": {"collapsed": false}, "order": 197, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 675, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2*2"]}, {"id": 365, "type": "Conrain_SAMModelLoader", "pos": [-8832.4033203125, -166.285400390625], "size": [269.19927978515625, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [709, 712], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 366, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-8237.40234375, -448.2854919433594], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 168, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 709, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 710, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1589, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 839, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1528], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1522], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "Conrain_GroundingDinoModelLoader", "pos": [-8843.6650390625, -305.6015625], "size": [285.80181884765625, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [710, 713], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 368, "type": "CR Text", "pos": [-9829.40234375, -398.2855529785156], "size": [217.36741638183594, 128.27645874023438], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [834, 1513, 1536, 1545], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "抠图词", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["upper garment"], "color": "#232", "bgcolor": "#353"}, {"id": 369, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-7178.40771484375, -141.28543090820312], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 169, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 712, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 713, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1590, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 1544, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1548], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 384, "type": "ImageScaleBy", "pos": [-6080.5673828125, 2347.185546875], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 225, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 766}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 385, "type": "ImageCompositeMasked", "pos": [-5063.65869140625, 2265.23193359375], "size": [210, 138], "flags": {}, "order": 230, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 749, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 768, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 771, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 770, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [773], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 386, "type": "EmptyImage", "pos": [-5709.65869140625, 2175.23193359375], "size": [243.3533935546875, 102], "flags": {}, "order": 206, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 761, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 762, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [749], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 0], "color": "#222", "bgcolor": "#000"}, {"id": 390, "type": "Reroute", "pos": [-6058.65869140625, 1615.2320556640625], "size": [75, 26], "flags": {}, "order": 198, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 755, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [757, 758, 761], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 391, "type": "Reroute", "pos": [-6041.65869140625, 1705.2315673828125], "size": [75, 26], "flags": {}, "order": 201, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 756, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [759, 760, 762], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 392, "type": "Bounded Image Crop with Mask", "pos": [-6673, 2271.185546875], "size": [243.56057739257812, 150], "flags": {}, "order": 218, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 765}, {"name": "mask", "type": "MASK", "link": 1467}, {"name": "padding_left", "type": "INT", "link": 1461, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1462, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1463, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1464, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [766], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 397, "type": "ImageScaleBy", "pos": [-9207, 439], "size": [217.8218994140625, 125.52959442138672], "flags": {}, "order": 161, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 806}, {"name": "scale_by", "type": "FLOAT", "link": 798, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 398, "type": "UpscaleSizeCalculator", "pos": [-9680, 398], "size": [220, 118], "flags": {}, "order": 160, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 805}, {"name": "target_size", "type": "INT", "link": 797, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [798], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 399, "type": "ConrainPythonExecutor", "pos": [-10235, 516], "size": [365.79345703125, 195.28152465820312], "flags": {}, "order": 159, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 800, "shape": 7}, {"name": "any_b", "type": "*", "link": 801, "shape": 7}, {"name": "any_c", "type": "*", "link": 802, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [797], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "type": "Get Image Size", "pos": [-10582, 556], "size": [210, 46], "flags": {}, "order": 158, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 804}], "outputs": [{"name": "width", "type": "INT", "links": [800], "slot_index": 0}, {"name": "height", "type": "INT", "links": [801], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 401, "type": "CR Seed", "pos": [-11021, 844], "size": [270.7088317871094, 109.29169464111328], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [802, 811], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "最大支持尺寸", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [2100, "fixed"]}, {"id": 402, "type": "Reroute", "pos": [-10955, 416], "size": [75, 26], "flags": {}, "order": 157, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1259}], "outputs": [{"name": "", "type": "*", "links": [804, 805, 806], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 404, "type": "Get Image Size", "pos": [-11018, 1175], "size": [210, 46], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 827}], "outputs": [{"name": "width", "type": "INT", "links": [809, 895], "slot_index": 0}, {"name": "height", "type": "INT", "links": [810, 896], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 405, "type": "ConrainPythonExecutor", "pos": [-10711.865234375, 1396.52978515625], "size": [353.50982666015625, 168.5362548828125], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 809, "shape": 7}, {"name": "any_b", "type": "*", "link": 810, "shape": 7}, {"name": "any_c", "type": "*", "link": 811, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "type": "UpscaleSizeCalculator", "pos": [-10250.865234375, 1398.52978515625], "size": [220, 102.22442626953125], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 828}, {"name": "target_size", "type": "INT", "link": 812, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [817, 897], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 407, "type": "Reroute", "pos": [-11098.865234375, 1623.52978515625], "size": [75, 26], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1257}], "outputs": [{"name": "", "type": "IMAGE", "links": [827, 828, 829, 1473], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageScaleBy", "pos": [-9744, 1571], "size": [210, 78], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 829}, {"name": "scale_by", "type": "FLOAT", "link": 817, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1615], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 418, "type": "ConrainPythonExecutor", "pos": [-9433.40234375, -255.2854766845703], "size": [296.9554443359375, 286.73455810546875], "flags": {"collapsed": true}, "order": 60, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 834, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [837], "slot_index": 0}], "title": "需要替换的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 419, "type": "ShowText|pysssss", "pos": [-9081.40234375, -354.2854919433594], "size": [210, 326], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 837, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [839], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["coat. shirt. jacket"], "coat. shirt. jacket", "clothing", "coat. shirt. jacket", "coat. shirt. jacket"]}, {"id": 433, "type": "CR Text", "pos": [-4494, 2341], "size": [211.76846313476562, 168.80604553222656], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [846], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "tryon生成图片张数", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["3"], "color": "#232", "bgcolor": "#353"}, {"id": 434, "type": "ConrainPythonExecutor", "pos": [-4173.435546875, 2273.9404296875], "size": [255.5079803466797, 218.5600128173828], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 846, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [847], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 435, "type": "JWStringToInteger", "pos": [-3869, 2279], "size": [210, 55.71077346801758], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 847, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [849, 1500], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 436, "type": "VHS_DuplicateLatents", "pos": [-3320, 1941], "size": [260.3999938964844, 58.512535095214844], "flags": {}, "order": 247, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 848}, {"name": "multiply_by", "type": "INT", "link": 849, "widget": {"name": "multiply_by"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [850], "slot_index": 0}, {"name": "count", "type": "INT", "links": [1262], "slot_index": 1}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 451, "type": "PreviewImage", "pos": [-1892, 1739], "size": [210, 246], "flags": {}, "order": 256, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 879}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 456, "type": "LoadImage", "pos": [-12923, 726], "size": [315, 314], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [880], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["product/202507/100386/product_3275330_Woxil.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 457, "type": "Reroute", "pos": [-11051.4404296875, 1035.841796875], "size": [75, 26], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 880}], "outputs": [{"name": "", "type": "IMAGE", "links": [885], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 459, "type": "ImageResize+", "pos": [-10249.865234375, 1021.5302734375], "size": [315, 218], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 885}, {"name": "width", "type": "INT", "link": 895, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 896, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [889], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "type": "ImageScaleBy", "pos": [-9742, 1040], "size": [214.26881408691406, 94.98396301269531], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 889}, {"name": "scale_by", "type": "FLOAT", "link": 897, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 461, "type": "ImageToMask", "pos": [-8799.865234375, 1392.52978515625], "size": [210, 74.41427612304688], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 890}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1510], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 462, "type": "LoadImage", "pos": [-12928, 1139], "size": [315, 314], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1257], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "title": "模特图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["product/202507/100386/6186e591e3a74608b0ce2711825b24e6.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 463, "type": "LoadImage", "pos": [-12917, 294], "size": [315, 314], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1567], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["product/202507/100386/product_2706401_1750906658_1932368_0_qaFBR.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 591, "type": "Text String", "pos": [-6490, -3020], "size": [315, 190], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1164, 1167], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1165, 1221], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存生成图片", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250721/100386/547707", "face_product_3275375", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 592, "type": "Text Concatenate", "pos": [-5440, -2860], "size": [315, 178], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 1153, "widget": {"name": "text_a"}, "shape": 7, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 1154, "widget": {"name": "text_b"}, "shape": 7, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 1155, "widget": {"name": "text_c"}, "shape": 7, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 1156, "widget": {"name": "text_d"}, "shape": 7, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1219], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 593, "type": "String to Text", "pos": [-6950, -2820], "size": [315, 58], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1155], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 594, "type": "String to Text", "pos": [-6930, -3010], "size": [315, 58], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1153], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 597, "type": "ConrainImageSave", "pos": [-4850, -3190], "size": [320, 266], "flags": {}, "order": 152, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1163, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1164, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1165, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 598, "type": "Text Concatenate", "pos": [-5990, -3170], "size": [250, 142], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 1166, "widget": {"name": "text_a"}, "shape": 7, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 1167, "widget": {"name": "text_b"}, "shape": 7, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "shape": 7, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "shape": 7, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1220], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 599, "type": "String to Text", "pos": [-6540, -3160], "size": [315, 58], "flags": {"collapsed": false}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1166], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 600, "type": "InspyrenetRembg", "pos": [-6550, -3530], "size": [230, 90], "flags": {}, "order": 143, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1168, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1172], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1218], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 601, "type": "CR Upscale Image", "pos": [-7040, -3660], "size": [315, 222], "flags": {}, "order": 141, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1169, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1168, 1173], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 602, "type": "Note", "pos": [-6120, -3850], "size": [260, 110], "flags": {}, "order": 20, "mode": 4, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 603, "type": "EmptyImage", "pos": [-6090, -3670], "size": [231.5089111328125, 120.12616729736328], "flags": {}, "order": 147, "mode": 4, "inputs": [{"name": "width", "type": "INT", "link": 1170, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 1171, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1217], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 604, "type": "ImageRGBA2RGB", "pos": [-6110, -3460], "size": [252, 26], "flags": {}, "order": 146, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1172, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1216], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 605, "type": "Image Size to Number", "pos": [-6540, -3760], "size": [229.20001220703125, 126], "flags": {}, "order": 144, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1173, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [1170], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [1171], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 606, "type": "ImageScaleBy", "pos": [-4780, -3780], "size": [228.9691162109375, 78], "flags": {}, "order": 150, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1174, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 1175, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1239], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}, {"id": 607, "type": "UpscaleSizeCalculator", "pos": [-5100, -3670], "size": [220, 118], "flags": {}, "order": 149, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1176, "label": "image"}, {"name": "target_size", "type": "INT", "link": 1177, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [1175], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 608, "type": "CLIPTextEncode", "pos": [-11377.796875, -3820.************], "size": [285.6000061035156, 54], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1178}, {"name": "text", "type": "STRING", "link": 1179, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 609, "type": "VAEDecode", "pos": [-10147.796875, -4010.************], "size": [210, 46], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1180}, {"name": "vae", "type": "VAE", "link": 1181}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1225, 1603, 1613], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 610, "type": "VAELoader", "pos": [-10507.796875, -3780.************], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1181, 1211], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 611, "type": "DualCLIPLoader", "pos": [-12107.796875, -4040.************], "size": [282.20135498046875, 106], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1575], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 612, "type": "UNETLoader", "pos": [-12117.796875, -4230.**********], "size": [271.2318420410156, 82], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1609], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 613, "type": "SamplerCustomAdvanced", "pos": [-10457.796875, -4140.**********], "size": [236.8000030517578, 112.51068878173828], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1182, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1183, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1184, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 1185, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1186, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1180], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 614, "type": "KSamplerSelect", "pos": [-10797.796875, -3870.************], "size": [210, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1184], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 615, "type": "BasicScheduler", "pos": [-10777.796875, -3660.************], "size": [210, 106], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1187, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [1185], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", "20", 1]}, {"id": 616, "type": "BasicGuider", "pos": [-10757.796875, -3990.************], "size": [161.1999969482422, 46], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1188, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1189, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1183], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 617, "type": "RandomNoise", "pos": [-10857.796875, -4190.**********], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1182], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [766055086762299, "decrement"], "color": "#232", "bgcolor": "#353"}, {"id": 618, "type": "FluxGuidance", "pos": [-11027.796875, -3950.************], "size": [211.60000610351562, 58], "flags": {}, "order": 132, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1662}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1189], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 619, "type": "EmptySD3LatentImage", "pos": [-11087.796875, -3700.************], "size": [210, 86.50716400146484], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1191, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1192, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1186], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", 1], "color": "#222", "bgcolor": "#000"}, {"id": 620, "type": "ModelSamplingFlux", "pos": [-11077.796875, -3520.************], "size": [210, 122], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1604, "slot_index": 0}, {"name": "width", "type": "INT", "link": 1194, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1195, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1187, 1188], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 621, "type": "ConrainRandomPrompts", "pos": [-11947.796875, -3770.************], "size": [426.6739196777344, 307.13214111328125], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1196, 1197, 1627], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["Brown hooded jacket. The jacket has a waist-length cut and long sleeves with a loose fit. It features a beige ribbed hood lining and two front flap pockets. Includes a front opening with beige trim.front view, slight turn to the left,whole body,The model is wearing { mgs2222 dark navy wide-leg trousers} underneath. The model is wearing mgs2222 dark brown leather loafers. The  mgs2222 dark brown leather loafers are made of leather. \n\n\nmgs2222,an outdoor urban sidewalk with a paved stone ground, light gray exterior wall, glass window display partially visible on the right, and a cylindrical concrete column. The lighting is natural daylight, casting subtle, diffused shadows near the model's feet and to the right of the column.posing casually.Standing upright, the left arm is bent at the elbow with the left hand raised towards the forehead, as if shielding or adjusting hair. The right arm hangs naturally by the side. Both legs are straight, feet slightly apart, facing forward. The head is turned slightly to the left.\n\n\na mgm3004 porcelain skin with a rosy glow chinese female model,20-year-old,  long straight dark brown hair styled with a with a subtle fringe,", 2048, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 622, "type": "ShowText|pysssss", "pos": [-11437.796875, -3870.************], "size": [256.63372802734375, 476], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1196, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1179], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["(linrun2111:1.3),front view,whole body,The model is wearing A white t-shirt. The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),"], "(linrun2111:1.3),front view,whole body,The model is wearing A white t-shirt. The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "$backTags$extTags$styleLoraConfig.posture$styleLoraConfig.lens$styleLoraConfig.style$FACE.tags$FACE.extInfo.expression$FACE.extInfo.hairstyle$pictureMattingPrompt", "(linrun2111:1.3),front view,whole body,The model is wearing A white t-shirt. The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "linrun2010,a model stands on a city street with buildings and power lines in the background.standing casually.standing with one hand relaxed by their side and the other slightly bent.front view,upper body,A 23 year old Asian male model, clean and handsome,The model sported short mid-part brown hair that was trendy and cool, A beige and black striped shirt. It has a polo collar and short sleeves. The shirt is hips length with a button-down front. Features a small chest graphic and solid fabric."]}, {"id": 623, "type": "CR Seed", "pos": [-11427.796875, -3130.************], "size": [243.4204864501953, 102], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1234, 1241, 1659], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [2100, "fixed"]}, {"id": 624, "type": "CR Seed", "pos": [-11447.796875, -3320.************], "size": [243.4204864501953, 102], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1233, 1240, 1658], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1610, "fixed"]}, {"id": 625, "type": "ConrainRandomPrompts", "pos": [-11957.796875, -3380.************], "size": [411.6590881347656, 124], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1198], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 626, "type": "Reroute", "pos": [-10237.796875, -3470.************], "size": [75, 26], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1197, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1154]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 627, "type": "Reroute", "pos": [-10277.796875, -3340.************], "size": [75, 26], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1198, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1156]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 630, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-11757.796875, -4010.************], "size": [512.595703125, 126], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1576}, {"name": "clip", "type": "CLIP", "link": 1577}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1199], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1200], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["product/风格-少女-短款棉服街拍_5493_20250106_155346/风格-少女-短款棉服街拍_5493_20250106_155346-flux/风格-少女-短款棉服街拍_5493_20250106_155346-flux.safetensors", 1, 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 631, "type": "FaceDetailerPipe", "pos": [-8720, -4210], "size": [346, 782], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1613}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 1206}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1157, 1611], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 516268210931281, "fixed", 10, 2.5, "euler", "beta", 0.55, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 632, "type": "BasicPipeToDetailerPipe", "pos": [-9040, -4200], "size": [262, 204.4281768798828], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 1207}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1208}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [1206], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 633, "type": "ToBasicPipe", "pos": [-9340, -4200], "size": [241.79998779296875, 106], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1620}, {"name": "clip", "type": "CLIP", "link": 1210}, {"name": "vae", "type": "VAE", "link": 1211}, {"name": "positive", "type": "CONDITIONING", "link": 1212}, {"name": "negative", "type": "CONDITIONING", "link": 1213}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [1207], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 634, "type": "CLIPTextEncode", "pos": [-9630, -4150], "size": [210, 116.85224914550781], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1214}, {"name": "text", "type": "STRING", "link": 1599, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1212], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A French model,clear face, blue eyes,happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression,blue eyes,"], "color": "#232", "bgcolor": "#353"}, {"id": 635, "type": "UltralyticsDetectorProvider", "pos": [-9360, -3590], "size": [226.8000030517578, 78], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1208], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 636, "type": "ImpactNegativeConditioningPlaceholder", "pos": [-9660, -3690], "size": [210, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1213], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 637, "type": "Note", "pos": [-9360, -4020], "size": [210, 91.33761596679688], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 639, "type": "Note", "pos": [-8150, -3140], "size": [210, 91.33761596679688], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 640, "type": "Note", "pos": [-7690, -3170], "size": [210, 91.33761596679688], "flags": {}, "order": 34, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 641, "type": "Note", "pos": [-7010, -3840], "size": [210, 91.33761596679688], "flags": {}, "order": 35, "mode": 4, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 642, "type": "ConrainImageCompositeMasked", "pos": [-5680, -3580], "size": [252, 146], "flags": {}, "order": 148, "mode": 4, "inputs": [{"name": "source", "type": "IMAGE", "link": 1216}, {"name": "destination", "type": "IMAGE", "link": 1217}, {"name": "mask", "type": "MASK", "link": 1218, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1174, 1176], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 643, "type": "Note", "pos": [-8160, -2710], "size": [210, 91.33761596679688], "flags": {}, "order": 36, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 644, "type": "ConrainTextSave", "pos": [-4830, -2810], "size": [315, 106], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1219, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 1220, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 1221, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 651, "type": "ConrainRandomPrompts", "pos": [-10317.796875, -3700.************], "size": [397.79266357421875, 181.14865112304688], "flags": {}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1599, 1600, 1602], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a mgm3004 porcelain skin with a rosy glow chinese female model,20-year-old,  long straight dark brown hair styled with a with a subtle fringe,", 1858, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 652, "type": "Note", "pos": [-8970, -3920], "size": [210, 91.33761596679688], "flags": {}, "order": 38, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"], "color": "#322", "bgcolor": "#533"}, {"id": 653, "type": "Note", "pos": [-9150, -2540], "size": [210, 91.33761596679688], "flags": {}, "order": 39, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"], "color": "#322", "bgcolor": "#533"}, {"id": 654, "type": "ConrainPythonExecutor", "pos": [-11097.796875, -3240.************], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 73, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1233, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1235], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 655, "type": "ConrainPythonExecutor", "pos": [-11077.796875, -3140.************], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 72, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1234, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1237], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 656, "type": "ConrainAnyToStrings", "pos": [-10867.796875, -3270.************], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1235}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1236], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 657, "type": "JWStringToInteger", "pos": [-10617.796875, -3270.************], "size": [210, 34], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1236, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [1191, 1194], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 658, "type": "ConrainAnyToStrings", "pos": [-10797.796875, -3140.************], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1237}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1238], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 659, "type": "JWStringToInteger", "pos": [-10557.796875, -3120.************], "size": [210, 34], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1238, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [1192, 1195], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 660, "type": "ImageCrop", "pos": [-5380, -3190], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 151, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1239}, {"name": "width", "type": "INT", "link": 1240, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1241, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1163, 1256, 1452], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 661, "type": "Image Size to Number", "pos": [-5660, -3820], "size": [229.20001220703125, 126], "flags": {}, "order": 142, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1242, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [1243], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [1244], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 662, "type": "JWIntegerMax", "pos": [-5370, -3740], "size": [210, 67.1211166381836], "flags": {}, "order": 145, "mode": 4, "inputs": [{"name": "a", "type": "INT", "link": 1243, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 1244, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [1177], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 665, "type": "Reroute", "pos": [-12392, 403], "size": [75, 26], "flags": {}, "order": 154, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1452}], "outputs": [{"name": "", "type": "IMAGE", "links": [1614], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 666, "type": "CR Text", "pos": [-12468, 580], "size": [210, 106.75990295410156], "flags": {}, "order": 40, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1248], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "是否使用风格lora生成图", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["1"], "color": "#232", "bgcolor": "#353"}, {"id": 667, "type": "easy compare", "pos": [-12026, 774], "size": [216.3546905517578, 95.56697845458984], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1248}, {"name": "b", "type": "*", "link": 1249}], "outputs": [{"name": "boolean", "type": "BOOLEAN", "links": [1250], "slot_index": 0}], "properties": {"Node name for S&R": "easy compare"}, "widgets_values": ["a == b"]}, {"id": 668, "type": "CR Text", "pos": [-12461, 845], "size": [210, 110.8138198852539], "flags": {}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1249], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["0"]}, {"id": 669, "type": "easy ifElse", "pos": [-11663, 339], "size": [210, 80.45691680908203], "flags": {}, "order": 156, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1563}, {"name": "on_false", "type": "*", "link": 1265}, {"name": "boolean", "type": "BOOLEAN", "link": 1250, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1259], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 670, "type": "PreviewImage", "pos": [-5090, -3080], "size": [210, 246], "flags": {}, "order": 153, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1256}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 672, "type": "easy showAnything", "pos": [-2964.4384765625, 2021.940673828125], "size": [210, 76], "flags": {}, "order": 249, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 1262, "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["4"]}, {"id": 673, "type": "ConrainPythonExecutor", "pos": [-12093, 371], "size": [327.3860778808594, 248.97320556640625], "flags": {"collapsed": false}, "order": 155, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1614, "shape": 7}, {"name": "any_b", "type": "*", "link": 1564, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1265], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"]}, {"id": 764, "type": "ImageCrop", "pos": [-5062.6416015625, 426.25982666015625], "size": [210, 114], "flags": {}, "order": 211, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1425}, {"name": "width", "type": "INT", "link": 1426, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1427, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1419, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1420, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1428], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 765, "type": "ConrainPythonExecutor", "pos": [-5436.6416015625, 726.2598876953125], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 209, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1424, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1419, 1421], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 766, "type": "ConrainPythonExecutor", "pos": [-5428.6416015625, 613.2598876953125], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 208, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1423, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1420, 1422], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 767, "type": "ImageCrop", "pos": [-5100.6416015625, 867.2598876953125], "size": [210, 114], "flags": {}, "order": 212, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1429}, {"name": "width", "type": "INT", "link": 1430, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1431, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1421, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1422, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1432], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 768, "type": "Reroute", "pos": [-3580.128173828125, 1965.8056640625], "size": [75, 26], "flags": {}, "order": 238, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1433}], "outputs": [{"name": "", "type": "IMAGE", "links": [1435], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 769, "type": "Reroute", "pos": [-3670, 2148], "size": [75, 26], "flags": {}, "order": 244, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1434}], "outputs": [{"name": "", "type": "MASK", "links": [1436], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 773, "type": "Reroute", "pos": [-8720, 1622], "size": [75, 26], "flags": {}, "order": 120, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1615}], "outputs": [{"name": "", "type": "IMAGE", "links": [1444], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 774, "type": "ConrainPythonExecutor", "pos": [-7860.6416015625, 773.2598876953125], "size": [400, 200], "flags": {}, "order": 188, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1449, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1446], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 775, "type": "ConrainPythonExecutor", "pos": [-7853.6416015625, 1077.259521484375], "size": [400, 200], "flags": {}, "order": 189, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1450, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1451], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 776, "type": "Reroute", "pos": [-7837.6416015625, 614.2598876953125], "size": [75, 26], "flags": {}, "order": 187, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1448}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1449, 1450], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 777, "type": "Note", "pos": [-9406.40234375, -212.285400390625], "size": [210, 111.45333862304688], "flags": {}, "order": 42, "mode": 0, "inputs": [], "outputs": [], "title": "抠图词", "properties": {}, "widgets_values": ["上装传入：coat. shirt. jacket\n下装：trousers\n套装：clothing\n"], "color": "#432", "bgcolor": "#653"}, {"id": 779, "type": "CR Seed", "pos": [-7522, 1666.17578125], "size": [315, 102], "flags": {}, "order": 43, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1455, 1460, 1468, 1469], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [0, "fixed"]}, {"id": 780, "type": "Reroute", "pos": [-7045, 1988.17578125], "size": [75, 26], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1455, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1456, 1457, 1458, 1459], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 781, "type": "Reroute", "pos": [-7010.5673828125, 2188.185546875], "size": [75, 26], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1460, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1461, 1462, 1463, 1464], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 784, "type": "Note", "pos": [-7697.6416015625, 520.2598266601562], "size": [210, 60], "flags": {}, "order": 44, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["扩散区域的大小，\n输入5,10,20,30"], "color": "#432", "bgcolor": "#653"}, {"id": 787, "type": "Reroute", "pos": [-8800.865234375, 1351.52978515625], "size": [75, 26], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1473}], "outputs": [{"name": "", "type": "IMAGE", "links": [], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 790, "type": "Note", "pos": [-6490, -2710], "size": [241.35919189453125, 58], "flags": {}, "order": 45, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["保存风格lora出图"], "color": "#432", "bgcolor": "#653"}, {"id": 793, "type": "ConrainPythonExecutor", "pos": [-3234, 2188], "size": [336.5685119628906, 190.69573974609375], "flags": {}, "order": 245, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1496, "shape": 7}, {"name": "any_b", "type": "*", "link": 1500, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1497], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"]}, {"id": 794, "type": "ImpactImageBatchToImageList", "pos": [-2561, 1732], "size": [227.2136688232422, 39.664772033691406], "flags": {}, "order": 251, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1498}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1499], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": []}, {"id": 800, "type": "DensePosePreprocessor", "pos": [-8613.943359375, -683.7633056640625], "size": [315, 106], "flags": {}, "order": 170, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1591}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1512], "slot_index": 0}], "properties": {"Node name for S&R": "DensePosePreprocessor"}, "widgets_values": ["densepose_r50_fpn_dl.torchscript", "<PERSON><PERSON><PERSON> (MagicAnimate)", 512]}, {"id": 801, "type": "ConrainMaskFromColors", "pos": [-8217.4580078125, -668.181640625], "size": [228.88619995117188, 124.55016326904297], "flags": {}, "order": 173, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1512}, {"name": "color_list", "type": "STRING", "link": 1514, "widget": {"name": "color_list"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1518], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainMaskFromColors"}, "widgets_values": ["128,128,128\n255,128,0", 2]}, {"id": 803, "type": "ConrainPythonExecutor", "pos": [-9155.013671875, -610.7435913085938], "size": [372.4948425292969, 392.6221008300781], "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1513, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1514], "slot_index": 0}], "title": "densepose提取mask区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"]}, {"id": 808, "type": "ConrainPythonExecutor", "pos": [-9426.40234375, -17.285396575927734], "size": [300.1857604980469, 284.6385192871094], "flags": {"collapsed": true}, "order": 62, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1536, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1543], "slot_index": 0}], "title": "需要排除的mask", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 810, "type": "GrowMask", "pos": [-7843.46240234375, -661.1817016601562], "size": [210, 95.39423370361328], "flags": {}, "order": 175, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1518}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1527], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 811, "type": "Masks Add", "pos": [-6810.0185546875, -508.7435302734375], "size": [210, 46], "flags": {}, "order": 179, "mode": 4, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1622}, {"name": "masks_b", "type": "MASK", "link": 1623}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1550], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Add"}, "widgets_values": []}, {"id": 812, "type": "GrowMask", "pos": [-7853.40771484375, -335.28546142578125], "size": [210, 93.73921966552734], "flags": {}, "order": 172, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1522}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1622], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 814, "type": "JWMaskResize", "pos": [-7547.40771484375, -645.2855834960938], "size": [259.6524353027344, 135.95858764648438], "flags": {}, "order": 177, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1527}, {"name": "height", "type": "INT", "link": 1534, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 1533, "widget": {"name": "width"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1623], "slot_index": 0}], "properties": {"Node name for S&R": "JWMaskResize"}, "widgets_values": [512, 512, "bilinear"]}, {"id": 815, "type": "GetImageSize+", "pos": [-7865.46240234375, -483.18170166015625], "size": [214.20001220703125, 66], "flags": {}, "order": 171, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1528}], "outputs": [{"name": "width", "type": "INT", "links": [1533, 1540], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1534, 1541], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 818, "type": "Image Blank", "pos": [-7199.40771484375, -660.2855834960938], "size": [315, 154], "flags": {}, "order": 174, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1540, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1541, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1542], "slot_index": 0}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 819, "type": "ImageToMask", "pos": [-6824.40771484375, -654.2855834960938], "size": [210, 68.49308776855469], "flags": {}, "order": 176, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1542}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1547], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 821, "type": "ConrainPythonExecutor", "pos": [-7197.40771484375, -444.2854309082031], "size": [304.83380126953125, 204.48739624023438], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1545, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1546], "slot_index": 0}], "title": "需要排除的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"]}, {"id": 822, "type": "Masks Subtract", "pos": [-6504.0185546875, -507.7434997558594], "size": [210, 46], "flags": {}, "order": 180, "mode": 4, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1550}, {"name": "masks_b", "type": "MASK", "link": 1549}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1552, 1572], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Subtract"}, "widgets_values": []}, {"id": 823, "type": "easy ifElse", "pos": [-6517.40771484375, -659.2855834960938], "size": [210, 82.75738525390625], "flags": {}, "order": 178, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1548}, {"name": "on_false", "type": "*", "link": 1547}, {"name": "boolean", "type": "BOOLEAN", "link": 1546, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1549], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 831, "type": "Reroute", "pos": [-12361, 269], "size": [75, 26], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1567}], "outputs": [{"name": "", "type": "IMAGE", "links": [1563, 1564], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 837, "type": "MaskToImage", "pos": [-6369.0185546875, -374.74365234375], "size": [188.457763671875, 48.499874114990234], "flags": {}, "order": 182, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1572}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1581], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 840, "type": "ConrainImageSave", "pos": [-6148.0185546875, -211.74365234375], "size": [231.75296020507812, 266], "flags": {}, "order": 185, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1581, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1579, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1580, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 841, "type": "Note", "pos": [-9351.4609375, -707.3843994140625], "size": [210, 60], "flags": {}, "order": 46, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["躯干\n73,31,112\n\n手臂\n38,173,129\n70,192,110\n170,220,50\n114,208,86\n53,183,121\n92,200,99\n197,224,33\n142,214,68\n\n腿\n44,113,142\n40,124,142\n31,154,138\n32,163,133"], "color": "#432", "bgcolor": "#653"}, {"id": 843, "type": "Reroute", "pos": [-8505.95703125, 499.120849609375], "size": [75, 26], "flags": {}, "order": 165, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1585}], "outputs": [{"name": "", "type": "IMAGE"}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 845, "type": "Reroute", "pos": [-7748.40576171875, 63.28346633911133], "size": [75, 26], "flags": {}, "order": 167, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1588}], "outputs": [{"name": "", "type": "IMAGE", "links": [1589, 1590, 1591], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 847, "type": "CLIPTextEncode", "pos": [-4108.36572265625, 1693.341796875], "size": [210, 93.48091125488281], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1593}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1594], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 850, "type": "ShowText|pysssss", "pos": [-9670, -3740], "size": [315, 76], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1602, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "$FACE.tags$FACE.extInfo.expression$FACE.extInfo.hairstyle"]}, {"id": 851, "type": "PreviewImage", "pos": [-10097.796875, -4270.**********], "size": [210, 246], "flags": {}, "order": 137, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1603}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 853, "type": "PreviewImage", "pos": [-8220, -4220], "size": [210, 246], "flags": {}, "order": 140, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1611}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 859, "type": "Reroute", "pos": [-6808.34814453125, -422.577880859375], "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*"}], "outputs": [{"name": "", "type": "*"}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 860, "type": "SetUnionControlNetType", "pos": [-9890, -5250], "size": [210, 58], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1624}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1633], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["openpose"]}, {"id": 861, "type": "SetUnionControlNetType", "pos": [-9900, -5110], "size": [210, 58], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1625}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1638], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["depth"]}, {"id": 862, "type": "ControlNetLoader", "pos": [-10660, -5390], "size": [577.4558715820312, 58.57819366455078], "flags": {}, "order": 48, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1624, 1625], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 863, "type": "CLIPTextEncode", "pos": [-9500, -4760], "size": [285.6000061035156, 57.11636734008789], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1626}, {"name": "text", "type": "STRING", "link": 1627, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1631]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 865, "type": "VAELoader", "pos": [-9960, -5390], "size": [315, 58], "flags": {}, "order": 49, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1634, 1639], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 866, "type": "GetImageSizeAndCount", "pos": [-9560, -4980], "size": [277.20001220703125, 86], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1628}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1635], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 867, "type": "CLIPTextEncode", "pos": [-9500, -4590], "size": [285.6000061035156, 76], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1629}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1632]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 868, "type": "DepthAnythingPreprocessor", "pos": [-9950, -4560], "size": [315, 82], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1663}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1640], "slot_index": 0}], "properties": {"Node name for S&R": "DepthAnythingPreprocessor"}, "widgets_values": ["depth_anything_vits14.pth", 1024]}, {"id": 869, "type": "ControlNetApplySD3", "pos": [-9230, -5330], "size": [315, 186], "flags": {}, "order": 130, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1631}, {"name": "negative", "type": "CONDITIONING", "link": 1632}, {"name": "control_net", "type": "CONTROL_NET", "link": 1633}, {"name": "vae", "type": "VAE", "link": 1634}, {"name": "image", "type": "IMAGE", "link": 1635}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1636], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [1637], "slot_index": 1}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.6, 0, 0.2]}, {"id": 870, "type": "ControlNetApplySD3", "pos": [-8760, -5080], "size": [315, 186], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1636}, {"name": "negative", "type": "CONDITIONING", "link": 1637}, {"name": "control_net", "type": "CONTROL_NET", "link": 1638}, {"name": "vae", "type": "VAE", "link": 1639}, {"name": "image", "type": "IMAGE", "link": 1640}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1662], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING"}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.4, 0, 0.2]}, {"id": 871, "type": "Reroute", "pos": [-10880, -5380], "size": [75, 26], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1641}], "outputs": [{"name": "", "type": "IMAGE", "links": [1648, 1660]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 872, "type": "DWPreprocessor", "pos": [-9960, -4940], "size": [315, 198], "flags": {}, "order": 128, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1664}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1628], "slot_index": 0}, {"name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "properties": {"Node name for S&R": "DWPreprocessor"}, "widgets_values": ["enable", "enable", "enable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384.onnx"]}, {"id": 874, "type": "MathExpressionPlus", "pos": [-11020, -4690], "size": [217.53543090820312, 98], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "a", "type": "INT,FLOAT", "link": 1646, "shape": 7}, {"name": "b", "type": "INT,FLOAT", "shape": 7}, {"name": "c", "type": "INT,FLOAT", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1649], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpressionPlus"}, "widgets_values": ["a[1][0]"]}, {"id": 875, "type": "ImageComposite+", "pos": [-10700, -4650], "size": [315, 170], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 1665}, {"name": "source", "type": "IMAGE", "link": 1648}, {"name": "mask", "type": "MASK", "shape": 7}, {"name": "x", "type": "INT", "link": 1649, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1650, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1661], "slot_index": 0}], "properties": {"Node name for S&R": "ImageComposite+"}, "widgets_values": [0, 0, 0, 0]}, {"id": 878, "type": "MathExpressionPlus", "pos": [-11010, -4820], "size": [217.53543090820312, 98], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "a", "type": "INT,FLOAT", "link": 1654, "shape": 7}, {"name": "b", "type": "INT,FLOAT", "shape": 7}, {"name": "c", "type": "INT,FLOAT", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1667], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpressionPlus"}, "widgets_values": ["a[0][1]"]}, {"id": 879, "type": "MathExpressionPlus", "pos": [-11020, -4540], "size": [217.53543090820312, 98], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "a", "type": "INT,FLOAT", "link": 1655, "shape": 7}, {"name": "b", "type": "INT,FLOAT", "shape": 7}, {"name": "c", "type": "INT,FLOAT", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1650], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpressionPlus"}, "widgets_values": ["a[1][1]"]}, {"id": 880, "type": "ConrainPythonExecutor", "pos": [-11470, -4690], "size": [400, 200], "flags": {"collapsed": false}, "order": 103, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1656, "shape": 7}, {"name": "any_b", "type": "*", "link": 1657, "shape": 7}, {"name": "any_c", "type": "*", "link": 1658, "shape": 7}, {"name": "any_d", "type": "*", "link": 1659, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1646, 1653, 1654, 1655], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    img_w, img_h = any_a, any_b\n    tgt_w, tgt_h = any_c, any_d\n\t\n    if tgt_w/tgt_h > img_w/img_h:\n        _img_w = int(img_h*tgt_w/tgt_h)\n        _img_h = img_h\n        coordinates = [(_img_w-img_w)//2, 0]\n    else:\n        _img_h = int(img_w*tgt_h/tgt_w)\n        _img_w = img_w\n        coordinates = [0, (_img_h-img_h)//2]\n\n    return ([_img_w, _img_h], coordinates),"]}, {"id": 881, "type": "GetImageSize+", "pos": [-11450, -4870], "size": [214.20001220703125, 66], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1660}], "outputs": [{"name": "width", "type": "INT", "links": [1656], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1657], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 882, "type": "LoadImage", "pos": [-11450, -5380], "size": [300.1754455566406, 314], "flags": {}, "order": 50, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1641], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["product/202507/100386/product_2706401_1750906658_1932368_0_qaFBR.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 883, "type": "ImageResize+", "pos": [-10330, -5000], "size": [315, 218], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1661}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1663, 1664], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1024, 1024, "nearest", "keep proportion", "downscale if bigger", 0]}, {"id": 877, "type": "MathExpressionPlus", "pos": [-11010, -4960], "size": [217.53543090820312, 98], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "a", "type": "INT,FLOAT", "link": 1653, "shape": 7}, {"name": "b", "type": "INT,FLOAT", "shape": 7}, {"name": "c", "type": "INT,FLOAT", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1666], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpressionPlus"}, "widgets_values": ["a[0][0]"]}, {"id": 884, "type": "EmptyImage", "pos": [-10717.1796875, -4920.31494140625], "size": [315, 130], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1666, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1667, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1665], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215]}, {"id": 314, "type": "Get Image Size", "pos": [-7221.40380859375, 327.5995788574219], "size": [210, 46], "flags": {}, "order": 163, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 608}], "outputs": [{"name": "width", "type": "INT", "links": [564, 566], "slot_index": 0}, {"name": "height", "type": "INT", "links": [565, 567], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 839, "type": "Text String", "pos": [-6698.2548828125, -258.5939636230469], "size": [300.3023681640625, 216.65859985351562], "flags": {}, "order": 51, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1579], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1580], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存参看图的mask", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250721/100386/547707", "mask_product_3275375", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 168, "type": "Bounded Image Crop with Mask", "pos": [-6783.6259765625, 303.************], "size": [248.14456176757812, 150], "flags": {}, "order": 183, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 606}, {"name": "mask", "type": "MASK", "link": 569}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [571], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [292, 1448], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 316, "type": "Get Image Size", "pos": [-6240.74169921875, 312.345947265625], "size": [210, 46], "flags": {}, "order": 186, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"name": "width", "type": "INT", "links": [572], "slot_index": 0}, {"name": "height", "type": "INT", "links": [573], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 844, "type": "InspyrenetRembg", "pos": [-8031.57275390625, 26.234153747558594], "size": [211.82809448242188, 78], "flags": {}, "order": 166, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1586}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1588], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"]}, {"id": 820, "type": "ShowText|pysssss", "pos": [-8468.4677734375, -514.1475830078125], "size": [210, 326], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1543, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1544], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["trousers"], "trousers", "clothing", "trousers", "trousers"]}, {"id": 596, "type": "ConrainReActorFaceSwap", "pos": [-7660, -2980], "size": [367.79998779296875, 370], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 1157, "label": "input_image"}, {"name": "swap_model", "type": "FACE_MODEL", "link": 1158, "label": "swap_model"}, {"name": "facedetection", "type": "FACE_MODEL", "link": 1159, "label": "facedetection"}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 1160, "label": "face_restore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 1161, "label": "faceparse_model"}, {"name": "source_image", "type": "IMAGE", "link": 1162, "shape": 7, "label": "source_image"}, {"name": "face_model", "type": "FACE_MODEL", "shape": 7, "label": "face_model"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1169, 1242], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "shape": 3, "label": "FACE_MODEL"}], "title": "换脸", "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, "0.7", "0.7", "no", "no", "0", "0", 1, "yes"], "color": "#494949", "bgcolor": "#353535"}, {"id": 595, "type": "LoadConrainReactorModels", "pos": [-8200, -2970], "size": [324.5391845703125, 190], "flags": {}, "order": 52, "mode": 0, "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [1158], "slot_index": 0, "shape": 3, "label": "faceswapper_model"}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [1159], "slot_index": 1, "shape": 3, "label": "facedetection_model"}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [1160], "slot_index": 2, "shape": 3, "label": "facerestore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [1161], "slot_index": 3, "shape": 3, "label": "faceparse_model"}], "title": "导入换脸模型", "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"], "color": "#494949", "bgcolor": "#353535"}, {"id": 590, "type": "LoadImage", "pos": [-8190, -2520], "size": [320, 314], "flags": {}, "order": 53, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1162], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "shape": 3, "label": "MASK"}], "title": "导入人脸图片", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["202507/100634/9777975b9b19456c8fe046c223639b4b.jpg", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 649, "type": "FaceDetailer", "pos": [-8880, -3180], "size": [350.5302734375, 902.3991088867188], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1225, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1226, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 1227, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 1228, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 1229, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1230, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1231, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 1232, "slot_index": 7, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "slot_index": 8, "shape": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7, "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, 889757844059893, "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 647, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-9640, -3000], "size": [389.95330810546875, 157.71157836914062], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1222, "label": "clip"}, {"name": "text", "type": "STRING", "link": 1600, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1229], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#232", "bgcolor": "#353"}, {"id": 650, "type": "CheckpointLoaderSimple", "pos": [-9650, -3190], "size": [315, 98], "flags": {}, "order": 54, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1226], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [1222, 1224, 1227], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [1228], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 648, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-9620, -2750], "size": [400, 200], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1224, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1230], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 645, "type": "UltralyticsDetectorProvider", "pos": [-9620, -2480], "size": [315, 78], "flags": {}, "order": 55, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1231], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 646, "type": "SAMLoader", "pos": [-9580, -2320], "size": [315, 82], "flags": {}, "order": 56, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1232], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 629, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-11757.796875, -4250.**********], "size": [508.2063293457031, 169.1739501953125], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1609}, {"name": "clip", "type": "CLIP", "link": 1575}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1576, 1620], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1577], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["product/美甜-原像素_37070_20250709_195910/美甜-原像素_37070_20250709_195910-flux/美甜-原像素_37070_20250709_195910-flux.safetensors", 0.8, "1"], "color": "#232", "bgcolor": "#353"}, {"id": 628, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-11208.8623046875, -4189.42138671875], "size": [255.225341796875, 126], "flags": {}, "order": 99, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 1199}, {"name": "clip", "type": "CLIP", "link": 1200}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1604], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1178, 1210, 1214, 1626, 1629], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/紫葡萄皮肤衣测试_7648_20250301_141220/紫葡萄皮肤衣测试_7648_20250301_141220-flux/紫葡萄皮肤衣测试_7648_20250301_141220-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}], "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [709, 365, 0, 366, 0, "SAM_MODEL"], [710, 367, 0, 366, 1, "GROUNDING_DINO_MODEL"], [712, 365, 0, 369, 0, "SAM_MODEL"], [713, 367, 0, 369, 1, "GROUNDING_DINO_MODEL"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [834, 368, 0, 418, 0, "*"], [837, 418, 0, 419, 0, "STRING"], [839, 419, 0, 366, 3, "STRING"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [1153, 594, 0, 592, 0, "STRING"], [1154, 626, 0, 592, 1, "STRING"], [1155, 593, 0, 592, 2, "STRING"], [1156, 627, 0, 592, 3, "STRING"], [1157, 631, 0, 596, 0, "IMAGE"], [1158, 595, 0, 596, 1, "FACE_MODEL"], [1159, 595, 1, 596, 2, "FACE_MODEL"], [1160, 595, 2, 596, 3, "FACE_MODEL"], [1161, 595, 3, 596, 4, "FACE_MODEL"], [1162, 590, 0, 596, 5, "IMAGE"], [1163, 660, 0, 597, 0, "IMAGE"], [1164, 591, 0, 597, 1, "STRING"], [1165, 591, 1, 597, 2, "STRING"], [1166, 599, 0, 598, 0, "STRING"], [1167, 591, 0, 598, 1, "STRING"], [1168, 601, 0, 600, 0, "IMAGE"], [1169, 596, 0, 601, 0, "IMAGE"], [1170, 605, 4, 603, 0, "INT"], [1171, 605, 5, 603, 1, "INT"], [1172, 600, 0, 604, 0, "IMAGE"], [1173, 601, 0, 605, 0, "IMAGE"], [1174, 642, 0, 606, 0, "IMAGE"], [1175, 607, 0, 606, 1, "FLOAT"], [1176, 642, 0, 607, 0, "IMAGE"], [1177, 662, 0, 607, 1, "INT"], [1178, 628, 1, 608, 0, "CLIP"], [1179, 622, 0, 608, 1, "STRING"], [1180, 613, 0, 609, 0, "LATENT"], [1181, 610, 0, 609, 1, "VAE"], [1182, 617, 0, 613, 0, "NOISE"], [1183, 616, 0, 613, 1, "GUIDER"], [1184, 614, 0, 613, 2, "SAMPLER"], [1185, 615, 0, 613, 3, "SIGMAS"], [1186, 619, 0, 613, 4, "LATENT"], [1187, 620, 0, 615, 0, "MODEL"], [1188, 620, 0, 616, 0, "MODEL"], [1189, 618, 0, 616, 1, "CONDITIONING"], [1191, 657, 0, 619, 0, "INT"], [1192, 659, 0, 619, 1, "INT"], [1194, 657, 0, 620, 1, "INT"], [1195, 659, 0, 620, 2, "INT"], [1196, 621, 0, 622, 0, "STRING"], [1197, 621, 0, 626, 0, "*"], [1198, 625, 0, 627, 0, "*"], [1199, 630, 0, 628, 0, "MODEL"], [1200, 630, 1, 628, 1, "CLIP"], [1206, 632, 0, 631, 1, "DETAILER_PIPE"], [1207, 633, 0, 632, 0, "BASIC_PIPE"], [1208, 635, 0, 632, 1, "BBOX_DETECTOR"], [1210, 628, 1, 633, 1, "CLIP"], [1211, 610, 0, 633, 2, "VAE"], [1212, 634, 0, 633, 3, "CONDITIONING"], [1213, 636, 0, 633, 4, "CONDITIONING"], [1214, 628, 1, 634, 0, "CLIP"], [1216, 604, 0, 642, 0, "IMAGE"], [1217, 603, 0, 642, 1, "IMAGE"], [1218, 600, 1, 642, 2, "MASK"], [1219, 592, 0, 644, 0, "STRING"], [1220, 598, 0, 644, 1, "STRING"], [1221, 591, 1, 644, 2, "STRING"], [1222, 650, 1, 647, 0, "CLIP"], [1224, 650, 1, 648, 0, "CLIP"], [1225, 609, 0, 649, 0, "IMAGE"], [1226, 650, 0, 649, 1, "MODEL"], [1227, 650, 1, 649, 2, "CLIP"], [1228, 650, 2, 649, 3, "VAE"], [1229, 647, 0, 649, 4, "CONDITIONING"], [1230, 648, 0, 649, 5, "CONDITIONING"], [1231, 645, 0, 649, 6, "BBOX_DETECTOR"], [1232, 646, 0, 649, 7, "SAM_MODEL"], [1233, 624, 0, 654, 0, "*"], [1234, 623, 0, 655, 0, "*"], [1235, 654, 0, 656, 0, "*"], [1236, 656, 0, 657, 0, "STRING"], [1237, 655, 0, 658, 0, "*"], [1238, 658, 0, 659, 0, "STRING"], [1239, 606, 0, 660, 0, "IMAGE"], [1240, 624, 0, 660, 1, "INT"], [1241, 623, 0, 660, 2, "INT"], [1242, 596, 0, 661, 0, "IMAGE"], [1243, 661, 4, 662, 0, "INT"], [1244, 661, 5, 662, 1, "INT"], [1248, 666, 0, 667, 0, "*"], [1249, 668, 0, 667, 1, "*"], [1250, 667, 0, 669, 2, "BOOLEAN"], [1256, 660, 0, 670, 0, "IMAGE"], [1257, 462, 0, 407, 0, "*"], [1259, 669, 0, 402, 0, "*"], [1262, 436, 1, 672, 0, "*"], [1265, 673, 0, 669, 1, "*"], [1419, 765, 0, 764, 3, "INT"], [1420, 766, 0, 764, 4, "INT"], [1421, 765, 0, 767, 3, "INT"], [1422, 766, 0, 767, 4, "INT"], [1423, 176, 0, 766, 0, "*"], [1424, 176, 0, 765, 0, "*"], [1425, 328, 0, 764, 0, "IMAGE"], [1426, 264, 0, 764, 1, "INT"], [1427, 265, 0, 764, 2, "INT"], [1428, 764, 0, 208, 0, "*"], [1429, 194, 0, 767, 0, "IMAGE"], [1430, 264, 0, 767, 1, "INT"], [1431, 265, 0, 767, 2, "INT"], [1432, 767, 0, 211, 0, "IMAGE"], [1433, 321, 0, 768, 0, "*"], [1434, 56, 0, 769, 0, "*"], [1435, 768, 0, 289, 0, "IMAGE"], [1436, 769, 0, 289, 1, "MASK"], [1437, 289, 1, 282, 3, "IMAGE"], [1438, 289, 2, 282, 4, "MASK"], [1440, 288, 0, 291, 0, "IMAGE"], [1444, 773, 0, 346, 0, "IMAGE"], [1446, 774, 0, 246, 0, "INT"], [1448, 168, 1, 776, 0, "*"], [1449, 776, 0, 774, 0, "*"], [1450, 776, 0, 775, 0, "*"], [1451, 775, 0, 247, 0, "INT"], [1452, 660, 0, 665, 0, "*"], [1455, 779, 0, 780, 0, "*"], [1456, 780, 0, 242, 2, "INT"], [1457, 780, 0, 242, 3, "INT"], [1458, 780, 0, 242, 4, "INT"], [1459, 780, 0, 242, 5, "INT"], [1460, 779, 0, 781, 0, "*"], [1461, 781, 0, 392, 2, "INT"], [1462, 781, 0, 392, 3, "INT"], [1463, 781, 0, 392, 4, "INT"], [1464, 781, 0, 392, 5, "INT"], [1465, 351, 0, 242, 1, "MASK"], [1466, 351, 0, 300, 0, "MASK"], [1467, 351, 0, 392, 1, "MASK"], [1468, 779, 0, 347, 1, "*"], [1469, 779, 0, 348, 1, "*"], [1473, 407, 0, 787, 0, "*"], [1496, 289, 0, 793, 0, "*"], [1497, 793, 0, 288, 0, "STITCH"], [1498, 276, 0, 794, 0, "IMAGE"], [1499, 794, 0, 288, 1, "IMAGE"], [1500, 435, 0, 793, 1, "*"], [1510, 461, 0, 331, 0, "*"], [1512, 800, 0, 801, 0, "IMAGE"], [1513, 368, 0, 803, 0, "*"], [1514, 803, 0, 801, 1, "STRING"], [1518, 801, 0, 810, 0, "MASK"], [1522, 366, 1, 812, 0, "MASK"], [1527, 810, 0, 814, 0, "MASK"], [1528, 366, 0, 815, 0, "IMAGE"], [1533, 815, 0, 814, 2, "INT"], [1534, 815, 1, 814, 1, "INT"], [1536, 368, 0, 808, 0, "*"], [1540, 815, 0, 818, 0, "INT"], [1541, 815, 1, 818, 1, "INT"], [1542, 818, 0, 819, 0, "IMAGE"], [1543, 808, 0, 820, 0, "STRING"], [1544, 820, 0, 369, 3, "STRING"], [1545, 368, 0, 821, 0, "*"], [1546, 821, 0, 823, 2, "BOOLEAN"], [1547, 819, 0, 823, 1, "*"], [1548, 369, 1, 823, 0, "*"], [1549, 823, 0, 822, 1, "MASK"], [1550, 811, 0, 822, 0, "MASK"], [1552, 822, 0, 315, 0, "MASK"], [1563, 831, 0, 669, 0, "*"], [1564, 831, 0, 673, 1, "*"], [1567, 463, 0, 831, 0, "*"], [1572, 822, 0, 837, 0, "MASK"], [1575, 611, 0, 629, 1, "CLIP"], [1576, 629, 0, 630, 0, "MODEL"], [1577, 629, 1, 630, 1, "CLIP"], [1579, 839, 0, 840, 1, "STRING"], [1580, 839, 1, 840, 2, "STRING"], [1581, 837, 0, 840, 0, "IMAGE"], [1585, 328, 0, 843, 0, "*"], [1586, 328, 0, 844, 0, "IMAGE"], [1588, 844, 0, 845, 0, "*"], [1589, 845, 0, 366, 2, "IMAGE"], [1590, 845, 0, 369, 2, "IMAGE"], [1591, 845, 0, 800, 0, "IMAGE"], [1593, 281, 0, 847, 0, "CLIP"], [1594, 847, 0, 290, 0, "CONDITIONING"], [1599, 651, 0, 634, 1, "STRING"], [1600, 651, 0, 647, 1, "STRING"], [1602, 651, 0, 850, 0, "STRING"], [1603, 609, 0, 851, 0, "IMAGE"], [1604, 628, 0, 620, 0, "MODEL"], [1609, 612, 0, 629, 0, "MODEL"], [1611, 631, 0, 853, 0, "IMAGE"], [1613, 609, 0, 631, 0, "IMAGE"], [1614, 665, 0, 673, 0, "*"], [1615, 408, 0, 773, 0, "*"], [1620, 629, 0, 633, 0, "MODEL"], [1622, 812, 0, 811, 0, "MASK"], [1623, 814, 0, 811, 1, "MASK"], [1624, 862, 0, 860, 0, "CONTROL_NET"], [1625, 862, 0, 861, 0, "CONTROL_NET"], [1626, 628, 1, 863, 0, "CLIP"], [1627, 621, 0, 863, 1, "STRING"], [1628, 872, 0, 866, 0, "IMAGE"], [1629, 628, 1, 867, 0, "CLIP"], [1631, 863, 0, 869, 0, "CONDITIONING"], [1632, 867, 0, 869, 1, "CONDITIONING"], [1633, 860, 0, 869, 2, "CONTROL_NET"], [1634, 865, 0, 869, 3, "VAE"], [1635, 866, 0, 869, 4, "IMAGE"], [1636, 869, 0, 870, 0, "CONDITIONING"], [1637, 869, 1, 870, 1, "CONDITIONING"], [1638, 861, 0, 870, 2, "CONTROL_NET"], [1639, 865, 0, 870, 3, "VAE"], [1640, 868, 0, 870, 4, "IMAGE"], [1641, 882, 0, 871, 0, "*"], [1646, 880, 0, 874, 0, "INT,FLOAT"], [1648, 871, 0, 875, 1, "IMAGE"], [1649, 874, 0, 875, 3, "INT"], [1650, 879, 0, 875, 4, "INT"], [1653, 880, 0, 877, 0, "INT,FLOAT"], [1654, 880, 0, 878, 0, "INT,FLOAT"], [1655, 880, 0, 879, 0, "INT,FLOAT"], [1656, 881, 0, 880, 0, "*"], [1657, 881, 1, 880, 1, "*"], [1658, 624, 0, 880, 2, "*"], [1659, 623, 0, 880, 3, "*"], [1660, 871, 0, 881, 0, "IMAGE"], [1661, 875, 0, 883, 0, "IMAGE"], [1662, 870, 0, 618, 0, "CONDITIONING"], [1663, 883, 0, 868, 0, "IMAGE"], [1664, 883, 0, 872, 0, "IMAGE"], [1665, 884, 0, 875, 0, "IMAGE"], [1666, 877, 0, 884, 0, "INT"], [1667, 878, 0, 884, 1, "INT"]], "groups": [{"id": 2, "title": "cat图片准备", "bounding": [-4589.86474609375, 176.38858032226562, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "处理服装图mask", "bounding": [-7916.77880859375, 1547.8116455078125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "处理参考图mask", "bounding": [-7896.23095703125, 199.59716796875, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "重绘", "bounding": [-4552.4736328125, 1515.76904296875, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "图片尺寸最大2048", "bounding": [-11184.8447265625, 188.96893310546875, 3120.570556640625, 2013.73291015625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "换脸", "bounding": [-8260, -3310, 1037.859130859375, 1197.2232666015625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 15, "title": "1.5修脸", "bounding": [-9690, -3310, 1347, 1200], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 16, "title": "换背景", "bounding": [-7120, -3960, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 17, "title": "修脸换脸", "bounding": [-9690, -4330, 1350, 956], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 18, "title": "出图", "bounding": [-12137.796875, -4320.**********, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 19, "title": "保存图片", "bounding": [-7120, -3290, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 26, "title": "Group", "bounding": [-12964.205078125, 204.96542358398438, 1616.3167724609375, 1283.9755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 27, "title": "处理模特图的mask", "bounding": [-9863.421875, -857.2464599609375, 3980.1259765625, 983.1404418945312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 28, "title": "controlnet", "bounding": [-11510, -5480, 3157.2744140625, 1028.9208984375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.30735605491428325, "offset": [8387.268533042414, 654.9470240119424]}}, "version": 0.4, "seed_widgets": {"275": 0, "401": 0, "617": 0, "621": 1, "623": 0, "624": 0, "625": 1, "631": 3, "649": 3, "651": 1, "779": 0}}}}}